class Emojis:
    
    # Basic Status Emojis
    success = '<:success:1379289220606070834>'
    error = '<:error:1379296270438371419>'
    warn = '<:warn:1379296346334433316>'
    cooldown = '<:cooldown:1379296257746538677>'
    loading = '<a:loading:1379296276356665364>'
    
    # Navigation Emojis
    left = '<:left:1380275840004132864>'
    right = '<:right:1379296316081045595>'
    cancel = '<:cancel:1379296264008634486>'
    choose = '<:choose:1379296334321942690>'
    
    # Voice Master Emojis (New Set)
    lock = '<:lock:1390690311726043349>'
    unlock = '<:unlock:1390690318873133072>'
    ghost = '<:ghost:1390690301370306683>'
    unghost = '<:unghost:1390690315375083582>'
    claim = '<:claim:1390690287101149385>'
    disconnect = '<:disconnect:1390690293053001830>'
    activity = '<:activity:1390690296953704498>'
    info = '<:info:1390690308353822843>'
    increase = '<:increase:1390690304776081482>'
    decrease = '<:decrease:1390690290402066583>'

    # Service Emojis
    google = '<:Google:1385950785975746684>'
    
    # Legacy Support (for existing code)
    check = success
    stop = error


# ==================== COLORS ====================

class Colors:
    """Centralized color configuration"""
    
    # Status Colors
    success = 0xa4ec7c
    error = 0xfc6464
    warn = 0xfbd03b
    cooldown = 0xa5eb78
    default = 0xe1d4da
    
    # Discord Theme Colors
    blurple = 0x5865F2
    dark = 0x2B2D31
    darker = 0x1e1f22
    
    # Common Colors
    red = 0xff0000
    green = 0x00ff00
    blue = 0x0000ff
    yellow = 0xffff00
    orange = 0xffa500
    purple = 0x800080
    pink = 0xffc0cb
    
    invisible = 0x2B2D31


# ==================== EMBED TEMPLATES ====================

import discord

async def success(ctx, text):
    """Success embed template"""
    embed = discord.Embed(color=Colors.success, description=f"{Emojis.success} {text}")
    return await ctx.send(embed=embed)

async def deny(ctx, text):
    """Error/deny embed template"""
    embed = discord.Embed(color=Colors.error, description=f"{Emojis.error} {ctx.author.mention}: {text}")
    return await ctx.send(embed=embed)

async def warn(ctx, text):
    """Warning embed template"""
    embed = discord.Embed(color=Colors.warn, description=f"{Emojis.warn} {ctx.author.mention}: {text}")
    return await ctx.send(embed=embed)

async def loading(ctx, text):
    """Loading embed template"""
    embed = discord.Embed(color=Colors.default, description=f"{Emojis.loading} {ctx.author.mention}: {text}")
    return await ctx.send(embed=embed)

async def info(ctx, text):
    """Info embed template"""
    embed = discord.Embed(color=Colors.default, description=f"{Emojis.info} {ctx.author.mention}: {text}")
    return await ctx.send(embed=embed)

# Utility functions for creating embeds without sending
def success_embed(text, user=None):
    """Create success embed without sending"""
    if user:
        return discord.Embed(color=Colors.success, description=f"{Emojis.success} {user.mention}: {text}")
    return discord.Embed(color=Colors.success, description=f"{Emojis.success} {text}")

def deny_embed(text, user=None):
    """Create error embed without sending"""
    if user:
        return discord.Embed(color=Colors.error, description=f"{Emojis.error} {user.mention}: {text}")
    return discord.Embed(color=Colors.error, description=f"{Emojis.error} {text}")

def warn_embed(text, user=None):
    """Create warning embed without sending"""
    if user:
        return discord.Embed(color=Colors.warn, description=f"{Emojis.warn} {user.mention}: {text}")
    return discord.Embed(color=Colors.warn, description=f"{Emojis.warn} {text}")

def loading_embed(user, text):
    """Create loading embed without sending"""
    return discord.Embed(color=Colors.default, description=f"{Emojis.loading} {user.mention}: {text}")

def info_embed(user, text):
    """Create info embed without sending"""
    return discord.Embed(color=Colors.default, description=f"{Emojis.info} {user.mention}: {text}")

# ==================== OLD EMBED TEMPLATES ====================

class EmbedTemplates:
    """Pre-built embed configurations"""
    
    @staticmethod
    def success(title: str = None, description: str = None):
        """Success embed template"""
        return {
            'color': Colors.success,
            'title': f"{Emojis.success} {title}" if title else None,
            'description': description
        }
    
    @staticmethod
    def error(title: str = None, description: str = None):
        """Error embed template"""
        return {
            'color': Colors.error,
            'title': f"{Emojis.error} {title}" if title else None,
            'description': description
        }
    
    @staticmethod
    def warning(title: str = None, description: str = None):
        """Warning embed template"""
        return {
            'color': Colors.warn,
            'title': f"{Emojis.warn} {title}" if title else None,
            'description': description
        }
    
    @staticmethod
    def info(title: str = None, description: str = None):
        """Info embed template"""
        return {
            'color': Colors.default,
            'title': title,
            'description': description
        }
    
    @staticmethod
    def loading(title: str = "Loading...", description: str = None):
        """Loading embed template"""
        return {
            'color': Colors.cooldown,
            'title': f"{Emojis.loading} {title}",
            'description': description
        }


# ==================== CONSTANTS ====================

class Constants:
    """General constants and limits"""
    
    # Pagination
    ITEMS_PER_PAGE = 10
    MAX_EMBED_FIELDS = 25
    MAX_EMBED_LENGTH = 6000
    
    # Timeouts
    DEFAULT_TIMEOUT = 60
    LONG_TIMEOUT = 300
    SHORT_TIMEOUT = 30
    
    # Limits
    MAX_REASON_LENGTH = 500
    MAX_PREFIX_LENGTH = 10
    MAX_CUSTOM_TEXT_LENGTH = 2000
    
    # URLs
    SUPPORT_SERVER = "https://discord.gg/akMpxknJaX"
    INVITE_URL = "https://discord.com/oauth2/authorize?client_id=1387303028528513185"
    
    # Default Messages
    NO_REASON = "No reason provided"
    PERMISSION_DENIED = "You don't have permission to use this command"
    COMMAND_ON_COOLDOWN = "This command is on cooldown"


# ==================== PERMISSIONS ====================

class PermissionLevels:
    """Permission level constants"""
    
    OWNER = 5
    ADMIN = 4
    MODERATOR = 3
    TRUSTED = 2
    MEMBER = 1
    RESTRICTED = 0


# ==================== DONOR BENEFITS ====================

class DonorBenefits:
    """Donor-only features and benefits"""
    
    # Features that require donor status
    DONOR_COMMANDS = [
        "premium_embed",
        "custom_prefix", 
        "advanced_automod",
        "priority_support"
    ]
    
    # Limits for donors vs regular users
    REGULAR_LIMITS = {
        'custom_embeds': 5,
        'autoresponders': 10,
        'reaction_roles': 20
    }
    
    DONOR_LIMITS = {
        'custom_embeds': 50,
        'autoresponders': 100,
        'reaction_roles': 200
    }


# ==================== HELPER FUNCTIONS ====================

def get_emoji(name: str) -> str:
    """Get emoji by name with fallback"""
    return getattr(Emojis, name, '❓')

def get_color(name: str) -> int:
    """Get color by name with fallback"""
    return getattr(Colors, name, Colors.default)

def is_donor_command(command_name: str) -> bool:
    """Check if command requires donor status"""
    return command_name in DonorBenefits.DONOR_COMMANDS

def get_limit(feature: str, is_donor: bool = False) -> int:
    """Get feature limit based on donor status"""
    limits = DonorBenefits.DONOR_LIMITS if is_donor else DonorBenefits.REGULAR_LIMITS
    return limits.get(feature, 0)


# ==================== EXPORT ALL ====================

__all__ = [
    'Emojis',
    'Colors', 
    'EmbedTemplates',
    'Constants',
    'PermissionLevels',
    'DonorBenefits',
    'get_emoji',
    'get_color',
    'is_donor_command',
    'get_limit'
]
