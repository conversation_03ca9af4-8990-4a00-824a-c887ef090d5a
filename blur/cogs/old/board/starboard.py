import discord
from discord.ext import commands
from tools.checks import Perms


class Starboard(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle star reactions"""
        if payload.user_id == self.bot.user.id:
            return
        
        # Get starboard config
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM starboard_config WHERE guild_id = %s",
                    payload.guild_id
                )
                config = await cursor.fetchone()
                
                if not config or not config[2]:  # enabled column
                    return
                
                # Check if it's the star emoji
                if str(payload.emoji) != config[3]:  # emoji column
                    return
                
                # Get the message
                guild = self.bot.get_guild(payload.guild_id)
                if not guild:
                    return
                
                channel = guild.get_channel(payload.channel_id)
                if not channel:
                    return
                
                # Don't star messages in starboard channel
                if channel.id == config[1]:  # channel_id
                    return
                
                try:
                    message = await channel.fetch_message(payload.message_id)
                except discord.NotFound:
                    return
                
                # Count total star reactions
                star_count = 0
                for reaction in message.reactions:
                    if str(reaction.emoji) == config[3]:
                        star_count = reaction.count
                        break
                
                # Check if meets threshold
                if star_count < config[4]:  # threshold column
                    return
                
                await self.handle_starboard(guild, message, star_count, config)

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle star reaction removal"""
        if payload.user_id == self.bot.user.id:
            return
        
        # Get starboard config
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM starboard_config WHERE guild_id = %s",
                    payload.guild_id
                )
                config = await cursor.fetchone()
                
                if not config or not config[2]:
                    return
                
                if str(payload.emoji) != config[3]:
                    return
                
                guild = self.bot.get_guild(payload.guild_id)
                if not guild:
                    return
                
                channel = guild.get_channel(payload.channel_id)
                if not channel or channel.id == config[1]:
                    return
                
                try:
                    message = await channel.fetch_message(payload.message_id)
                except discord.NotFound:
                    return
                
                # Count remaining stars
                star_count = 0
                for reaction in message.reactions:
                    if str(reaction.emoji) == config[3]:
                        star_count = reaction.count
                        break
                
                # Update or remove from starboard
                if star_count >= config[4]:
                    await self.handle_starboard(guild, message, star_count, config)
                else:
                    await self.remove_from_starboard(message.id, config[1])

    async def handle_starboard(self, guild, message, star_count, config):
        """Handle starboard message creation/update"""
        starboard_channel = guild.get_channel(config[1])
        if not starboard_channel:
            return
        
        # Check if message already exists in starboard
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT starboard_message_id FROM starboard_messages WHERE original_message_id = %s",
                    message.id
                )
                existing = await cursor.fetchone()
                
                embed = discord.Embed(
                    description=message.content or "*No text content*",
                    color=0xffd700,
                    timestamp=message.created_at
                )
                embed.set_author(
                    name=message.author.display_name,
                    icon_url=message.author.display_avatar.url
                )
                embed.add_field(
                    name="Source",
                    value=f"[Jump to message]({message.jump_url})",
                    inline=False
                )
                
                # Add image if present
                if message.attachments:
                    attachment = message.attachments[0]
                    if attachment.content_type and attachment.content_type.startswith('image/'):
                        embed.set_image(url=attachment.url)
                
                # Add embed image if present
                if message.embeds:
                    for embed_obj in message.embeds:
                        if embed_obj.image:
                            embed.set_image(url=embed_obj.image.url)
                            break
                
                content = f"{config[3]} **{star_count}** | {message.channel.mention}"
                
                if existing:
                    # Update existing message
                    try:
                        starboard_message = await starboard_channel.fetch_message(existing[0])
                        await starboard_message.edit(content=content, embed=embed)
                    except discord.NotFound:
                        # Starboard message was deleted, create new one
                        starboard_message = await starboard_channel.send(content=content, embed=embed)
                        await cursor.execute(
                            "UPDATE starboard_messages SET starboard_message_id = %s WHERE original_message_id = %s",
                            starboard_message.id, message.id
                        )
                else:
                    # Create new starboard message
                    starboard_message = await starboard_channel.send(content=content, embed=embed)
                    await cursor.execute(
                        "INSERT INTO starboard_messages (guild_id, original_message_id, starboard_message_id, star_count) VALUES (%s, %s, %s, %s)",
                        guild.id, message.id, starboard_message.id, star_count
                    )

    async def remove_from_starboard(self, message_id, starboard_channel_id):
        """Remove message from starboard if stars fall below threshold"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT starboard_message_id FROM starboard_messages WHERE original_message_id = %s",
                    message_id
                )
                result = await cursor.fetchone()
                
                if result:
                    # Delete starboard message
                    starboard_channel = self.bot.get_channel(starboard_channel_id)
                    if starboard_channel:
                        try:
                            starboard_message = await starboard_channel.fetch_message(result[0])
                            await starboard_message.delete()
                        except discord.NotFound:
                            pass
                    
                    # Remove from database
                    await cursor.execute(
                        "DELETE FROM starboard_messages WHERE original_message_id = %s",
                        message_id
                    )

    @commands.group(
        name="starboard",
        description="Starboard system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def starboard(self, ctx):
        """Starboard system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="⭐ Starboard System",
                description="Highlight popular messages with stars",
                color=0xffd700
            )
            
            embed.add_field(
                name="Commands",
                value="`starboard setup [channel] <threshold> <emoji>` - Setup starboard\n"
                      "`starboard disable` - Disable starboard\n"
                      "`starboard stats` - Show starboard statistics\n"
                      "`starboard top` - Show top starred messages\n"
                      "`starboard config` - Show current configuration",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @starboard.command(
        name="setup",
        description="Setup starboard",
        usage="[channel] <threshold> <emoji>"
    )
    @Perms.get_perms("manage_guild")
    async def starboard_setup(self, ctx, channel: discord.TextChannel, threshold: int = 3, emoji: str = "⭐"):
        """Setup starboard"""
        if threshold < 1 or threshold > 50:
            return await ctx.send_warning("Threshold must be between 1-50!")
        
        # Test emoji
        try:
            await ctx.message.add_reaction(emoji)
            await ctx.message.remove_reaction(emoji, ctx.guild.me)
        except:
            return await ctx.send_warning("Invalid emoji!")
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO starboard_config (guild_id, channel_id, enabled, emoji, threshold) VALUES (%s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE channel_id = %s, enabled = %s, emoji = %s, threshold = %s",
                    ctx.guild.id, channel.id, True, emoji, threshold, channel.id, True, emoji, threshold
                )
        
        embed = discord.Embed(
            title="⭐ Starboard Setup Complete",
            color=0xffd700
        )
        embed.add_field(name="Channel", value=channel.mention, inline=True)
        embed.add_field(name="Threshold", value=f"{threshold} {emoji}", inline=True)
        embed.add_field(name="Emoji", value=emoji, inline=True)
        
        await ctx.reply(embed=embed)

    @starboard.command(
        name="disable",
        description="Disable starboard"
    )
    @Perms.get_perms("manage_guild")
    async def starboard_disable(self, ctx):
        """Disable starboard"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE starboard_config SET enabled = FALSE WHERE guild_id = %s",
                    ctx.guild.id
                )
        
        await ctx.send_success("Starboard disabled!")

    @starboard.command(
        name="config",
        description="Show starboard configuration"
    )
    async def starboard_config(self, ctx):
        """Show starboard configuration"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM starboard_config WHERE guild_id = %s",
                    ctx.guild.id
                )
                config = await cursor.fetchone()
                
                if not config:
                    return await ctx.send_warning("Starboard is not configured!")
                
                embed = discord.Embed(
                    title="⭐ Starboard Configuration",
                    color=0xffd700
                )
                
                channel = ctx.guild.get_channel(config[1])
                embed.add_field(
                    name="Channel",
                    value=channel.mention if channel else "Not found",
                    inline=True
                )
                embed.add_field(
                    name="Status",
                    value="Enabled" if config[2] else "Disabled",
                    inline=True
                )
                embed.add_field(
                    name="Emoji",
                    value=config[3],
                    inline=True
                )
                embed.add_field(
                    name="Threshold",
                    value=f"{config[4]} {config[3]}",
                    inline=True
                )
                
                await ctx.reply(embed=embed)

    @starboard.command(
        name="stats",
        description="Show starboard statistics"
    )
    async def starboard_stats(self, ctx):
        """Show starboard statistics"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM starboard_messages WHERE guild_id = %s",
                    ctx.guild.id
                )
                total_starred = (await cursor.fetchone())[0]
                
                await cursor.execute(
                    "SELECT AVG(star_count) FROM starboard_messages WHERE guild_id = %s",
                    ctx.guild.id
                )
                avg_stars = await cursor.fetchone()
                avg_stars = avg_stars[0] if avg_stars[0] else 0
                
                await cursor.execute(
                    "SELECT MAX(star_count) FROM starboard_messages WHERE guild_id = %s",
                    ctx.guild.id
                )
                max_stars = await cursor.fetchone()
                max_stars = max_stars[0] if max_stars[0] else 0
                
                embed = discord.Embed(
                    title="⭐ Starboard Statistics",
                    color=0xffd700
                )
                embed.add_field(name="Total Starred Messages", value=total_starred, inline=True)
                embed.add_field(name="Average Stars", value=f"{avg_stars:.1f}", inline=True)
                embed.add_field(name="Highest Stars", value=max_stars, inline=True)
                
                await ctx.reply(embed=embed)

    @starboard.command(
        name="top",
        description="Show top starred messages"
    )
    async def starboard_top(self, ctx):
        """Show top starred messages"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT original_message_id, star_count FROM starboard_messages WHERE guild_id = %s ORDER BY star_count DESC LIMIT 10",
                    ctx.guild.id
                )
                results = await cursor.fetchall()
                
                if not results:
                    return await ctx.send_warning("No starred messages found!")
                
                embed = discord.Embed(
                    title="⭐ Top Starred Messages",
                    color=0xffd700
                )
                
                description = ""
                for i, (message_id, star_count) in enumerate(results, 1):
                    description += f"`{i}.` [Message](https://discord.com/channels/{ctx.guild.id}/_/{message_id}) - {star_count} ⭐\n"
                
                embed.description = description
                await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Starboard(bot))
