import discord
import json
import asyncio
import datetime
import random
from discord.ext import commands, tasks
from tools.checks import Perms
import humanfriendly


async def gwend_task(bot, result, date: datetime.datetime):
    """End giveaway task"""
    members = json.loads(result["members"])
    winners = result["winners"]
    channel_id = result["channel_id"]
    message_id = result["message_id"]
    
    channel = bot.get_channel(channel_id)
    if not channel:
        return
    
    try:
        message = await channel.fetch_message(message_id)
    except discord.NotFound:
        return
    
    wins = []
    if len(members) <= winners:
        embed = discord.Embed(
            color=bot.color,
            title=message.embeds[0].title,
            description=f"Hosted by: <@!{result['host']}>\n\nNot enough entries to determine the winners!",
        )
        await message.edit(embed=embed, view=None)
    else:
        for _ in range(winners):
            winner = random.choice(members)
            wins.append(winner)
            members.remove(winner)  # Prevent duplicate winners
        
        embed = discord.Embed(
            color=bot.color,
            title=message.embeds[0].title,
            description=f"Ended <t:{int(date.timestamp())}:R>\nHosted by: <@!{result['host']}>",
        ).add_field(
            name="Winners",
            value="\n".join([f"<@{w}>" for w in wins]),
        )
        await message.edit(embed=embed, view=None)
        await message.reply(
            f"🎉 **{result['title']}** winners:\n" + "\n".join([f"<@{w}>" for w in wins])
        )
    
    # Store ended giveaway
    async with bot.db.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "INSERT INTO gw_ended (channel_id, message_id, members) VALUES (%s, %s, %s)",
                channel_id, message_id, json.dumps(wins)
            )
            await cursor.execute(
                "DELETE FROM giveaway WHERE channel_id = %s AND message_id = %s",
                channel_id, message_id
            )


class GiveawayView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(
        emoji="🎉", style=discord.ButtonStyle.green, custom_id="persistent:join_gw"
    )
    async def join_gw(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Join giveaway button"""
        async with interaction.client.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM giveaway WHERE guild_id = %s AND message_id = %s",
                    interaction.guild.id, interaction.message.id
                )
                check = await cursor.fetchone()
                
                if not check:
                    return await interaction.response.send_message(
                        "This giveaway no longer exists!", ephemeral=True
                    )
                
                members = json.loads(check[4])  # members column
                
                if interaction.user.id in members:
                    # User wants to leave
                    button1 = discord.ui.Button(
                        label="Leave the Giveaway", style=discord.ButtonStyle.danger
                    )

                    async def button1_callback(inter: discord.Interaction):
                        members.remove(interaction.user.id)
                        async with inter.client.db.acquire() as conn2:
                            async with conn2.cursor() as cursor2:
                                await cursor2.execute(
                                    "UPDATE giveaway SET members = %s WHERE guild_id = %s AND message_id = %s",
                                    json.dumps(members), inter.guild.id, interaction.message.id
                                )
                        
                        # Update embed
                        embed = interaction.message.embeds[0]
                        embed.set_field_at(0, name="Entries", value=f"{len(members)}")
                        await interaction.message.edit(embed=embed)
                        
                        return await inter.response.edit_message(
                            content="You left the giveaway", view=None
                        )

                    button1.callback = button1_callback
                    view = discord.ui.View()
                    view.add_item(button1)
                    return await interaction.response.send_message(
                        content="You are already in this giveaway", view=view, ephemeral=True
                    )
                else:
                    # User joins
                    members.append(interaction.user.id)
                    await cursor.execute(
                        "UPDATE giveaway SET members = %s WHERE guild_id = %s AND message_id = %s",
                        json.dumps(members), interaction.guild.id, interaction.message.id
                    )
                    
                    # Update embed
                    embed = interaction.message.embeds[0]
                    embed.set_field_at(0, name="Entries", value=f"{len(members)}")
                    await interaction.response.edit_message(embed=embed)


class Giveaway(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.giveaway_task.start()

    def cog_unload(self):
        self.giveaway_task.cancel()

    @tasks.loop(seconds=30)
    async def giveaway_task(self):
        """Check for ended giveaways"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM giveaway WHERE finish_time <= %s",
                        datetime.datetime.utcnow()
                    )
                    results = await cursor.fetchall()
                    
                    for result in results:
                        # Convert tuple to dict-like object
                        gw_data = {
                            'guild_id': result[0],
                            'channel_id': result[1],
                            'message_id': result[2],
                            'winners': result[3],
                            'members': result[4],
                            'finish_time': result[5],
                            'host': result[6],
                            'title': result[7]
                        }
                        await gwend_task(self.bot, gw_data, datetime.datetime.utcnow())
        except Exception as e:
            print(f"Giveaway task error: {e}")

    @giveaway_task.before_loop
    async def before_giveaway_task(self):
        await self.bot.wait_until_ready()

    @commands.group(
        name="giveaway",
        description="Giveaway system",
        invoke_without_command=True,
        aliases=["gw"]
    )
    @Perms.get_perms("manage_guild")
    async def giveaway(self, ctx):
        """Giveaway system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🎉 Giveaway System",
                description="Manage server giveaways",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`giveaway create [channel]` - Create a giveaway\n"
                      "`giveaway end [message_id] [channel]` - End giveaway early\n"
                      "`giveaway reroll [message_id] [channel]` - Reroll winners\n"
                      "`giveaway list` - List active giveaways",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @giveaway.command(
        name="create",
        description="Create a giveaway",
        usage="<channel>"
    )
    @Perms.get_perms("manage_guild")
    async def gw_create(self, ctx, *, channel: discord.TextChannel = None):
        """Create a giveaway"""
        if not channel:
            channel = ctx.channel
        
        await ctx.reply(f"Starting giveaway setup in {channel.mention}...")
        
        questions = [
            "What is the prize for this giveaway?",
            "How long should the giveaway last? (e.g., 1h, 30m, 1d)",
            "How many winners should this giveaway have?"
        ]
        
        responses = []
        
        for question in questions:
            embed = discord.Embed(
                title="🎉 Giveaway Setup",
                description=question,
                color=self.bot.color
            )
            await ctx.send(embed=embed)
            
            def check(m):
                return m.author.id == ctx.author.id and m.channel.id == ctx.channel.id
            
            try:
                message = await self.bot.wait_for("message", check=check, timeout=60.0)
                responses.append(message.content)
                await message.add_reaction("✅")
            except asyncio.TimeoutError:
                return await ctx.send_warning("You didn't reply in time! Giveaway setup cancelled.")
        
        # Parse responses
        prize = responses[0]
        
        try:
            duration = humanfriendly.parse_timespan(responses[1])
        except:
            return await ctx.send_warning("Invalid time format! Use formats like: 1h, 30m, 1d")
        
        try:
            winners = int(responses[2])
            if winners < 1 or winners > 20:
                return await ctx.send_warning("Number of winners must be between 1-20!")
        except ValueError:
            return await ctx.send_warning("Invalid number of winners!")
        
        # Create giveaway
        end_time = datetime.datetime.utcnow() + datetime.timedelta(seconds=duration)
        
        embed = discord.Embed(
            title=f"🎉 {prize} 🎉",
            description=f"Ends: <t:{int(end_time.timestamp())}> (<t:{int(end_time.timestamp())}:R>)\n"
                       f"Hosted by: {ctx.author.mention}\n"
                       f"Winners: **{winners}**",
            color=self.bot.color
        )
        embed.add_field(name="Entries", value="0")
        embed.set_footer(text="Click 🎉 to enter!")
        
        view = GiveawayView()
        gw_message = await channel.send(embed=embed, view=view)
        
        # Store in database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO giveaway (guild_id, channel_id, message_id, winners, members, finish_time, host, title) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                    ctx.guild.id, channel.id, gw_message.id, winners, json.dumps([]), end_time, ctx.author.id, prize
                )
        
        await ctx.send_success(f"Giveaway created in {channel.mention}!")

    @giveaway.command(
        name="end",
        description="End a giveaway early",
        usage="[message_id] <channel>"
    )
    @Perms.get_perms("manage_guild")
    async def gw_end(self, ctx, message_id: int, *, channel: discord.TextChannel = None):
        """End giveaway early"""
        if not channel:
            channel = ctx.channel
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM giveaway WHERE guild_id = %s AND channel_id = %s AND message_id = %s",
                    ctx.guild.id, channel.id, message_id
                )
                check = await cursor.fetchone()
                
                if not check:
                    return await ctx.send_warning("This message is not an active giveaway!")
                
                # Convert to dict
                gw_data = {
                    'guild_id': check[0],
                    'channel_id': check[1], 
                    'message_id': check[2],
                    'winners': check[3],
                    'members': check[4],
                    'finish_time': check[5],
                    'host': check[6],
                    'title': check[7]
                }
                
                await gwend_task(self.bot, gw_data, datetime.datetime.utcnow())
                await ctx.send_success(f"Ended giveaway in {channel.mention}")

    @giveaway.command(
        name="reroll",
        description="Reroll giveaway winners",
        usage="[message_id] <channel>"
    )
    @Perms.get_perms("manage_guild")
    async def gw_reroll(self, ctx, message_id: int, *, channel: discord.TextChannel = None):
        """Reroll giveaway winners"""
        if not channel:
            channel = ctx.channel
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM gw_ended WHERE channel_id = %s AND message_id = %s",
                    channel.id, message_id
                )
                check = await cursor.fetchone()
                
                if not check:
                    return await ctx.send_warning("This message is not an ended giveaway!")
                
                members = json.loads(check[2])  # members column
                if not members:
                    return await ctx.send_warning("No participants to reroll!")
                
                new_winner = random.choice(members)
                await ctx.reply(f"🎉 **New winner:** <@{new_winner}>")

    @giveaway.command(
        name="list",
        description="List active giveaways"
    )
    async def gw_list(self, ctx):
        """List active giveaways"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM giveaway WHERE guild_id = %s ORDER BY finish_time",
                    ctx.guild.id
                )
                results = await cursor.fetchall()
                
                if not results:
                    return await ctx.send_warning("No active giveaways in this server!")
                
                embed = discord.Embed(
                    title=f"🎉 Active Giveaways ({len(results)})",
                    color=self.bot.color
                )
                
                description = ""
                for i, result in enumerate(results[:10], 1):
                    description += f"`{i}` [**{result[7]}**](https://discord.com/channels/{ctx.guild.id}/{result[1]}/{result[2]}) ends <t:{int(result[5].timestamp())}:R>\n"
                
                if len(results) > 10:
                    description += f"\n... and {len(results) - 10} more giveaways"
                
                embed.description = description
                await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Giveaway(bot))
