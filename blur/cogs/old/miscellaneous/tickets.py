import discord
from discord.ext import commands
from tools.checks import Perms
import asyncio


class TicketView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Create Ticket", emoji="🎫", style=discord.ButtonStyle.green, custom_id="create_ticket")
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Create a new ticket"""
        # Check if user already has a ticket
        existing = await interaction.client.db.fetchrow(
            "SELECT * FROM tickets WHERE guild_id = %s AND user_id = %s AND status = 'open'",
            interaction.guild.id, interaction.user.id
        )
        
        if existing:
            return await interaction.response.send_message("You already have an open ticket!", ephemeral=True)
        
        # Get ticket config
        config = await interaction.client.db.fetchrow(
            "SELECT * FROM ticket_config WHERE guild_id = %s",
            interaction.guild.id
        )
        
        if not config:
            return await interaction.response.send_message("Ticket system not configured!", ephemeral=True)
        
        # Create ticket channel
        category = interaction.guild.get_channel(config['category_id'])
        support_role = interaction.guild.get_role(config['support_role_id']) if config['support_role_id'] else None
        
        overwrites = {
            interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
            interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
            interaction.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
        }
        
        if support_role:
            overwrites[support_role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)
        
        ticket_channel = await interaction.guild.create_text_channel(
            name=f"ticket-{interaction.user.name}",
            category=category,
            overwrites=overwrites
        )
        
        # Store ticket in database
        await interaction.client.db.execute(
            "INSERT INTO tickets (guild_id, user_id, channel_id, status) VALUES (%s, %s, %s, 'open')",
            interaction.guild.id, interaction.user.id, ticket_channel.id
        )
        
        # Send ticket message
        embed = discord.Embed(
            title="🎫 Ticket Created",
            description=f"Hello {interaction.user.mention}! Support will be with you shortly.",
            color=interaction.client.color
        )
        
        close_view = TicketCloseView()
        await ticket_channel.send(embed=embed, view=close_view)
        
        await interaction.response.send_message(f"Ticket created: {ticket_channel.mention}", ephemeral=True)


class TicketCloseView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Close Ticket", emoji="🔒", style=discord.ButtonStyle.red, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Close the ticket"""
        # Check if this is a ticket channel
        ticket_data = await interaction.client.db.fetchrow(
            "SELECT * FROM tickets WHERE guild_id = %s AND channel_id = %s AND status = 'open'",
            interaction.guild.id, interaction.channel.id
        )
        
        if not ticket_data:
            return await interaction.response.send_message("This is not a ticket channel!", ephemeral=True)
        
        # Check permissions
        config = await interaction.client.db.fetchrow(
            "SELECT * FROM ticket_config WHERE guild_id = %s",
            interaction.guild.id
        )
        
        support_role = interaction.guild.get_role(config['support_role_id']) if config and config['support_role_id'] else None
        
        if (interaction.user.id != ticket_data['user_id'] and 
            not interaction.user.guild_permissions.manage_channels and
            (not support_role or support_role not in interaction.user.roles)):
            return await interaction.response.send_message("You don't have permission to close this ticket!", ephemeral=True)
        
        # Update ticket status
        await interaction.client.db.execute(
            "UPDATE tickets SET status = 'closed' WHERE channel_id = %s",
            interaction.channel.id
        )
        
        embed = discord.Embed(
            title="🔒 Ticket Closed",
            description="This ticket will be deleted in 10 seconds...",
            color=0xff0000
        )
        
        await interaction.response.send_message(embed=embed)
        
        # Delete channel after 10 seconds
        await asyncio.sleep(10)
        await interaction.channel.delete(reason="Ticket closed")


class Tickets(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="ticket",
        description="Ticket system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_channels")
    async def ticket(self, ctx):
        """Ticket system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🎫 Ticket System",
                description="Manage support tickets",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`ticket setup [category] [support_role]` - Setup ticket system\n"
                      "`ticket panel` - Send ticket creation panel\n"
                      "`ticket close` - Close current ticket\n"
                      "`ticket add [member]` - Add member to ticket\n"
                      "`ticket remove [member]` - Remove member from ticket",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @ticket.command(
        name="setup",
        description="Setup ticket system",
        usage="[category] <support_role>"
    )
    @Perms.get_perms("manage_channels")
    async def ticket_setup(self, ctx, category: discord.CategoryChannel, support_role: discord.Role = None):
        """Setup ticket system"""
        await self.bot.db.execute(
            "INSERT INTO ticket_config (guild_id, category_id, support_role_id) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE category_id = %s, support_role_id = %s",
            ctx.guild.id, category.id, support_role.id if support_role else None, category.id, support_role.id if support_role else None
        )
        
        support_text = f" with support role {support_role.mention}" if support_role else ""
        await ctx.send_success(f"Ticket system setup in {category.mention}{support_text}")

    @ticket.command(
        name="panel",
        description="Send ticket creation panel"
    )
    @Perms.get_perms("manage_channels")
    async def ticket_panel(self, ctx):
        """Send ticket creation panel"""
        embed = discord.Embed(
            title="🎫 Support Tickets",
            description="Click the button below to create a support ticket",
            color=self.bot.color
        )
        
        view = TicketView()
        await ctx.send(embed=embed, view=view)

    @ticket.command(
        name="close",
        description="Close current ticket"
    )
    async def ticket_close(self, ctx):
        """Close current ticket"""
        ticket_data = await self.bot.db.fetchrow(
            "SELECT * FROM tickets WHERE guild_id = %s AND channel_id = %s AND status = 'open'",
            ctx.guild.id, ctx.channel.id
        )
        
        if not ticket_data:
            return await ctx.send_warning("This is not a ticket channel!")
        
        # Check permissions
        config = await self.bot.db.fetchrow(
            "SELECT * FROM ticket_config WHERE guild_id = %s",
            ctx.guild.id
        )
        
        support_role = ctx.guild.get_role(config['support_role_id']) if config and config['support_role_id'] else None
        
        if (ctx.author.id != ticket_data['user_id'] and 
            not ctx.author.guild_permissions.manage_channels and
            (not support_role or support_role not in ctx.author.roles)):
            return await ctx.send_warning("You don't have permission to close this ticket!")
        
        # Update ticket status
        await self.bot.db.execute(
            "UPDATE tickets SET status = 'closed' WHERE channel_id = %s",
            ctx.channel.id
        )
        
        embed = discord.Embed(
            title="🔒 Ticket Closed",
            description="This ticket will be deleted in 10 seconds...",
            color=0xff0000
        )
        
        await ctx.send(embed=embed)
        
        # Delete channel after 10 seconds
        await asyncio.sleep(10)
        await ctx.channel.delete(reason="Ticket closed")

    @ticket.command(
        name="add",
        description="Add member to ticket",
        usage="[member]"
    )
    async def ticket_add(self, ctx, member: discord.Member):
        """Add member to ticket"""
        ticket_data = await self.bot.db.fetchrow(
            "SELECT * FROM tickets WHERE guild_id = %s AND channel_id = %s AND status = 'open'",
            ctx.guild.id, ctx.channel.id
        )
        
        if not ticket_data:
            return await ctx.send_warning("This is not a ticket channel!")
        
        await ctx.channel.set_permissions(member, read_messages=True, send_messages=True)
        await ctx.send_success(f"Added {member.mention} to the ticket")

    @ticket.command(
        name="remove",
        description="Remove member from ticket",
        usage="[member]"
    )
    async def ticket_remove(self, ctx, member: discord.Member):
        """Remove member from ticket"""
        ticket_data = await self.bot.db.fetchrow(
            "SELECT * FROM tickets WHERE guild_id = %s AND channel_id = %s AND status = 'open'",
            ctx.guild.id, ctx.channel.id
        )
        
        if not ticket_data:
            return await ctx.send_warning("This is not a ticket channel!")
        
        if member.id == ticket_data['user_id']:
            return await ctx.send_warning("You can't remove the ticket owner!")
        
        await ctx.channel.set_permissions(member, read_messages=False)
        await ctx.send_success(f"Removed {member.mention} from the ticket")


async def setup(bot):
    await bot.add_cog(Tickets(bot))
