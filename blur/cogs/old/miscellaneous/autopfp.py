import discord
from discord.ext import commands
from tools.checks import Perms
import aiohttp
import asyncio
import random


class AutoPFP(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="autopfp",
        description="Auto profile picture system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def autopfp(self, ctx):
        """Auto profile picture system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🖼️ Auto PFP System",
                description="Automatically change bot's profile picture",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`autopfp add [image_url]` - Add image to rotation\n"
                      "`autopfp remove [index]` - Remove image from rotation\n"
                      "`autopfp list` - List all images\n"
                      "`autopfp start [interval]` - Start auto rotation\n"
                      "`autopfp stop` - Stop auto rotation\n"
                      "`autopfp change` - Manually change PFP",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @autopfp.command(
        name="add",
        description="Add image to autopfp rotation",
        usage="[image_url]"
    )
    @Perms.get_perms("administrator")
    async def autopfp_add(self, ctx, image_url: str):
        """Add image to autopfp rotation"""
        # Validate image URL
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as resp:
                    if resp.status != 200:
                        return await ctx.send_warning("Invalid image URL!")
                    
                    content_type = resp.headers.get('content-type', '')
                    if not content_type.startswith('image/'):
                        return await ctx.send_warning("URL must be an image!")
        except:
            return await ctx.send_warning("Failed to validate image URL!")
        
        # Add to database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO autopfp_images (guild_id, image_url) VALUES (%s, %s)",
                    (ctx.guild.id, image_url)
                )
        
        await ctx.send_success("Added image to autopfp rotation!")

    @autopfp.command(
        name="remove",
        description="Remove image from rotation",
        usage="[index]"
    )
    @Perms.get_perms("administrator")
    async def autopfp_remove(self, ctx, index: int):
        """Remove image from autopfp rotation"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM autopfp_images WHERE guild_id = %s ORDER BY id",
                    (ctx.guild.id,)
                )
                images = await cursor.fetchall()
        
        if not images or index < 1 or index > len(images):
            return await ctx.send_warning("Invalid index!")
        
        image = images[index - 1]
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM autopfp_images WHERE id = %s",
                    (image['id'],)
                )
        
        await ctx.send_success(f"Removed image {index} from rotation!")

    @autopfp.command(
        name="list",
        description="List all autopfp images"
    )
    @Perms.get_perms("administrator")
    async def autopfp_list(self, ctx):
        """List all autopfp images"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM autopfp_images WHERE guild_id = %s ORDER BY id",
                    (ctx.guild.id,)
                )
                images = await cursor.fetchall()
        
        if not images:
            return await ctx.send_warning("No images in autopfp rotation!")
        
        embed = discord.Embed(
            title="🖼️ AutoPFP Images",
            color=self.bot.color
        )
        
        description = ""
        for i, image in enumerate(images[:10], 1):
            description += f"{i}. [Image {i}]({image['image_url']})\n"
        
        if len(images) > 10:
            description += f"\n... and {len(images) - 10} more images"
        
        embed.description = description
        await ctx.reply(embed=embed)

    @autopfp.command(
        name="start",
        description="Start autopfp rotation",
        usage="[interval_hours]"
    )
    @Perms.get_perms("administrator")
    async def autopfp_start(self, ctx, interval: int = 24):
        """Start autopfp rotation"""
        if interval < 1 or interval > 168:  # Max 1 week
            return await ctx.send_warning("Interval must be between 1-168 hours!")
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM autopfp_images WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                images = await cursor.fetchall()
        
        if not images:
            return await ctx.send_warning("No images in rotation! Add some first.")
        
        # Update config
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO autopfp_config (guild_id, enabled, interval_hours) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE enabled = %s, interval_hours = %s",
                    (ctx.guild.id, True, interval, True, interval)
                )
        
        await ctx.send_success(f"Started autopfp rotation with {interval} hour interval!")

    @autopfp.command(
        name="stop",
        description="Stop autopfp rotation"
    )
    @Perms.get_perms("administrator")
    async def autopfp_stop(self, ctx):
        """Stop autopfp rotation"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE autopfp_config SET enabled = FALSE WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
        
        await ctx.send_success("Stopped autopfp rotation!")

    @autopfp.command(
        name="change",
        description="Manually change profile picture"
    )
    @Perms.get_perms("administrator")
    async def autopfp_change(self, ctx):
        """Manually change profile picture"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM autopfp_images WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                images = await cursor.fetchall()
        
        if not images:
            return await ctx.send_warning("No images in rotation!")
        
        # Pick random image
        image = random.choice(images)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image['image_url']) as resp:
                    if resp.status == 200:
                        image_data = await resp.read()
                        await self.bot.user.edit(avatar=image_data)
                        await ctx.send_success("Changed profile picture!")
                    else:
                        await ctx.send_warning("Failed to download image!")
        except discord.HTTPException as e:
            if "rate limited" in str(e).lower():
                await ctx.send_warning("Rate limited! Try again later.")
            else:
                await ctx.send_warning(f"Failed to change avatar: {e}")
        except Exception as e:
            await ctx.send_warning(f"Error: {e}")

    async def autopfp_task(self):
        """Background task for autopfp rotation"""
        await self.bot.wait_until_ready()
        
        while not self.bot.is_closed():
            try:
                # Get all enabled autopfp configs
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT * FROM autopfp_config WHERE enabled = TRUE"
                        )
                        configs = await cursor.fetchall()
                
                for config in configs:
                    # Check if it's time to change
                    last_change = config.get('last_change', 0)
                    interval_seconds = config['interval_hours'] * 3600
                    
                    if (discord.utils.utcnow().timestamp() - last_change) >= interval_seconds:
                        # Get images for this guild
                        async with self.bot.db.acquire() as conn2:
                            async with conn2.cursor() as cursor2:
                                await cursor2.execute(
                                    "SELECT * FROM autopfp_images WHERE guild_id = %s",
                                    (config['guild_id'],)
                                )
                                images = await cursor2.fetchall()
                        
                        if images:
                            image = random.choice(images)
                            
                            try:
                                async with aiohttp.ClientSession() as session:
                                    async with session.get(image['image_url']) as resp:
                                        if resp.status == 200:
                                            image_data = await resp.read()
                                            await self.bot.user.edit(avatar=image_data)
                                            
                                            # Update last change time
                                            async with self.bot.db.acquire() as conn3:
                                                async with conn3.cursor() as cursor3:
                                                    await cursor3.execute(
                                                        "UPDATE autopfp_config SET last_change = %s WHERE guild_id = %s",
                                                        (discord.utils.utcnow().timestamp(), config['guild_id'])
                                                    )
                            except:
                                pass  # Ignore errors in background task
                
                # Wait 1 hour before checking again
                await asyncio.sleep(3600)
                
            except Exception as e:
                print(f"AutoPFP task error: {e}")
                await asyncio.sleep(3600)

    @commands.Cog.listener()
    async def on_ready(self):
        """Start autopfp task when bot is ready"""
        if not hasattr(self.bot, 'autopfp_task_started'):
            self.bot.autopfp_task_started = True
            self.bot.loop.create_task(self.autopfp_task())


async def setup(bot):
    await bot.add_cog(AutoPFP(bot))
