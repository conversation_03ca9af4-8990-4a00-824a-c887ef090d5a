import discord
from discord.ext import commands
import random
import datetime
import functools


def create_account():
    """Decorator to create economy account if it doesn't exist"""
    def predicate(func):
        @functools.wraps(func)
        async def wrapper(self, ctx, *args, **kwargs):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                    )
                    check = await cursor.fetchone()

                    if not check:
                        await cursor.execute(
                            "INSERT INTO economy (user_id, cash, bank, rob, daily, weekly, last_work) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                            (ctx.author.id, 100.0, 0.0, 0, 0, 0, 0)
                        )

            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return predicate


class Economy(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.cash = "💵"
        self.bank = "🏦"

    async def economy_send(self, ctx, message: str):
        """Send economy embed"""
        embed = discord.Embed(
            color=self.bot.color,
            description=f"{self.cash} {ctx.author.mention}: {message}"
        )
        return await ctx.reply(embed=embed)

    @commands.command(
        name="balance",
        description="Check your balance",
        aliases=["bal", "money"]
    )
    @create_account()
    async def balance(self, ctx, member: discord.Member = None):
        """Check balance"""
        member = member or ctx.author

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (member.id,)
                )
                data = await cursor.fetchone()

        if not data:
            if member == ctx.author:
                # Create account for self
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "INSERT INTO economy (user_id, cash, bank, rob, daily, weekly, last_work) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                            (member.id, 100.0, 0.0, 0, 0, 0, 0)
                        )
                cash, bank = 100.0, 0.0
            else:
                return await ctx.send_warning(f"**{member}** doesn't have an economy account")
        else:
            cash, bank = data[1], data[2]  # data is tuple: (user_id, cash, bank, rob, daily, weekly)
        
        total = cash + bank
        
        embed = discord.Embed(
            title=f"{member.display_name}'s Balance",
            color=self.bot.color
        )
        embed.add_field(name=f"{self.cash} Cash", value=f"${cash:,.2f}", inline=True)
        embed.add_field(name=f"{self.bank} Bank", value=f"${bank:,.2f}", inline=True)
        embed.add_field(name="💰 Total", value=f"${total:,.2f}", inline=True)
        embed.set_thumbnail(url=member.display_avatar.url)
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="daily",
        description="Claim your daily reward"
    )
    @create_account()
    async def daily(self, ctx):
        """Claim daily reward"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                data = await cursor.fetchone()

        now = datetime.datetime.utcnow().timestamp()
        last_daily = data[4]  # data[4] is daily

        # Check if 24 hours have passed
        if now - last_daily < 86400:  # 24 hours in seconds
            time_left = 86400 - (now - last_daily)
            hours = int(time_left // 3600)
            minutes = int((time_left % 3600) // 60)

            return await ctx.send_warning(f"You already claimed your daily! Try again in **{hours}h {minutes}m**")

        # Give daily reward
        reward = random.randint(100, 500)
        new_cash = data[1] + reward  # data[1] is cash

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s, daily = %s WHERE user_id = %s",
                    (new_cash, int(now), ctx.author.id)
                )
        
        await self.economy_send(ctx, f"You claimed your daily reward of **${reward}**!")

    @commands.command(
        name="weekly",
        description="Claim your weekly reward"
    )
    @create_account()
    async def weekly(self, ctx):
        """Claim weekly reward"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                data = await cursor.fetchone()

        now = datetime.datetime.utcnow().timestamp()
        last_weekly = data[5]  # data[5] is weekly

        # Check if 7 days have passed
        if now - last_weekly < 604800:  # 7 days in seconds
            time_left = 604800 - (now - last_weekly)
            days = int(time_left // 86400)
            hours = int((time_left % 86400) // 3600)

            return await ctx.send_warning(f"You already claimed your weekly! Try again in **{days}d {hours}h**")

        # Give weekly reward
        reward = random.randint(1000, 3000)
        new_cash = data[1] + reward  # data[1] is cash

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s, weekly = %s WHERE user_id = %s",
                    (new_cash, int(now), ctx.author.id)
                )
        
        await self.economy_send(ctx, f"You claimed your weekly reward of **${reward}**!")

    @commands.command(
        name="work",
        description="Work to earn money"
    )
    @create_account()
    async def work(self, ctx):
        """Work to earn money"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                data = await cursor.fetchone()

        # Work cooldown (1 hour)
        now = datetime.datetime.utcnow().timestamp()
        last_work = data[6] if len(data) > 6 else 0  # data[6] is last_work

        if now - last_work < 3600:  # 1 hour cooldown
            time_left = 3600 - (now - last_work)
            minutes = int(time_left // 60)

            return await ctx.send_warning(f"You're tired! Rest for **{minutes} minutes** before working again")

        # Work rewards
        jobs = [
            ("programmer", 50, 150),
            ("teacher", 30, 100),
            ("doctor", 100, 200),
            ("chef", 40, 120),
            ("artist", 25, 80),
            ("mechanic", 60, 140)
        ]

        job, min_pay, max_pay = random.choice(jobs)
        earnings = random.randint(min_pay, max_pay)

        new_cash = data[1] + earnings  # data[1] is cash

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s, last_work = %s WHERE user_id = %s",
                    (new_cash, int(now), ctx.author.id)
                )
        
        await self.economy_send(ctx, f"You worked as a **{job}** and earned **${earnings}**!")

    @commands.command(
        name="deposit",
        description="Deposit money to bank",
        usage="[amount]",
        aliases=["dep"]
    )
    @create_account()
    async def deposit(self, ctx, amount: str):
        """Deposit money to bank"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                data = await cursor.fetchone()

        if amount.lower() == "all":
            amount = data[1]  # data[1] is cash
        else:
            try:
                amount = float(amount)
            except ValueError:
                return await ctx.send_warning("Invalid amount!")

        if amount <= 0:
            return await ctx.send_warning("Amount must be positive!")

        if data[1] < amount:  # data[1] is cash
            return await ctx.send_warning("You don't have enough cash!")

        new_cash = data[1] - amount  # data[1] is cash
        new_bank = data[2] + amount  # data[2] is bank

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s, bank = %s WHERE user_id = %s",
                    (new_cash, new_bank, ctx.author.id)
                )
        
        await self.economy_send(ctx, f"Deposited **${amount:,.2f}** to your bank!")

    @commands.command(
        name="withdraw",
        description="Withdraw money from bank",
        usage="[amount]",
        aliases=["with"]
    )
    @create_account()
    async def withdraw(self, ctx, amount: str):
        """Withdraw money from bank"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                data = await cursor.fetchone()

        if amount.lower() == "all":
            amount = data[2]  # data[2] is bank
        else:
            try:
                amount = float(amount)
            except ValueError:
                return await ctx.send_warning("Invalid amount!")

        if amount <= 0:
            return await ctx.send_warning("Amount must be positive!")

        if data[2] < amount:  # data[2] is bank
            return await ctx.send_warning("You don't have enough money in bank!")

        new_cash = data[1] + amount  # data[1] is cash
        new_bank = data[2] - amount  # data[2] is bank

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s, bank = %s WHERE user_id = %s",
                    (new_cash, new_bank, ctx.author.id)
                )
        
        await self.economy_send(ctx, f"Withdrew **${amount:,.2f}** from your bank!")

    @commands.command(
        name="pay",
        description="Pay someone money",
        usage="[member] [amount]",
        aliases=["give"]
    )
    @create_account()
    async def pay(self, ctx, member: discord.Member, amount: float):
        """Pay someone money"""
        if member == ctx.author:
            return await ctx.send_warning("You can't pay yourself!")
        
        if member.bot:
            return await ctx.send_warning("You can't pay bots!")
        
        if amount <= 0:
            return await ctx.send_warning("Amount must be positive!")
        
        # Check sender's balance
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (ctx.author.id,)
                )
                sender_data = await cursor.fetchone()

        if sender_data[1] < amount:  # sender_data[1] is cash
            return await ctx.send_warning("You don't have enough cash!")

        # Create receiver account if needed
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM economy WHERE user_id = %s", (member.id,)
                )
                receiver_data = await cursor.fetchone()

        if not receiver_data:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO economy (user_id, cash, bank, rob, daily, weekly, last_work) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                        (member.id, 0.0, 0.0, 0, 0, 0, 0)
                    )
            receiver_cash = 0.0
        else:
            receiver_cash = receiver_data[1]  # receiver_data[1] is cash

        # Transfer money
        new_sender_cash = sender_data[1] - amount  # sender_data[1] is cash
        new_receiver_cash = receiver_cash + amount

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s WHERE user_id = %s",
                    (new_sender_cash, ctx.author.id)
                )

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE economy SET cash = %s WHERE user_id = %s",
                    (new_receiver_cash, member.id)
                )
        
        await self.economy_send(ctx, f"Paid **${amount:,.2f}** to **{member.display_name}**!")


async def setup(bot):
    await bot.add_cog(Economy(bot))
