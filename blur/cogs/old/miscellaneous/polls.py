import discord
from discord.ext import commands
from tools.checks import Perms
import asyncio
import datetime


class Polls(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(
        name="poll",
        description="Create a simple yes/no poll",
        usage="<duration> <question> or <question> (60 seconds default)"
    )
    async def poll(self, ctx, duration_or_question=None, *, question: str = None):
        """Create a simple yes/no poll"""
        
        # Parse arguments
        if duration_or_question is None:
            return await ctx.send_error("Usage: `,poll <duration> <question>` or `,poll <question>`")
        
        # Check if first argument is a number (duration) or text (question)
        try:
            duration = int(duration_or_question)
            if question is None:
                return await ctx.send_error("Please provide a question after the duration!")
        except ValueError:
            # First argument is not a number, so it's part of the question
            duration = 60  # Default duration
            question = f"{duration_or_question} {question}" if question else duration_or_question
        
        # Limit duration
        if duration > 250:
            duration = 250
        
        if duration < 10:
            duration = 60
        
        # Create embed
        embed = discord.Embed(
            color=self.bot.color,
            timestamp=datetime.datetime.utcnow()
        )
        
        embed.set_author(
            name=ctx.author.display_name,
            icon_url=ctx.author.display_avatar.url
        )
        
        end_time = datetime.datetime.utcnow() + datetime.timedelta(seconds=duration)
        embed.description = f"**{ctx.author.display_name}** started a poll that will end <t:{int(end_time.timestamp())}:R>\n\n**Question:** {question}"
        
        message = await ctx.send(embed=embed)
        
        # Add reactions
        await message.add_reaction("��")
        await message.add_reaction("👎")
        
        # Wait for poll to end
        await asyncio.sleep(duration)
        
        # Get final reactions
        try:
            message = await ctx.channel.fetch_message(message.id)
            thumbs_up = 0
            thumbs_down = 0
            
            for reaction in message.reactions:
                if str(reaction.emoji) == "👍":
                    thumbs_up = reaction.count - 1  # Subtract bot's reaction
                elif str(reaction.emoji) == "👎":
                    thumbs_down = reaction.count - 1  # Subtract bot's reaction
            
            # Update embed with results
            embed.description = f"**{ctx.author.display_name}** started a poll that will end <t:{int(end_time.timestamp())}:R>\n\n**Question:** {question}\n\n👍 `{thumbs_up}` / 👎 `{thumbs_down}`"
            
            await message.edit(embed=embed)
        except:
            pass  # Message might be deleted


async def setup(bot):
    await bot.add_cog(Polls(bot))
