import discord
from discord.ext import commands
from tools.checks import Perms
from config.constants import Emojis
import aiohttp
import re
import io


class Steal(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def extract_emojis_from_content(self, content):
        """Extract all custom emojis from text content"""
        emoji_pattern = r'<a?:(\w+):(\d+)>'
        return re.findall(emoji_pattern, content)

    async def extract_emojis_from_embed(self, embed):
        """Extract all custom emojis from an embed"""
        emojis = []
        
        # Check embed title
        if embed.title:
            emojis.extend(await self.extract_emojis_from_content(embed.title))
        
        # Check embed description
        if embed.description:
            emojis.extend(await self.extract_emojis_from_content(embed.description))
        
        # Check embed fields
        for field in embed.fields:
            if field.name:
                emojis.extend(await self.extract_emojis_from_content(field.name))
            if field.value:
                emojis.extend(await self.extract_emojis_from_content(field.value))
        
        # Check embed footer
        if embed.footer and embed.footer.text:
            emojis.extend(await self.extract_emojis_from_content(embed.footer.text))
        
        # Check embed author
        if embed.author and embed.author.name:
            emojis.extend(await self.extract_emojis_from_content(embed.author.name))
        
        return emojis

    async def steal_single_emoji(self, ctx, emoji_name, emoji_id, animated=False):
        """Steal a single emoji"""
        # Validate name
        if not re.match(r'^[a-zA-Z0-9_]+$', emoji_name):
            emoji_name = re.sub(r'[^a-zA-Z0-9_]', '_', emoji_name)
        
        if len(emoji_name) < 2:
            emoji_name = f"emoji_{emoji_id}"
        elif len(emoji_name) > 32:
            emoji_name = emoji_name[:32]
        
        # Check emoji limits based on boost level
        if ctx.guild.premium_tier == 0:
            emoji_limit = 50  # No boost
        elif ctx.guild.premium_tier == 1:
            emoji_limit = 100  # Level 1 boost
        elif ctx.guild.premium_tier == 2:
            emoji_limit = 150  # Level 2 boost
        elif ctx.guild.premium_tier >= 3:
            emoji_limit = 250  # Level 3 boost
        
        current_emojis = len([e for e in ctx.guild.emojis if e.animated == animated])
        
        if current_emojis >= emoji_limit:
            return None, f"Server has reached the limit for {'animated' if animated else 'static'} emojis ({emoji_limit})!"
        
        # Download emoji
        emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.{'gif' if animated else 'png'}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(emoji_url) as resp:
                    if resp.status != 200:
                        return None, "Failed to download emoji!"
                    
                    emoji_data = await resp.read()
                    
                    if len(emoji_data) > 256000:  # 256KB limit
                        return None, "Emoji file is too large!"
        except Exception as e:
            return None, f"Failed to download emoji: {e}"
        
        # Create emoji
        try:
            new_emoji = await ctx.guild.create_custom_emoji(
                name=emoji_name,
                image=emoji_data,
                reason=f"Stolen by {ctx.author}"
            )
            return new_emoji, None
            
        except discord.HTTPException as e:
            if "Maximum number of emojis reached" in str(e):
                return None, "Server has reached the emoji limit!"
            elif "Invalid image" in str(e):
                return None, "Invalid emoji image!"
            else:
                return None, f"Failed to create emoji: {e}"
        except Exception as e:
            return None, f"Error creating emoji: {e}"

    async def steal_sticker(self, ctx, sticker):
        """Steal a sticker"""
        # Validate name
        final_name = sticker.name
        if len(final_name) < 2 or len(final_name) > 30:
            final_name = f"sticker_{sticker.id}"
        
        # Check sticker limits
        sticker_limit = 5
        if ctx.guild.premium_tier >= 1:
            sticker_limit = 15
        elif ctx.guild.premium_tier >= 2:
            sticker_limit = 30
        elif ctx.guild.premium_tier >= 3:
            sticker_limit = 60
        
        if len(ctx.guild.stickers) >= sticker_limit:
            return None, f"Server has reached the sticker limit ({sticker_limit})!"
        
        # Download sticker
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(sticker.url) as resp:
                    if resp.status != 200:
                        return None, "Failed to download sticker!"
                    
                    sticker_data = await resp.read()
                    
                    if len(sticker_data) > 512000:  # 512KB limit
                        return None, "Sticker file is too large!"
        except Exception as e:
            return None, f"Failed to download sticker: {e}"
        
        # Create sticker
        try:
            new_sticker = await ctx.guild.create_sticker(
                name=final_name,
                description=sticker.description or "Stolen sticker",
                emoji="😀",  # Required emoji
                file=discord.File(io.BytesIO(sticker_data), filename=f"{final_name}.png"),
                reason=f"Stolen by {ctx.author}"
            )
            return new_sticker, None
            
        except discord.HTTPException as e:
            if "Maximum number of stickers reached" in str(e):
                return None, "Server has reached the sticker limit!"
            else:
                return None, f"Failed to create sticker: {e}"
        except Exception as e:
            return None, f"Error creating sticker: {e}"

    @commands.command(
        name="steal",
        description="Steal emojis and stickers from messages",
        usage="Reply to a message with emojis/stickers"
    )
    @Perms.get_perms("manage_emojis")
    async def steal(self, ctx):
        """Steal emojis and stickers from replied message"""
        if not ctx.message.reference:
            return await ctx.send_warning("Please reply to a message containing emojis or stickers!")

        try:
            replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
        except:
            return await ctx.send_warning("Could not fetch the replied message!")

        # Send initial processing message
        processing_msg = await ctx.send(f"{Emojis.loading} Scanning message for emojis and stickers...")

        # Collect all emojis and stickers
        emojis_to_steal = []
        stickers_to_steal = []

        # Extract emojis from message content using improved regex
        if replied_message.content:
            # Find all custom emojis (both animated and static)
            emoji_pattern = r'<(a?):([a-zA-Z0-9_]+):(\d+)>'
            matches = re.findall(emoji_pattern, replied_message.content)

            for animated_flag, emoji_name, emoji_id in matches:
                animated = bool(animated_flag)  # 'a' for animated, '' for static
                emojis_to_steal.append((emoji_name, emoji_id, animated))

        # Extract emojis from embeds
        for embed in replied_message.embeds:
            embed_content = ""

            # Combine all embed text content
            if embed.title:
                embed_content += embed.title + " "
            if embed.description:
                embed_content += embed.description + " "
            if embed.footer and embed.footer.text:
                embed_content += embed.footer.text + " "
            if embed.author and embed.author.name:
                embed_content += embed.author.name + " "

            # Include ALL fields (this was missing the field content!)
            for field in embed.fields:
                if field.name:
                    embed_content += field.name + " "
                if field.value:
                    embed_content += field.value + " "

            # Extract emojis from combined embed content
            if embed_content:
                emoji_pattern = r'<(a?):([a-zA-Z0-9_]+):(\d+)>'
                matches = re.findall(emoji_pattern, embed_content)

                for animated_flag, emoji_name, emoji_id in matches:
                    animated = bool(animated_flag)
                    emojis_to_steal.append((emoji_name, emoji_id, animated))

        # Extract stickers
        for sticker in replied_message.stickers:
            stickers_to_steal.append(sticker)

        # Remove duplicates while preserving order
        seen = set()
        unique_emojis = []
        for emoji in emojis_to_steal:
            if emoji not in seen:
                seen.add(emoji)
                unique_emojis.append(emoji)
        emojis_to_steal = unique_emojis

        if not emojis_to_steal and not stickers_to_steal:
            await processing_msg.edit(content="❌ No emojis or stickers found in the replied message!")
            return

        # Update processing message
        await processing_msg.edit(content=f"{Emojis.loading} Found {len(emojis_to_steal)} emojis and {len(stickers_to_steal)} stickers. Starting to steal...")

        # Start stealing
        stolen_emojis = []
        stolen_stickers = []
        failed_items = []

        # Steal emojis
        for i, (emoji_name, emoji_id, animated) in enumerate(emojis_to_steal, 1):
            await processing_msg.edit(content=f"{Emojis.loading} Stealing emoji {i}/{len(emojis_to_steal)}: {emoji_name}...")

            emoji, error = await self.steal_single_emoji(ctx, emoji_name, emoji_id, animated)
            if emoji:
                stolen_emojis.append(emoji)
            else:
                failed_items.append(f"Emoji `{emoji_name}`: {error}")

        # Steal stickers
        for i, sticker in enumerate(stickers_to_steal, 1):
            await processing_msg.edit(content=f"{Emojis.loading} Stealing sticker {i}/{len(stickers_to_steal)}: {sticker.name}...")

            new_sticker, error = await self.steal_sticker(ctx, sticker)
            if new_sticker:
                stolen_stickers.append(new_sticker)
            else:
                failed_items.append(f"Sticker `{sticker.name}`: {error}")

        # Create response
        if not stolen_emojis and not stolen_stickers:
            error_msg = "❌ Failed to steal any emojis or stickers!\n\n**Errors:**\n" + "\n".join(failed_items[:5])
            await processing_msg.edit(content=error_msg)
            return

        embed = discord.Embed(
            title="🎭 Steal Results",
            color=0x00ff00 if not failed_items else 0xffaa00
        )

        if stolen_emojis:
            emoji_text = ""
            for emoji in stolen_emojis[:10]:  # Limit display
                emoji_text += f"{emoji} `:{emoji.name}:`\n"
            if len(stolen_emojis) > 10:
                emoji_text += f"... and {len(stolen_emojis) - 10} more"

            embed.add_field(
                name=f"✅ Stolen Emojis ({len(stolen_emojis)})",
                value=emoji_text,
                inline=False
            )

        if stolen_stickers:
            sticker_text = ""
            for sticker in stolen_stickers[:5]:  # Limit display
                sticker_text += f"`{sticker.name}`\n"
            if len(stolen_stickers) > 5:
                sticker_text += f"... and {len(stolen_stickers) - 5} more"

            embed.add_field(
                name=f"✅ Stolen Stickers ({len(stolen_stickers)})",
                value=sticker_text,
                inline=False
            )

        if failed_items:
            failed_text = "\n".join(failed_items[:5])  # Limit display
            if len(failed_items) > 5:
                failed_text += f"\n... and {len(failed_items) - 5} more failures"

            embed.add_field(
                name=f"❌ Failed ({len(failed_items)})",
                value=failed_text,
                inline=False
            )

        await processing_msg.edit(content="", embed=embed)

    @commands.command(
        name="scanemojis",
        description="Debug command to scan for emojis in a message",
        usage="Reply to a message"
    )
    @Perms.get_perms("manage_emojis")
    async def scan_emojis(self, ctx):
        """Debug command to scan for emojis"""
        if not ctx.message.reference:
            return await ctx.send_warning("Please reply to a message!")

        try:
            replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
        except:
            return await ctx.send_warning("Could not fetch the replied message!")

        debug_info = []

        # Check message content
        if replied_message.content:
            debug_info.append(f"**Message Content:**\n```{replied_message.content[:500]}```")

            # Find emojis
            emoji_pattern = r'<(a?):([a-zA-Z0-9_]+):(\d+)>'
            matches = re.findall(emoji_pattern, replied_message.content)

            if matches:
                emoji_list = []
                for animated_flag, emoji_name, emoji_id in matches:
                    animated = "animated" if animated_flag else "static"
                    emoji_list.append(f"`{emoji_name}` (ID: {emoji_id}, {animated})")
                debug_info.append(f"**Found Emojis in Content:**\n" + "\n".join(emoji_list))
            else:
                debug_info.append("**No emojis found in content**")

        # Check embeds
        if replied_message.embeds:
            debug_info.append(f"**Found {len(replied_message.embeds)} embed(s)**")

            for i, embed in enumerate(replied_message.embeds):
                embed_content = ""
                if embed.title:
                    embed_content += f"Title: {embed.title}\n"
                if embed.description:
                    embed_content += f"Description: {embed.description[:200]}...\n"

                # Show fields content
                if embed.fields:
                    embed_content += f"Fields ({len(embed.fields)}):\n"
                    for j, field in enumerate(embed.fields):
                        embed_content += f"  Field {j+1}: {field.name} = {field.value[:100]}...\n"

                debug_info.append(f"**Embed {i+1}:**\n```{embed_content[:500]}```")

                # Check for emojis in embed (including fields!)
                full_embed_text = ""
                if embed.title:
                    full_embed_text += embed.title + " "
                if embed.description:
                    full_embed_text += embed.description + " "
                if embed.footer and embed.footer.text:
                    full_embed_text += embed.footer.text + " "
                if embed.author and embed.author.name:
                    full_embed_text += embed.author.name + " "

                # Include fields in emoji search
                for field in embed.fields:
                    if field.name:
                        full_embed_text += field.name + " "
                    if field.value:
                        full_embed_text += field.value + " "

                emoji_pattern = r'<(a?):([a-zA-Z0-9_]+):(\d+)>'
                matches = re.findall(emoji_pattern, full_embed_text)

                if matches:
                    emoji_list = []
                    for animated_flag, emoji_name, emoji_id in matches:
                        animated = "animated" if animated_flag else "static"
                        emoji_list.append(f"`{emoji_name}` (ID: {emoji_id}, {animated})")
                    debug_info.append(f"**Emojis in Embed {i+1}:**\n" + "\n".join(emoji_list))
                else:
                    debug_info.append(f"**No emojis found in Embed {i+1}**")

        # Check stickers
        if replied_message.stickers:
            sticker_list = []
            for sticker in replied_message.stickers:
                sticker_list.append(f"`{sticker.name}` (ID: {sticker.id})")
            debug_info.append(f"**Found Stickers:**\n" + "\n".join(sticker_list))

        if not debug_info:
            debug_info.append("**No content found in message**")

        # Send debug info in chunks
        full_debug = "\n\n".join(debug_info)

        if len(full_debug) > 2000:
            # Split into multiple messages
            chunks = [full_debug[i:i+1900] for i in range(0, len(full_debug), 1900)]
            for i, chunk in enumerate(chunks):
                if i == 0:
                    await ctx.send(f"🔍 **Debug Info (Part {i+1}/{len(chunks)}):**\n{chunk}")
                else:
                    await ctx.send(f"**Part {i+1}/{len(chunks)}:**\n{chunk}")
        else:
            await ctx.send(f"🔍 **Debug Info:**\n{full_debug}")

    @commands.command(
        name="emojilimits",
        description="Check current emoji usage and limits",
        aliases=["elimit"]
    )
    @Perms.get_perms("manage_emojis")
    async def emoji_limits(self, ctx):
        """Check emoji limits and current usage"""
        # Calculate limits based on boost level
        if ctx.guild.premium_tier == 0:
            emoji_limit = 50  # No boost
        elif ctx.guild.premium_tier == 1:
            emoji_limit = 100  # Level 1 boost
        elif ctx.guild.premium_tier == 2:
            emoji_limit = 150  # Level 2 boost
        elif ctx.guild.premium_tier >= 3:
            emoji_limit = 250  # Level 3 boost

        # Count current emojis
        static_emojis = [e for e in ctx.guild.emojis if not e.animated]
        animated_emojis = [e for e in ctx.guild.emojis if e.animated]

        embed = discord.Embed(
            title="📊 Emoji Usage & Limits",
            color=self.bot.color
        )

        embed.add_field(
            name="Server Info",
            value=f"**Boost Level:** {ctx.guild.premium_tier}\n**Emoji Limit:** {emoji_limit} each type",
            inline=False
        )

        embed.add_field(
            name="Static Emojis",
            value=f"**Used:** {len(static_emojis)}/{emoji_limit}\n**Available:** {emoji_limit - len(static_emojis)}",
            inline=True
        )

        embed.add_field(
            name="Animated Emojis",
            value=f"**Used:** {len(animated_emojis)}/{emoji_limit}\n**Available:** {emoji_limit - len(animated_emojis)}",
            inline=True
        )

        # Show if limits are reached
        if len(static_emojis) >= emoji_limit:
            embed.add_field(
                name="⚠️ Static Emoji Limit Reached",
                value="Cannot add more static emojis!",
                inline=False
            )

        if len(animated_emojis) >= emoji_limit:
            embed.add_field(
                name="⚠️ Animated Emoji Limit Reached",
                value="Cannot add more animated emojis!",
                inline=False
            )

        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Steal(bot))
