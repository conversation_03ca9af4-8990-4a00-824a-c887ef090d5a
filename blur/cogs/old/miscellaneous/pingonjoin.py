import discord
from discord.ext import commands
from tools.checks import Perms
import asyncio
import re


class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def parse_poj_arguments(self, args_string: str):
        """Parse ping-on-join arguments for custom text and --seconds parameter"""
        if not args_string:
            return None, 5  # Default: no custom text, 5 seconds

        # Extract --seconds parameter using regex
        seconds_match = re.search(r'--(\d+)', args_string)
        delete_seconds = 5  # Default

        if seconds_match:
            seconds_value = int(seconds_match.group(1))
            # Apply constraints: min 1 second, max 60 seconds
            delete_seconds = max(1, min(60, seconds_value))
            # Remove --seconds from the string
            args_string = re.sub(r'--\d+', '', args_string).strip()

        # What remains is the custom text (if any)
        custom_text = args_string.strip() if args_string.strip() else None

        return custom_text, delete_seconds

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle ping on join with custom text and configurable deletion timing"""
        try:
            # Get ping channels with their custom settings for this guild
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id, custom_text, delete_seconds FROM ping_on_join_channels WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    ping_channels = await cursor.fetchall()

            if not ping_channels:
                return

            # Send ping message to each channel with custom settings
            for channel_data in ping_channels:
                channel = self.bot.get_channel(channel_data[0])
                if channel:
                    try:
                        custom_text = channel_data[1] if len(channel_data) > 1 else None
                        delete_seconds = channel_data[2] if len(channel_data) > 2 and channel_data[2] else 5

                        # Prepare message content
                        if custom_text:
                            # Replace {user.mention} placeholder with actual mention
                            message_content = custom_text.replace("{user.mention}", member.mention)
                        else:
                            # Default behavior: just the mention
                            message_content = member.mention

                        # Send the message
                        message = await channel.send(message_content)

                        # Auto-delete after specified seconds
                        await asyncio.sleep(delete_seconds)
                        await message.delete()
                    except Exception as e:
                        print(f"Error sending/deleting ping message in {channel.name}: {e}")

        except Exception as e:
            print(f"Ping on join error: {e}")

    @commands.group(
        name="pingonjoin",
        description="Ping on join system",
        invoke_without_command=True,
        aliases=["poj"]
    )
    @Perms.get_perms("manage_guild")
    async def pingonjoin(self, ctx):
        """Ping on join system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="📢 Ping on Join System",
                description="Ping users in channels when someone joins with customizable messages and auto-deletion",
                color=self.bot.color
            )

            embed.add_field(
                name="Commands",
                value="`poj add #channel [custom_text] [--seconds]` - Add channel for pings (max 5)\n"
                      "`poj remove #channel` - Remove channel from pings\n"
                      "`poj list` - List all ping channels with settings\n"
                      "`poj reset` - Remove all ping channels",
                inline=False
            )

            embed.add_field(
                name="Enhanced Features",
                value="• **Custom Text:** Include custom message with `{user.mention}` placeholder\n"
                      "• **Auto-deletion:** Configure timing with `--seconds` (1-60 seconds, default: 5)\n"
                      "• **Backward Compatible:** Old format still works\n"
                      "• **Maximum:** 5 channels per server",
                inline=False
            )

            embed.add_field(
                name="Examples",
                value="`poj add #general` - Just ping (default 5s deletion)\n"
                      "`poj add #welcome Welcome {user.mention}!` - Custom text (5s deletion)\n"
                      "`poj add #lobby Hello {user.mention} --20` - Custom text + 20s deletion\n"
                      "`poj add #general {user.mention} enjoy! --100` - Capped at 60s deletion",
                inline=False
            )

            await ctx.reply(embed=embed)

    @pingonjoin.command(
        name="add",
        description="Add a channel for ping on join",
        usage="#channel [custom_text] [--seconds]"
    )
    @Perms.get_perms("manage_guild")
    async def poj_add(self, ctx, channel: discord.TextChannel, *, args: str = None):
        """Add a channel for ping on join with optional custom text and deletion timing"""
        # Parse arguments for custom text and deletion timing
        custom_text, delete_seconds = self.parse_poj_arguments(args)

        # Check current count
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM ping_on_join_channels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                count = await cursor.fetchone()

        if count[0] >= 5:
            return await ctx.send_warning("You can only have up to **5** ping channels!")

        # Check if channel already exists
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM ping_on_join_channels WHERE guild_id = %s AND channel_id = %s",
                    (ctx.guild.id, channel.id)
                )
                existing = await cursor.fetchone()

        if existing:
            return await ctx.send_warning(f"{channel.mention} is already added to ping on join!")

        # Add channel with custom settings
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO ping_on_join_channels (guild_id, channel_id, custom_text, delete_seconds) VALUES (%s, %s, %s, %s)",
                    (ctx.guild.id, channel.id, custom_text, delete_seconds)
                )

        # Create success message with details
        success_msg = f"Added {channel.mention} to ping on join! ({count[0] + 1}/5 channels)"
        if custom_text:
            success_msg += f"\n**Custom text:** {custom_text}"
        success_msg += f"\n**Auto-delete:** {delete_seconds} seconds"

        await ctx.send_success(success_msg)

    @pingonjoin.command(
        name="remove",
        description="Remove a channel from ping on join",
        usage="#channel"
    )
    @Perms.get_perms("manage_guild")
    async def poj_remove(self, ctx, channel: discord.TextChannel):
        """Remove a channel from ping on join"""
        # Check if channel exists
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM ping_on_join_channels WHERE guild_id = %s AND channel_id = %s",
                    (ctx.guild.id, channel.id)
                )
                existing = await cursor.fetchone()

        if not existing:
            return await ctx.send_warning(f"{channel.mention} is not in the ping on join list!")

        # Remove channel
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM ping_on_join_channels WHERE guild_id = %s AND channel_id = %s",
                    (ctx.guild.id, channel.id)
                )

        await ctx.send_success(f"Removed {channel.mention} from ping on join!")

    @pingonjoin.command(
        name="list",
        description="List all ping on join channels with their settings"
    )
    @Perms.get_perms("manage_guild")
    async def poj_list(self, ctx):
        """List all ping on join channels with their custom settings"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT channel_id, custom_text, delete_seconds FROM ping_on_join_channels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                channels = await cursor.fetchall()

        if not channels:
            return await ctx.send_warning("No ping on join channels configured!")

        embed = discord.Embed(
            title="📢 Ping on Join Channels",
            color=self.bot.color
        )

        channel_list = []
        for channel_data in channels:
            channel = self.bot.get_channel(channel_data[0])
            custom_text = channel_data[1] if len(channel_data) > 1 else None
            delete_seconds = channel_data[2] if len(channel_data) > 2 and channel_data[2] else 5

            if channel:
                channel_info = f"• {channel.mention}"
                if custom_text:
                    # Truncate long custom text for display
                    display_text = custom_text[:50] + "..." if len(custom_text) > 50 else custom_text
                    channel_info += f"\n  **Text:** {display_text}"
                else:
                    channel_info += f"\n  **Text:** Default ping only"
                channel_info += f"\n  **Auto-delete:** {delete_seconds}s"
                channel_list.append(channel_info)
            else:
                channel_list.append(f"• <#{channel_data[0]}> (deleted)")

        embed.description = "\n\n".join(channel_list)
        embed.set_footer(text=f"{len(channels)}/5 channels configured")

        await ctx.reply(embed=embed)

    @pingonjoin.command(
        name="reset",
        description="Remove all ping on join channels"
    )
    @Perms.get_perms("manage_guild")
    async def poj_reset(self, ctx):
        """Remove all ping on join channels"""
        # Check if any channels exist
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM ping_on_join_channels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                count = await cursor.fetchone()

        if count[0] == 0:
            return await ctx.send_warning("No ping on join channels to remove!")

        # Remove all channels
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM ping_on_join_channels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )

        await ctx.send_success(f"Removed all **{count[0]}** ping on join channels!")


async def setup(bot):
    await bot.add_cog(PingOnJoin(bot))
