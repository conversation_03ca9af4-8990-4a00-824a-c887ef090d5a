import discord, datetime, asyncio, traceback, textwrap, io, contextlib
from discord import Embed
from discord.ext import commands
from tools.checks import Owners
from cogs.old.auth import owners


class Owner(commands.Cog):
    __is_hidden_event__ = True

    def __init__(self, bot: commands.AutoShardedBot):
        self.bot = bot
        self._last_result = None

    def cleanup_code(self, content):
        """Automatically removes code blocks from the code."""
        # remove ```py\n```
        if content.startswith('```') and content.endswith('```'):
            return '\n'.join(content.split('\n')[1:-1])

        # remove `foo`
        return content.strip('` \n')

    @commands.command(
        name="cmdlogs",
        description="View recent command usage logs",
        usage="[limit]",
        hidden=True
    )
    @commands.is_owner()
    async def command_logs(self, ctx, limit: int = 10):
        """View recent command usage logs"""
        if limit > 50:
            limit = 50

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """SELECT command_name, user_id, guild_id, used_at
                           FROM command_usage
                           ORDER BY used_at DESC
                           LIMIT %s""",
                        (limit,)
                    )
                    results = await cursor.fetchall()

            if not results:
                return await ctx.send_warning("No command usage logs found.")

            embed = discord.Embed(
                title=f"Recent Command Usage (Last {len(results)})",
                color=self.bot.color,
                timestamp=datetime.datetime.utcnow()
            )

            log_text = ""
            for command_name, user_id, guild_id, used_at in results:
                user = self.bot.get_user(user_id)
                guild = self.bot.get_guild(guild_id)
                user_name = user.name if user else f"Unknown ({user_id})"
                guild_name = guild.name if guild else f"Unknown ({guild_id})"

                log_text += f"`{used_at}` - **{command_name}** by {user_name} in {guild_name}\n"

            embed.description = log_text[:4000]  # Discord embed limit
            await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"Error fetching command logs: {e}")

    @commands.command(
        name="eval",
        description="Evaluate Python code",
        usage="[code]",
        hidden=True
    )
    @commands.is_owner()
    async def eval_command(self, ctx, *, body: str):
        """Evaluate Python code"""
        env = {
            'bot': self.bot,
            'ctx': ctx,
            'channel': ctx.channel,
            'author': ctx.author,
            'guild': ctx.guild,
            'message': ctx.message,
            '_': self._last_result
        }

        env.update(globals())

        body = self.cleanup_code(body)
        stdout = io.StringIO()

        to_compile = f'async def func():\n{textwrap.indent(body, "  ")}'

        try:
            exec(to_compile, env)
        except Exception as e:
            return await ctx.send(f'```py\n{e.__class__.__name__}: {e}\n```')

        func = env['func']
        try:
            with contextlib.redirect_stdout(stdout):
                ret = await func()
        except Exception as e:
            value = stdout.getvalue()
            await ctx.send(f'```py\n{value}{traceback.format_exc()}\n```')
        else:
            value = stdout.getvalue()
            try:
                await ctx.message.add_reaction('✅')
            except:
                pass

            if ret is None:
                if value:
                    await ctx.send(f'```py\n{value}\n```')
            else:
                self._last_result = ret
                await ctx.send(f'```py\n{value}{ret}\n```')

    @commands.group(invoke_without_command=True)
    @Owners.check_owners()
    async def donor(self, ctx):
        await ctx.create_pages()

    @donor.command()
    @Owners.check_owners()
    async def add(self, ctx: commands.Context, *, member: discord.User):
        check = await self.bot.db.fetchrow(
            "SELECT * FROM donor WHERE user_id = $1", member.id
        )
        if check:
            return await ctx.send_warning(f"**{member}** is already a donor")
        ts = int(datetime.datetime.now().timestamp())
        await self.bot.db.execute("INSERT INTO donor VALUES ($1,$2)", member.id, ts)
        return await ctx.send_success(f"**{member}** is now a donor")

    @donor.command()
    @Owners.check_owners()
    async def remove(self, ctx: commands.Context, *, member: discord.User):
        check = await self.bot.db.fetchrow(
            "SELECT * FROM donor WHERE user_id = $1", member.id
        )
        if not check:
            return await ctx.send_warning(f"**{member}** is not a donor")
        await self.bot.db.execute("DELETE FROM donor WHERE user_id = $1", member.id)
        return await ctx.send_success(f"**{member}** is not a donor anymore")

    @donor.command()
    @Owners.check_owners()
    async def list(self, ctx):
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM donor")
                    results = await cursor.fetchall()

            if len(results) == 0:
                return await ctx.send_error("There are no donators")

            i = 0
            k = 1
            l = 0
            mes = ""
            number = []
            messages = []

            for result in results:
                user_id, time_added = result
                mes = f"{mes}`{k}` <@!{user_id}> ({user_id}) - (<t:{int(time_added)}:R>)\n"
                k += 1
                l += 1
                if l == 10:
                    messages.append(mes)
                    number.append(
                        Embed(
                            color=self.bot.color,
                            title=f"donators ({len(results)})",
                            description=messages[i],
                        )
                    )
                    i += 1
                    mes = ""
                    l = 0

            messages.append(mes)
            number.append(
                Embed(
                    color=self.bot.color,
                    title=f"donators ({len(results)})",
                    description=messages[i],
                )
            )
            await ctx.paginator(number)

        except Exception as e:
            await ctx.send_error(f"Error fetching donors: {e}")

    @commands.command(aliases=["servers"])
    @Owners.check_owners()
    async def guilds(self, ctx: commands.Context):
        def key(s):
            return s.member_count

        i = 0
        k = 1
        l = 0
        mes = ""
        number = []
        messages = []
        lis = [g for g in self.bot.guilds]
        lis.sort(reverse=True, key=key)
        for guild in lis:
            mes = f"{mes}`{k}` {guild.name} ({guild.id}) - ({guild.member_count})\n"
            k += 1
            l += 1
            if l == 10:
                messages.append(mes)
                number.append(
                    Embed(
                        color=self.bot.color,
                        title=f"guilds ({len(lis)})",
                        description=messages[i],
                    )
                )
                i += 1
                mes = ""
                l = 0

        messages.append(mes)
        number.append(
            Embed(
                color=self.bot.color,
                title=f"guilds ({len(lis)})",
                description=messages[i],
            )
        )
        await ctx.paginator(number)

    @commands.group(invoke_without_command=True)
    @Owners.check_owners()
    async def whitelist(self, ctx):
        """Server whitelist management"""
        await ctx.create_pages()

    @whitelist.command(name="add")
    @Owners.check_owners()
    async def whitelist_add(self, ctx: commands.Context, guild_id: int):
        """Add a server to the whitelist"""
        try:
            # Validate guild ID format (Discord IDs are 17-19 digits)
            if len(str(guild_id)) < 17 or len(str(guild_id)) > 19:
                return await ctx.send_error(f"Invalid server ID format: `{guild_id}`")

            # Check if already whitelisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )
                    check = await cursor.fetchone()

            if check:
                return await ctx.send_warning(f"Server `{guild_id}` is already whitelisted")

            # Just add the server ID to whitelist - no complex validation needed

            # Add to whitelist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO server_whitelist (guild_id, added_at) VALUES (%s, %s)",
                        (guild_id, int(datetime.datetime.now().timestamp()))
                    )

            await ctx.send_success(f"Server `{guild_id}` has been whitelisted")

        except ValueError:
            await ctx.send_error(f"Invalid server ID: `{guild_id}` must be a number")
        except Exception as e:
            await ctx.send_error(f"Error adding server to whitelist: {e}")

    @whitelist.command(name="remove")
    @Owners.check_owners()
    async def whitelist_remove(self, ctx: commands.Context, guild_id: int):
        """Remove a server from the whitelist"""
        try:
            # Check if whitelisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                return await ctx.send_warning(f"Server `{guild_id}` is not whitelisted")

            # Get guild info
            guild = self.bot.get_guild(guild_id)
            guild_name = guild.name if guild else "Unknown Server"

            # Remove from whitelist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM server_whitelist WHERE guild_id = %s", (guild_id,)
                    )

            await ctx.send_success(f"Removed **{guild_name}** (`{guild_id}`) from the whitelist")

            # Leave the server if bot is still in it
            if guild:
                try:
                    await guild.leave()
                    await ctx.send_success(f"Left **{guild_name}** as it's no longer whitelisted")
                except:
                    pass

        except Exception as e:
            await ctx.send_error(f"Error removing server from whitelist: {e}")

    @whitelist.command(name="list")
    @Owners.check_owners()
    async def whitelist_list(self, ctx):
        """List all whitelisted servers"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM server_whitelist ORDER BY added_at DESC")
                    results = await cursor.fetchall()

            if not results:
                return await ctx.send_error("No servers are whitelisted")

            i = 0
            k = 1
            l = 0
            mes = ""
            number = []
            messages = []

            for result in results:
                guild_id, _ = result  # added_at not needed
                guild = self.bot.get_guild(guild_id)

                if guild:
                    guild_name = guild.name
                    member_count = guild.member_count
                    mes += f"`{k}` **{guild_name}**(`{guild_id}`) - **{member_count}** Members\n"
                else:
                    mes += f"`{k}` **Invalid ID**(`{guild_id}`)\n"

                k += 1
                l += 1

                if l == 10:  # Back to 10 since entries are shorter now
                    messages.append(mes)
                    number.append(
                        Embed(
                            color=self.bot.color,
                            title=f"Whitelisted Servers ({len(results)})",
                            description=messages[i],
                        )
                    )
                    i += 1
                    mes = ""
                    l = 0

            if mes:  # Add remaining items
                messages.append(mes)
                number.append(
                    Embed(
                        color=self.bot.color,
                        title=f"Whitelisted Servers ({len(results)})",
                        description=messages[i],
                    )
                )

            await ctx.paginator(number)

        except Exception as e:
            await ctx.send_error(f"Error fetching whitelist: {e}")

    @commands.group(invoke_without_command=True)
    @Owners.check_owners()
    async def blacklist(self, ctx):
        """User blacklist management"""
        await ctx.create_pages()

    @blacklist.command(name="add")
    @Owners.check_owners()
    async def blacklist_add(self, ctx: commands.Context, user: discord.User, *, reason: str = "No reason provided"):
        """Add a user to the blacklist"""
        try:
            # Check if already blacklisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                return await ctx.send_warning(f"**{user}** is already blacklisted")

            # Add to blacklist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO blacklist (user_id, reason) VALUES (%s, %s)",
                        (user.id, reason)
                    )

            await ctx.send_success(f"Added **{user}** (`{user.id}`) to the blacklist\n**Reason:** {reason}")

        except Exception as e:
            await ctx.send_error(f"Error adding user to blacklist: {e}")

    @blacklist.command(name="remove")
    @Owners.check_owners()
    async def blacklist_remove(self, ctx: commands.Context, user: discord.User):
        """Remove a user from the blacklist"""
        try:
            # Check if blacklisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                return await ctx.send_warning(f"**{user}** is not blacklisted")

            # Remove from blacklist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM blacklist WHERE user_id = %s", (user.id,)
                    )

            await ctx.send_success(f"Removed **{user}** (`{user.id}`) from the blacklist")

        except Exception as e:
            await ctx.send_error(f"Error removing user from blacklist: {e}")

    @blacklist.command(name="list")
    @Owners.check_owners()
    async def blacklist_list(self, ctx):
        """List all blacklisted users"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM blacklist")
                    results = await cursor.fetchall()

            if not results:
                return await ctx.send_error("No users are blacklisted")

            i = 0
            k = 1
            l = 0
            mes = ""
            number = []
            messages = []

            for result in results:
                user_id, reason = result
                user = self.bot.get_user(user_id)
                user_name = user.name if user else f"Unknown User"

                mes += f"`{k}` **{user_name}** (`{user_id}`)\n**Reason:** {reason}\n\n"
                k += 1
                l += 1

                if l == 5:  # 5 per page for blacklist (more detailed)
                    messages.append(mes)
                    number.append(
                        Embed(
                            color=self.bot.color,
                            title=f"Blacklisted Users ({len(results)})",
                            description=messages[i],
                        )
                    )
                    i += 1
                    mes = ""
                    l = 0

            if mes:  # Add remaining items
                messages.append(mes)
                number.append(
                    Embed(
                        color=self.bot.color,
                        title=f"Blacklisted Users ({len(results)})",
                        description=messages[i],
                    )
                )

            await ctx.paginator(number)

        except Exception as e:
            await ctx.send_error(f"Error fetching blacklist: {e}")

    @blacklist.command(name="check")
    @Owners.check_owners()
    async def blacklist_check(self, ctx: commands.Context, user: discord.User):
        """Check if a user is blacklisted"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                _, reason = check
                await ctx.send_warning(f"**{user}** (`{user.id}`) is blacklisted\n**Reason:** {reason}")
            else:
                await ctx.send_success(f"**{user}** (`{user.id}`) is not blacklisted")

        except Exception as e:
            await ctx.send_error(f"Error checking blacklist: {e}")

    @commands.command(description="reload a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def reload(self, ctx, *, cog: str):
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.reload_extension(cog_name)
            await ctx.send_success(f"Successfully reloaded `{cog}`")
        except Exception as e:
            await ctx.send_error(f"Failed to reload `{cog}`: {e}")

    @commands.command(description="load a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def load(self, ctx, *, cog: str):
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.load_extension(cog_name)
            await ctx.send_success(f"Successfully loaded `{cog}`")
        except Exception as e:
            await ctx.send_error(f"Failed to load `{cog}`: {e}")

    @commands.command(description="unload a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def unload(self, ctx, *, cog: str):
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.unload_extension(cog_name)
            await ctx.send_success(f"Successfully unloaded `{cog}`")
        except Exception as e:
            await ctx.send_error(f"Failed to unload `{cog}`: {e}")

    @commands.command(description="shutdown the bot", hidden=True)
    @commands.is_owner()
    async def shutdown(self, ctx):
        embed = discord.Embed(
            title="Shutting Down",
            description="Bot is shutting down...",
            color=0xff0000
        )
        await ctx.send(embed=embed)
        await self.bot.close()

    @commands.command(description="sync slash commands", hidden=True)
    @commands.is_owner()
    async def sync(self, ctx):
        try:
            synced = await self.bot.tree.sync()
            await ctx.send_success(f"Synced {len(synced)} slash commands")
        except Exception as e:
            await ctx.send_error(f"Failed to sync commands: {e}")

    @commands.command(description="leave a guild", usage="[guild_id]", hidden=True)
    @commands.is_owner()
    async def leave(self, ctx, guild_id: int = None):
        """Leave a guild by ID"""
        if guild_id is None:
            return await ctx.send_warning("Please provide a guild ID to leave!")

        # Validate guild ID
        if guild_id <= 0:
            return await ctx.send_error("Invalid guild ID!")

        guild = self.bot.get_guild(guild_id)
        if not guild:
            return await ctx.send_error(f"Guild with ID `{guild_id}` not found! Make sure the bot is in that server.")

        # Show guild info before leaving
        embed = discord.Embed(
            title="⚠️ Confirm Guild Leave",
            description=f"Are you sure you want to leave **{guild.name}**?",
            color=0xffaa00
        )
        embed.add_field(name="Guild ID", value=str(guild.id), inline=True)
        embed.add_field(name="Members", value=str(guild.member_count), inline=True)
        embed.add_field(name="Owner", value=str(guild.owner), inline=True)
        embed.set_thumbnail(url=guild.icon.url if guild.icon else None)

        # Add confirmation buttons
        view = discord.ui.View(timeout=30)

        async def confirm_callback(interaction):
            if interaction.user.id != ctx.author.id:
                return await interaction.response.send_message("Only the command author can use this button!", ephemeral=True)

            try:
                guild_name = guild.name
                await guild.leave()

                success_embed = discord.Embed(
                    title="✅ Left Guild",
                    description=f"Successfully left **{guild_name}** (`{guild.id}`)",
                    color=0x00ff00
                )
                await interaction.response.edit_message(embed=success_embed, view=None)

            except discord.HTTPException as e:
                error_embed = discord.Embed(
                    title="❌ Failed to Leave Guild",
                    description=f"Failed to leave **{guild.name}**: {str(e)}",
                    color=0xff0000
                )
                await interaction.response.edit_message(embed=error_embed, view=None)
            except Exception as e:
                error_embed = discord.Embed(
                    title="❌ Unexpected Error",
                    description=f"An unexpected error occurred: {str(e)}",
                    color=0xff0000
                )
                await interaction.response.edit_message(embed=error_embed, view=None)

        async def cancel_callback(interaction):
            if interaction.user.id != ctx.author.id:
                return await interaction.response.send_message("Only the command author can use this button!", ephemeral=True)

            cancel_embed = discord.Embed(
                title="❌ Cancelled",
                description="Guild leave cancelled.",
                color=0x808080
            )
            await interaction.response.edit_message(embed=cancel_embed, view=None)

        confirm_button = discord.ui.Button(label="Confirm Leave", style=discord.ButtonStyle.danger, emoji="✅")
        cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary, emoji="❌")

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback

        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await ctx.reply(embed=embed, view=view)

    @commands.command(description="execute command as another user", usage="[user] [command]", hidden=True)
    @commands.is_owner()
    async def sudo(self, ctx, user: discord.Member, *, command: str):
        """Execute a command as another user"""
        # Create a fake context
        fake_message = ctx.message
        fake_message.author = user
        fake_message.content = ctx.prefix + command

        new_ctx = await self.bot.get_context(fake_message)

        if new_ctx.command is None:
            return await ctx.send_error("Command not found!")

        try:
            await new_ctx.command.invoke(new_ctx)
            await ctx.message.add_reaction(ctx.bot.yes)
        except Exception as e:
            await ctx.send_error(f"Error executing command: {e}")

    @commands.command(description="trace command execution", usage="[command]", hidden=True)
    @commands.is_owner()
    async def trace(self, ctx, *, command: str):
        """Trace command execution with detailed logging"""
        import traceback
        import sys
        from io import StringIO

        # Capture stdout
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()

        try:
            # Execute the command
            fake_message = ctx.message
            fake_message.content = ctx.prefix + command
            new_ctx = await self.bot.get_context(fake_message)

            if new_ctx.command is None:
                return await ctx.send_error("Command not found!")

            await new_ctx.command.invoke(new_ctx)

            # Get captured output
            output = captured_output.getvalue()

            embed = discord.Embed(
                title="🔍 Command Trace",
                color=0x00ff00
            )

            embed.add_field(
                name="Command",
                value=f"`{command}`",
                inline=False
            )

            embed.add_field(
                name="User",
                value=f"{ctx.author} ({ctx.author.id})",
                inline=True
            )

            embed.add_field(
                name="Guild",
                value=f"{ctx.guild.name} ({ctx.guild.id})" if ctx.guild else "DM",
                inline=True
            )

            if output:
                embed.add_field(
                    name="Output",
                    value=f"```\n{output[:1000]}\n```",
                    inline=False
                )

            embed.add_field(
                name="Status",
                value=f"{self.bot.yes} Success",
                inline=False
            )

            await ctx.reply(embed=embed)

        except Exception as e:
            # Get traceback
            tb = traceback.format_exc()

            embed = discord.Embed(
                title="🔍 Command Trace",
                color=0xff0000
            )

            embed.add_field(
                name="Command",
                value=f"`{command}`",
                inline=False
            )

            embed.add_field(
                name="Status",
                value=f"{self.bot.no} Error",
                inline=False
            )

            embed.add_field(
                name="Error",
                value=f"```py\n{str(e)[:1000]}\n```",
                inline=False
            )

            if len(tb) < 1000:
                embed.add_field(
                    name="Traceback",
                    value=f"```py\n{tb}\n```",
                    inline=False
                )

            await ctx.reply(embed=embed)

        finally:
            # Restore stdout
            sys.stdout = old_stdout


async def setup(bot):
    await bot.add_cog(Owner(bot))
