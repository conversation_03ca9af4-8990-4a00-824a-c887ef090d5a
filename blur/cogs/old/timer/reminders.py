import discord
from discord.ext import commands, tasks
import datetime
import asyncio
import humanfriendly


class Reminders(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.reminder_task.start()

    def cog_unload(self):
        self.reminder_task.cancel()

    @tasks.loop(seconds=30)
    async def reminder_task(self):
        """Check for due reminders"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM reminders WHERE remind_time <= %s AND sent = FALSE",
                        datetime.datetime.utcnow()
                    )
                    due_reminders = await cursor.fetchall()
                    
                    for reminder in due_reminders:
                        await self.send_reminder(reminder)
                        
                        # Mark as sent
                        await cursor.execute(
                            "UPDATE reminders SET sent = TRUE WHERE id = %s",
                            reminder[0]
                        )
        except Exception as e:
            print(f"Reminder task error: {e}")

    @reminder_task.before_loop
    async def before_reminder_task(self):
        await self.bot.wait_until_ready()

    async def send_reminder(self, reminder):
        """Send a reminder to the user"""
        user_id, guild_id, channel_id, message, remind_time, created_at = reminder[1:7]
        
        user = self.bot.get_user(user_id)
        if not user:
            return
        
        embed = discord.Embed(
            title="⏰ Reminder",
            description=message,
            color=0x3498db,
            timestamp=remind_time
        )
        embed.set_footer(text=f"Set {humanfriendly.format_timespan(datetime.datetime.utcnow() - created_at)} ago")
        
        # Try to send in original channel first
        if channel_id:
            channel = self.bot.get_channel(channel_id)
            if channel:
                try:
                    await channel.send(f"{user.mention}", embed=embed)
                    return
                except discord.Forbidden:
                    pass
        
        # Fall back to DM
        try:
            await user.send(embed=embed)
        except discord.Forbidden:
            pass

    @commands.group(
        name="remind",
        description="Reminder system",
        invoke_without_command=True,
        aliases=["reminder"]
    )
    async def remind(self, ctx):
        """Reminder system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="⏰ Reminder System",
                description="Set and manage personal reminders",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`remind me [time] [message]` - Set a reminder\n"
                      "`remind list` - List your reminders\n"
                      "`remind delete [id]` - Delete a reminder\n"
                      "`remind clear` - Clear all your reminders",
                inline=False
            )
            
            embed.add_field(
                name="Time Examples",
                value="`1h` - 1 hour\n"
                      "`30m` - 30 minutes\n"
                      "`1d` - 1 day\n"
                      "`2h30m` - 2 hours 30 minutes",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @remind.command(
        name="me",
        description="Set a reminder",
        usage="[time] [message]"
    )
    async def remind_me(self, ctx, time: str, *, message: str):
        """Set a reminder"""
        if len(message) > 500:
            return await ctx.send_warning("Reminder message too long! Maximum 500 characters.")
        
        try:
            seconds = humanfriendly.parse_timespan(time)
        except humanfriendly.InvalidTimespan:
            return await ctx.send_warning("Invalid time format! Use formats like: 1h, 30m, 1d")
        
        if seconds < 60:  # Minimum 1 minute
            return await ctx.send_warning("Minimum reminder time is 1 minute!")
        
        if seconds > 31536000:  # Maximum 1 year
            return await ctx.send_warning("Maximum reminder time is 1 year!")
        
        # Check user reminder limit
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM reminders WHERE user_id = %s AND sent = FALSE",
                    ctx.author.id
                )
                count = (await cursor.fetchone())[0]
                
                if count >= 10:
                    return await ctx.send_warning("You can only have 10 active reminders at once!")
                
                # Create reminder
                remind_time = datetime.datetime.utcnow() + datetime.timedelta(seconds=seconds)
                
                await cursor.execute(
                    "INSERT INTO reminders (user_id, guild_id, channel_id, message, remind_time, created_at, sent) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                    ctx.author.id, ctx.guild.id, ctx.channel.id, message, remind_time, datetime.datetime.utcnow(), False
                )
                
                # Get the reminder ID
                await cursor.execute("SELECT LAST_INSERT_ID()")
                reminder_id = (await cursor.fetchone())[0]
        
        embed = discord.Embed(
            title="⏰ Reminder Set",
            description=f"I'll remind you about: **{message}**",
            color=0x00ff00
        )
        embed.add_field(
            name="When",
            value=f"<t:{int(remind_time.timestamp())}> (<t:{int(remind_time.timestamp())}:R>)",
            inline=False
        )
        embed.set_footer(text=f"Reminder ID: {reminder_id}")
        
        await ctx.reply(embed=embed)

    @remind.command(
        name="list",
        description="List your reminders"
    )
    async def remind_list(self, ctx):
        """List user's reminders"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM reminders WHERE user_id = %s AND sent = FALSE ORDER BY remind_time",
                    ctx.author.id
                )
                reminders = await cursor.fetchall()
                
                if not reminders:
                    return await ctx.send_warning("You have no active reminders!")
                
                embed = discord.Embed(
                    title=f"⏰ Your Reminders ({len(reminders)})",
                    color=self.bot.color
                )
                
                for reminder in reminders[:10]:
                    reminder_id, _, _, _, message, remind_time, created_at, _ = reminder
                    
                    embed.add_field(
                        name=f"ID: {reminder_id}",
                        value=f"**{message[:100]}{'...' if len(message) > 100 else ''}**\n"
                              f"Due: <t:{int(remind_time.timestamp())}:R>",
                        inline=False
                    )
                
                if len(reminders) > 10:
                    embed.set_footer(text=f"... and {len(reminders) - 10} more reminders")
                
                await ctx.reply(embed=embed)

    @remind.command(
        name="delete",
        description="Delete a reminder",
        usage="[id]",
        aliases=["remove"]
    )
    async def remind_delete(self, ctx, reminder_id: int):
        """Delete a reminder"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM reminders WHERE id = %s AND user_id = %s AND sent = FALSE",
                    reminder_id, ctx.author.id
                )
                reminder = await cursor.fetchone()
                
                if not reminder:
                    return await ctx.send_warning("Reminder not found or already sent!")
                
                await cursor.execute(
                    "DELETE FROM reminders WHERE id = %s",
                    reminder_id
                )
                
                await ctx.send_success(f"Deleted reminder: **{reminder[4]}**")

    @remind.command(
        name="clear",
        description="Clear all your reminders"
    )
    async def remind_clear(self, ctx):
        """Clear all user's reminders"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM reminders WHERE user_id = %s AND sent = FALSE",
                    ctx.author.id
                )
                count = (await cursor.fetchone())[0]
                
                if count == 0:
                    return await ctx.send_warning("You have no active reminders!")
                
                # Confirmation
                from config.constants import Emojis, Colors
                embed = discord.Embed(
                    title=f"{Emojis.warn} Confirm Clear",
                    description=f"Are you sure you want to delete all {count} of your reminders?",
                    color=Colors.error
                )
                
                view = discord.ui.View(timeout=30)
                
                async def confirm_callback(interaction):
                    if interaction.user.id != ctx.author.id:
                        return await interaction.response.send_message("This is not your confirmation!", ephemeral=True)
                    
                    async with self.bot.db.acquire() as conn2:
                        async with conn2.cursor() as cursor2:
                            await cursor2.execute(
                                "DELETE FROM reminders WHERE user_id = %s AND sent = FALSE",
                                ctx.author.id
                            )
                    
                    await interaction.response.edit_message(
                        content=f"✅ Deleted all {count} reminders!",
                        embed=None,
                        view=None
                    )
                
                async def cancel_callback(interaction):
                    if interaction.user.id != ctx.author.id:
                        return await interaction.response.send_message("This is not your confirmation!", ephemeral=True)
                    
                    await interaction.response.edit_message(
                        content=f"{Emojis.error} Cancelled!",
                        embed=None,
                        view=None
                    )
                
                confirm_button = discord.ui.Button(label="Confirm", style=discord.ButtonStyle.danger)
                cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary)
                
                confirm_button.callback = confirm_callback
                cancel_button.callback = cancel_callback
                
                view.add_item(confirm_button)
                view.add_item(cancel_button)
                
                await ctx.reply(embed=embed, view=view)

    @commands.command(
        name="remindme",
        description="Quick reminder command",
        usage="[time] [message]"
    )
    async def remindme(self, ctx, time: str, *, message: str):
        """Quick reminder command"""
        await ctx.invoke(self.remind_me, time=time, message=message)

    @commands.command(
        name="reminders",
        description="List your reminders"
    )
    async def reminders_list(self, ctx):
        """List reminders (alias)"""
        await ctx.invoke(self.remind_list)


async def setup(bot):
    await bot.add_cog(Reminders(bot))
