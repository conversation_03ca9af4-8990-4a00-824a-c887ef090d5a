import discord
from discord.ext import commands


class Events(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    # Removed old guild join handler to prevent duplicate welcome messages

    @commands.Cog.listener()
    async def on_guild_remove(self, guild):
        """Called when bot leaves a guild"""
        print(f"❌ Left guild: {guild.name} ({guild.id})")

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Called when a member joins a guild"""
        # Check for welcome system
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM welcome_config WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    welcome_data = await cursor.fetchone()
                
            if welcome_data and len(welcome_data) > 2 and welcome_data[2]:  # welcome_data[2] is enabled
                channel = self.bot.get_channel(welcome_data[1])  # welcome_data[1] is channel_id
                if channel:
                    # Replace variables in welcome message
                    message = welcome_data[3] if len(welcome_data) > 3 and welcome_data[3] else "Welcome {user} to {server}!"  # welcome_data[3] is message
                    message = message.replace("{user}", member.mention)
                    message = message.replace("{server}", member.guild.name)
                    message = message.replace("{count}", str(member.guild.member_count))

                    # Default to embed format
                    embed = discord.Embed(
                        description=message,
                        color=self.bot.color
                    )
                    embed.set_thumbnail(url=member.display_avatar.url)
                    await channel.send(embed=embed)
                        
        except Exception as e:
            print(f"Welcome system error: {e}")

    # Removed old goodbye system to prevent duplicate member leave logs
    # The new goodbye system is in blur/cogs/fixed/events/member.py

    @commands.Cog.listener()
    async def on_message_delete(self, message):
        """Called when a message is deleted"""
        if message.author.bot:
            return
        
        # Store deleted message for snipe command
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO snipe_messages (channel_id, author_id, content, deleted_at)
                           VALUES (%s, %s, %s, %s)
                           ON DUPLICATE KEY UPDATE
                           author_id = %s, content = %s, deleted_at = %s""",
                        (message.channel.id, message.author.id, message.content, discord.utils.utcnow(),
                         message.author.id, message.content, discord.utils.utcnow())
                    )
        except Exception as e:
            print(f"Snipe storage error: {e}")

    @commands.Cog.listener()
    async def on_message_edit(self, before, after):
        """Called when a message is edited"""
        if before.author.bot or before.content == after.content:
            return
        
        # Store edited message for editsnipe command
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO editsnipe_messages (channel_id, author_id, before_content, after_content, edited_at)
                           VALUES (%s, %s, %s, %s, %s)
                           ON DUPLICATE KEY UPDATE
                           author_id = %s, before_content = %s, after_content = %s, edited_at = %s""",
                        (before.channel.id, before.author.id, before.content, after.content, discord.utils.utcnow(),
                         before.author.id, before.content, after.content, discord.utils.utcnow())
                    )
        except Exception as e:
            print(f"Editsnipe storage error: {e}")

    @commands.Cog.listener()
    async def on_command(self, ctx):
        """Called when a command is invoked"""
        print(f"Command used: {ctx.command} by {ctx.author} in {ctx.guild}")

    @commands.Cog.listener()
    async def on_command_completion(self, ctx):
        """Called when a command completes successfully"""
        # Update command usage stats
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # First, check if the table exists and has the correct structure
                    await cursor.execute("SHOW COLUMNS FROM command_usage LIKE 'used_at'")
                    result = await cursor.fetchone()

                    if not result:
                        # Column doesn't exist, add it
                        try:
                            await cursor.execute("""
                                ALTER TABLE command_usage
                                ADD COLUMN used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                            """)
                            await conn.commit()
                        except Exception as alter_error:
                            print(f"Could not add used_at column: {alter_error}")
                            # Check if channel_id column exists for fallback
                            await cursor.execute("SHOW COLUMNS FROM command_usage LIKE 'channel_id'")
                            channel_fallback = await cursor.fetchone()

                            if channel_fallback:
                                # Try to insert with channel_id but without used_at
                                await cursor.execute(
                                    """INSERT INTO command_usage (command_name, user_id, guild_id, channel_id)
                                       VALUES (%s, %s, %s, %s)""",
                                    (ctx.command.name, ctx.author.id, ctx.guild.id if ctx.guild else None, ctx.channel.id)
                                )
                            else:
                                # Try to insert without used_at and channel_id
                                await cursor.execute(
                                    """INSERT INTO command_usage (command_name, user_id, guild_id)
                                       VALUES (%s, %s, %s)""",
                                    (ctx.command.name, ctx.author.id, ctx.guild.id if ctx.guild else None)
                                )
                            await conn.commit()
                            return

                    # Check if channel_id column exists
                    await cursor.execute("SHOW COLUMNS FROM command_usage LIKE 'channel_id'")
                    channel_result = await cursor.fetchone()

                    if channel_result:
                        # Insert with channel_id
                        await cursor.execute(
                            """INSERT INTO command_usage (command_name, user_id, guild_id, channel_id, used_at)
                               VALUES (%s, %s, %s, %s, %s)""",
                            (ctx.command.name, ctx.author.id, ctx.guild.id if ctx.guild else None, ctx.channel.id, discord.utils.utcnow())
                        )
                    else:
                        # Insert without channel_id
                        await cursor.execute(
                            """INSERT INTO command_usage (command_name, user_id, guild_id, used_at)
                               VALUES (%s, %s, %s, %s)""",
                            (ctx.command.name, ctx.author.id, ctx.guild.id if ctx.guild else None, discord.utils.utcnow())
                        )
                    await conn.commit()
        except Exception as e:
            print(f"Command usage tracking error: {e}")

    @commands.Cog.listener()
    async def on_ready(self):
        """Called when bot is ready (additional setup)"""
        print(f"🎉 Events cog loaded and ready!")

    @commands.command(
        name="snipe",
        description="Show the last deleted message",
        usage="<channel>"
    )
    async def snipe(self, ctx, channel: discord.TextChannel = None):
        """Show the last deleted message"""
        channel = channel or ctx.channel
        
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM snipe_messages WHERE channel_id = %s",
                        (channel.id,)
                    )
                    snipe_data = await cursor.fetchone()
            
            if not snipe_data:
                return await ctx.send_warning("No deleted messages found in this channel!")
            
            # snipe_data is tuple: (channel_id, author_id, content, deleted_at)
            author = self.bot.get_user(snipe_data[1])  # author_id
            if not author:
                author = f"Unknown User ({snipe_data[1]})"

            embed = discord.Embed(
                description=snipe_data[2] or "*No content*",  # content
                color=self.bot.color,
                timestamp=snipe_data[3]  # deleted_at
            )
            embed.set_author(name=str(author), icon_url=author.display_avatar.url if hasattr(author, 'display_avatar') else None)
            embed.set_footer(text=f"Deleted in #{channel.name}")
            
            await ctx.reply(embed=embed)
            
        except Exception as e:
            await ctx.send_error(f"Failed to get sniped message: {e}")

    @commands.command(
        name="editsnipe",
        description="Show the last edited message",
        usage="<channel>",
        aliases=["esnipe"]
    )
    async def editsnipe(self, ctx, channel: discord.TextChannel = None):
        """Show the last edited message"""
        channel = channel or ctx.channel
        
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM editsnipe_messages WHERE channel_id = %s",
                        (channel.id,)
                    )
                    snipe_data = await cursor.fetchone()
            
            if not snipe_data:
                return await ctx.send_warning("No edited messages found in this channel!")
            
            # snipe_data is tuple: (channel_id, author_id, before_content, after_content, edited_at)
            author = self.bot.get_user(snipe_data[1])  # author_id
            if not author:
                author = f"Unknown User ({snipe_data[1]})"

            embed = discord.Embed(
                color=self.bot.color,
                timestamp=snipe_data[4]  # edited_at
            )
            embed.set_author(name=str(author), icon_url=author.display_avatar.url if hasattr(author, 'display_avatar') else None)
            embed.add_field(name="Before", value=snipe_data[2] or "*No content*", inline=False)  # before_content
            embed.add_field(name="After", value=snipe_data[3] or "*No content*", inline=False)  # after_content
            embed.set_footer(text=f"Edited in #{channel.name}")
            
            await ctx.reply(embed=embed)
            
        except Exception as e:
            await ctx.send_error(f"Failed to get edited message: {e}")


async def setup(bot):
    await bot.add_cog(Events(bot))
