import discord
from discord.ext import commands
import random
import aiohttp


class Roleplay(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def get_anime_gif(self, action: str, nsfw: bool = False):
        """Get anime GIF from free API"""
        # For NSFW content, use different endpoints
        if nsfw:
            try:
                # Using waifu.pics NSFW API
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"https://api.waifu.pics/nsfw/{action}") as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            return data.get('url')
            except:
                pass

            try:
                # Using nekos.life NSFW API
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"https://nekos.life/api/v2/img/{action}") as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            return data.get('url')
            except:
                pass
        else:
            # SFW content
            try:
                # Using nekos.life API - free anime GIF API
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"https://nekos.life/api/v2/img/{action}") as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            return data.get('url')
            except:
                pass

            # Fallback to waifu.pics API
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"https://api.waifu.pics/sfw/{action}") as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            return data.get('url')
            except:
                pass

        return None

    def get_action_response(self, action: str, author: str, target: str):
        """Get response text for roleplay actions"""
        responses = {
            "hug": [
                f"**{author}** gives **{target}** a warm hug! 🤗",
                f"**{author}** hugs **{target}** tightly! 💕",
                f"**{target}** receives a big hug from **{author}**! 🫂"
            ],
            "kiss": [
                f"**{author}** gives **{target}** a sweet kiss! 😘",
                f"**{author}** kisses **{target}** gently! 💋",
                f"**{target}** gets a lovely kiss from **{author}**! ❤️"
            ],
            "slap": [
                f"**{author}** slaps **{target}**! 👋",
                f"**{author}** gives **{target}** a firm slap! ✋",
                f"**{target}** gets slapped by **{author}**! 😤"
            ],
            "pat": [
                f"**{author}** pats **{target}** on the head! 👋",
                f"**{author}** gives **{target}** gentle pats! 🥰",
                f"**{target}** enjoys head pats from **{author}**! 😊"
            ],
            "punch": [
                f"**{author}** punches **{target}**! 👊",
                f"**{author}** throws a punch at **{target}**! 💥",
                f"**{target}** gets punched by **{author}**! 😵"
            ],
            "cuddle": [
                f"**{author}** cuddles with **{target}**! 🥰",
                f"**{author}** gives **{target}** warm cuddles! 💕",
                f"**{target}** enjoys cuddling with **{author}**! 🤗"
            ],
            "bite": [
                f"**{author}** playfully bites **{target}**! 😈",
                f"**{author}** gives **{target}** a gentle bite! 🦷",
                f"**{target}** gets bitten by **{author}**! 😮"
            ],
            "poke": [
                f"**{author}** pokes **{target}**! 👉",
                f"**{author}** gives **{target}** a playful poke! 😄",
                f"**{target}** gets poked by **{author}**! 😊"
            ],
            "tickle": [
                f"**{author}** tickles **{target}**! 😂",
                f"**{author}** gives **{target}** tickles! 🤭",
                f"**{target}** gets tickled by **{author}**! 😆"
            ],
            "feed": [
                f"**{author}** feeds **{target}**! 🍽️",
                f"**{author}** gives **{target}** some food! 🥄",
                f"**{target}** gets fed by **{author}**! 😋"
            ],
            "wave": [
                f"**{author}** waves at **{target}**! 👋",
                f"**{author}** gives **{target}** a friendly wave! 😊",
                f"**{target}** gets a wave from **{author}**! 🙋"
            ],
            "dance": [
                f"**{author}** dances with **{target}**! 💃",
                f"**{author}** invites **{target}** to dance! 🕺",
                f"**{target}** dances with **{author}**! 🎵"
            ],
            # Additional SFW commands
            "lick": [
                f"**{author}** licks **{target}**! 👅",
                f"**{author}** gives **{target}** a playful lick! 😋",
                f"**{target}** gets licked by **{author}**! 😳"
            ],
            "blush": [
                f"**{author}** makes **{target}** blush! 😊",
                f"**{author}** causes **{target}** to turn red! ☺️",
                f"**{target}** blushes because of **{author}**! 😳"
            ],
            "cry": [
                f"**{author}** cries on **{target}**'s shoulder! 😢",
                f"**{author}** sheds tears with **{target}**! 😭",
                f"**{target}** comforts crying **{author}**! 🥺"
            ],
            "smile": [
                f"**{author}** smiles at **{target}**! 😊",
                f"**{author}** gives **{target}** a bright smile! 😄",
                f"**{target}** receives a warm smile from **{author}**! 🙂"
            ],
            "wink": [
                f"**{author}** winks at **{target}**! 😉",
                f"**{author}** gives **{target}** a flirty wink! 😏",
                f"**{target}** gets winked at by **{author}**! 😉"
            ],
            "stare": [
                f"**{author}** stares at **{target}**! 👀",
                f"**{author}** gives **{target}** an intense stare! 😳",
                f"**{target}** is being stared at by **{author}**! 😅"
            ],
            "nom": [
                f"**{author}** noms on **{target}**! 😋",
                f"**{author}** playfully noms **{target}**! 🍴",
                f"**{target}** gets nommed by **{author}**! 😆"
            ],
            "boop": [
                f"**{author}** boops **{target}**'s nose! 👆",
                f"**{author}** gives **{target}** a gentle boop! 😊",
                f"**{target}** gets booped by **{author}**! 😄"
            ],
            # NSFW commands
            "spank": [
                f"**{author}** spanks **{target}**! 🍑",
                f"**{author}** gives **{target}** a firm spank! 👋",
                f"**{target}** gets spanked by **{author}**! 😈"
            ],
            "fuck": [
                f"**{author}** fucks **{target}**! 🔥",
                f"**{author}** has intense fun with **{target}**! 💦",
                f"**{target}** gets fucked by **{author}**! 😈"
            ],
            "anal": [
                f"**{author}** does anal with **{target}**! 🍑",
                f"**{author}** explores **{target}**'s backdoor! 😈",
                f"**{target}** enjoys anal with **{author}**! 🔥"
            ],
            "suck": [
                f"**{author}** gets sucked by **{target}**! 👄",
                f"**{author}** receives oral from **{target}**! 💋",
                f"**{target}** sucks **{author}**! 😈"
            ],
            "cum": [
                f"**{author}** cums with **{target}**! 💦",
                f"**{author}** reaches climax with **{target}**! 🔥",
                f"**{target}** makes **{author}** cum! 😈"
            ],
            "pussy": [
                f"**{author}** plays with **{target}**'s pussy! 🐱",
                f"**{author}** explores **{target}**! 😈",
                f"**{target}** gets pleased by **{author}**! 💦"
            ],
            "dick": [
                f"**{author}** plays with **{target}**'s dick! 🍆",
                f"**{author}** handles **{target}**! 😈",
                f"**{target}** gets pleased by **{author}**! 🔥"
            ],
            "boobs": [
                f"**{author}** plays with **{target}**'s boobs! 🍈",
                f"**{author}** gropes **{target}**! 😈",
                f"**{target}** gets their boobs played with by **{author}**! 🔥"
            ],
            "ass": [
                f"**{author}** grabs **{target}**'s ass! 🍑",
                f"**{author}** squeezes **{target}**'s butt! 😈",
                f"**{target}** gets their ass grabbed by **{author}**! 🔥"
            ],
            "69": [
                f"**{author}** does 69 with **{target}**! 😈",
                f"**{author}** and **{target}** enjoy mutual pleasure! 💦",
                f"**{target}** does 69 with **{author}**! 🔥"
            ],
            "masturbate": [
                f"**{author}** masturbates with **{target}**! 💦",
                f"**{author}** pleasures themselves with **{target}**! 😈",
                f"**{target}** masturbates with **{author}**! 🔥"
            ],
            "orgasm": [
                f"**{author}** has an orgasm with **{target}**! 💦",
                f"**{author}** climaxes with **{target}**! 🔥",
                f"**{target}** gives **{author}** an orgasm! 😈"
            ]
        }

        return random.choice(responses.get(action, [f"**{author}** does something to **{target}**!"]))

    def is_nsfw_allowed(self, ctx):
        """Check if NSFW content is allowed in this channel"""
        if isinstance(ctx.channel, discord.DMChannel):
            return True
        return getattr(ctx.channel, 'nsfw', False)

    async def create_roleplay_embed(self, ctx, action: str, member: discord.Member, nsfw: bool = False):
        """Create roleplay embed with GIF"""
        if nsfw and not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")

        response = self.get_action_response(action, ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif(action, nsfw)

        # Set color based on action type
        color_map = {
            "hug": self.bot.color,
            "kiss": 0xff69b4,
            "slap": 0xff0000,
            "pat": self.bot.color,
            "punch": 0x8b0000,
            "cuddle": 0xffc0cb,
            "bite": 0x8b4513,
            "poke": self.bot.color,
            "tickle": 0xffff00,
            "feed": 0xffa500,
            "wave": self.bot.color,
            "dance": 0x9370db,
            "lick": 0xff1493,
            "blush": 0xffb6c1,
            "cry": 0x4169e1,
            "smile": 0xffd700,
            "wink": 0xff69b4,
            "stare": 0x696969,
            "nom": 0xffa500,
            "boop": self.bot.color,
            # NSFW colors
            "spank": 0xff4500,
            "fuck": 0xff0000,
            "anal": 0x8b0000,
            "suck": 0xff1493,
            "cum": 0xffffff,
            "pussy": 0xff69b4,
            "dick": 0x8b4513,
            "boobs": 0xffb6c1,
            "ass": 0xff4500,
            "69": 0xff0000,
            "masturbate": 0xff1493,
            "orgasm": 0xff0000
        }

        embed = discord.Embed(
            description=response,
            color=color_map.get(action, self.bot.color)
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="hug",
        description="Hug someone",
        usage="[member]"
    )
    async def hug(self, ctx, member: discord.Member = None):
        """Hug someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to hug?")
        
        if member == ctx.author:
            return await ctx.send_warning("You can't hug yourself! *gives you a hug* 🤗")
        
        if member.bot:
            return await ctx.send_warning("Bots don't need hugs... or do they? 🤖")
        
        response = self.get_action_response("hug", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("hug")

        embed = discord.Embed(
            description=response,
            color=self.bot.color
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="kiss",
        description="Kiss someone",
        usage="[member]"
    )
    async def kiss(self, ctx, member: discord.Member = None):
        """Kiss someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to kiss?")

        if member == ctx.author:
            return await ctx.send_warning("You can't kiss yourself! 😘")

        if member.bot:
            return await ctx.send_warning("That's a bit weird... 🤖")

        response = self.get_action_response("kiss", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("kiss")

        embed = discord.Embed(
            description=response,
            color=0xff69b4
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="slap",
        description="Slap someone",
        usage="[member]"
    )
    async def slap(self, ctx, member: discord.Member = None):
        """Slap someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to slap?")

        if member == ctx.author:
            return await ctx.send_warning("Why would you slap yourself? 😵")

        if member.bot:
            return await ctx.send_warning("Don't hurt the bots! 🤖")

        response = self.get_action_response("slap", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("slap")

        embed = discord.Embed(
            description=response,
            color=0xff0000
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="pat",
        description="Pat someone",
        usage="[member]"
    )
    async def pat(self, ctx, member: discord.Member = None):
        """Pat someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to pat?")

        if member == ctx.author:
            return await ctx.send_warning("*pats you on the head* 😊")

        response = self.get_action_response("pat", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("pat")

        embed = discord.Embed(
            description=response,
            color=self.bot.color
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="punch",
        description="Punch someone",
        usage="[member]"
    )
    async def punch(self, ctx, member: discord.Member = None):
        """Punch someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to punch?")

        if member == ctx.author:
            return await ctx.send_warning("Don't hurt yourself! 😵")

        if member.bot:
            return await ctx.send_warning("Violence against bots is not allowed! 🤖")

        response = self.get_action_response("punch", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("punch")

        embed = discord.Embed(
            description=response,
            color=0x8b0000
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="cuddle",
        description="Cuddle with someone",
        usage="[member]"
    )
    async def cuddle(self, ctx, member: discord.Member = None):
        """Cuddle with someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to cuddle with?")

        if member == ctx.author:
            return await ctx.send_warning("You can't cuddle yourself! 🤗")

        if member.bot:
            return await ctx.send_warning("Bots don't cuddle... or do they? 🤖")

        response = self.get_action_response("cuddle", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("cuddle")

        embed = discord.Embed(
            description=response,
            color=0xffc0cb
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="bite",
        description="Playfully bite someone",
        usage="[member]"
    )
    async def bite(self, ctx, member: discord.Member = None):
        """Playfully bite someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to bite?")

        if member == ctx.author:
            return await ctx.send_warning("Don't bite yourself! 😅")

        if member.bot:
            return await ctx.send_warning("Bots might taste like metal! 🤖")

        response = self.get_action_response("bite", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("bite")

        embed = discord.Embed(
            description=response,
            color=0x8b4513
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="poke",
        description="Poke someone",
        usage="[member]"
    )
    async def poke(self, ctx, member: discord.Member = None):
        """Poke someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to poke?")

        if member == ctx.author:
            return await ctx.send_warning("*pokes you* 😄")

        response = self.get_action_response("poke", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("poke")

        embed = discord.Embed(
            description=response,
            color=self.bot.color
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="tickle",
        description="Tickle someone",
        usage="[member]"
    )
    async def tickle(self, ctx, member: discord.Member = None):
        """Tickle someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to tickle?")

        if member == ctx.author:
            return await ctx.send_warning("You can't tickle yourself! 😂")

        response = self.get_action_response("tickle", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("tickle")

        embed = discord.Embed(
            description=response,
            color=0xffff00
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="feed",
        description="Feed someone",
        usage="[member]"
    )
    async def feed(self, ctx, member: discord.Member = None):
        """Feed someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to feed?")

        if member == ctx.author:
            return await ctx.send_warning("Feeding yourself? 🍽️")

        response = self.get_action_response("feed", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("feed")

        embed = discord.Embed(
            description=response,
            color=0xffa500
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="wave",
        description="Wave at someone",
        usage="[member]"
    )
    async def wave(self, ctx, member: discord.Member = None):
        """Wave at someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to wave at?")

        if member == ctx.author:
            return await ctx.send_warning("*waves at you* 👋")

        response = self.get_action_response("wave", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("wave")

        embed = discord.Embed(
            description=response,
            color=self.bot.color
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    @commands.command(
        name="dance",
        description="Dance with someone",
        usage="[member]"
    )
    async def dance(self, ctx, member: discord.Member = None):
        """Dance with someone"""
        if member is None:
            return await ctx.send_warning("Who do you want to dance with?")

        if member == ctx.author:
            return await ctx.send_warning("Dancing alone? 💃")

        response = self.get_action_response("dance", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("dance")

        embed = discord.Embed(
            description=response,
            color=0x9370db
        )

        if gif_url:
            embed.set_image(url=gif_url)

        await ctx.reply(embed=embed)

    # Additional SFW commands
    @commands.command(name="lick", description="Lick someone", usage="[member]")
    async def lick(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to lick?")
        if member == ctx.author:
            return await ctx.send_warning("Don't lick yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Bots might taste weird! 🤖")

        response = self.get_action_response("lick", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("lick")
        embed = discord.Embed(description=response, color=0xff1493)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="blush", description="Make someone blush", usage="[member]")
    async def blush(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to make blush?")
        if member == ctx.author:
            return await ctx.send_warning("*blushes* 😊")

        response = self.get_action_response("blush", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("blush")
        embed = discord.Embed(description=response, color=0xffb6c1)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="cry", description="Cry with someone", usage="[member]")
    async def cry(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to cry with?")
        if member == ctx.author:
            return await ctx.send_warning("*cries* 😢")

        response = self.get_action_response("cry", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("cry")
        embed = discord.Embed(description=response, color=0x4169e1)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="smile", description="Smile at someone", usage="[member]")
    async def smile(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to smile at?")
        if member == ctx.author:
            return await ctx.send_warning("*smiles* 😊")

        response = self.get_action_response("smile", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("smile")
        embed = discord.Embed(description=response, color=0xffd700)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="wink", description="Wink at someone", usage="[member]")
    async def wink(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to wink at?")
        if member == ctx.author:
            return await ctx.send_warning("*winks* 😉")

        response = self.get_action_response("wink", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("wink")
        embed = discord.Embed(description=response, color=0xff69b4)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="stare", description="Stare at someone", usage="[member]")
    async def stare(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to stare at?")
        if member == ctx.author:
            return await ctx.send_warning("*stares at mirror* 👀")

        response = self.get_action_response("stare", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("stare")
        embed = discord.Embed(description=response, color=0x696969)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="nom", description="Nom on someone", usage="[member]")
    async def nom(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to nom?")
        if member == ctx.author:
            return await ctx.send_warning("Don't nom yourself! 😋")

        response = self.get_action_response("nom", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("nom")
        embed = discord.Embed(description=response, color=0xffa500)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="boop", description="Boop someone's nose", usage="[member]")
    async def boop(self, ctx, member: discord.Member = None):
        if member is None:
            return await ctx.send_warning("Who do you want to boop?")
        if member == ctx.author:
            return await ctx.send_warning("*boops your nose* 👆")

        response = self.get_action_response("boop", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("boop")
        embed = discord.Embed(description=response, color=self.bot.color)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    # NSFW commands
    @commands.command(name="spank", description="Spank someone", usage="[member]")
    async def spank(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to spank?")
        if member == ctx.author:
            return await ctx.send_warning("Don't spank yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't spank bots! 🤖")

        response = self.get_action_response("spank", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("spank", nsfw=True)
        embed = discord.Embed(description=response, color=0xff4500)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="fuck", description="Fuck someone", usage="[member]")
    async def fuck(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to fuck?")
        if member == ctx.author:
            return await ctx.send_warning("You can't fuck yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't fuck bots! 🤖")

        response = self.get_action_response("fuck", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("fuck", nsfw=True)
        embed = discord.Embed(description=response, color=0xff0000)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="anal", description="Do anal with someone", usage="[member]")
    async def anal(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to do anal with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("anal", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("anal", nsfw=True)
        embed = discord.Embed(description=response, color=0x8b0000)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="suck", description="Get sucked by someone", usage="[member]")
    async def suck(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to get sucked by?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("suck", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("blowjob", nsfw=True)
        embed = discord.Embed(description=response, color=0xff1493)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="cum", description="Cum with someone", usage="[member]")
    async def cum(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to cum with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("cum", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("cum", nsfw=True)
        embed = discord.Embed(description=response, color=0xffffff)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="pussy", description="Play with someone's pussy", usage="[member]")
    async def pussy(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to play with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("pussy", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("pussy", nsfw=True)
        embed = discord.Embed(description=response, color=0xff69b4)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="dick", description="Play with someone's dick", usage="[member]")
    async def dick(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to play with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("dick", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("dick", nsfw=True)
        embed = discord.Embed(description=response, color=0x8b4513)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="boobs", description="Play with someone's boobs", usage="[member]")
    async def boobs(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to play with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("boobs", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("boobs", nsfw=True)
        embed = discord.Embed(description=response, color=0xffb6c1)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="ass", description="Grab someone's ass", usage="[member]")
    async def ass(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to grab?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("ass", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("ass", nsfw=True)
        embed = discord.Embed(description=response, color=0xff4500)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="69", description="Do 69 with someone", usage="[member]", aliases=["sixtynine"])
    async def sixtynine(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to do 69 with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("69", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("69", nsfw=True)
        embed = discord.Embed(description=response, color=0xff0000)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="masturbate", description="Masturbate with someone", usage="[member]", aliases=["fap"])
    async def masturbate(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to masturbate with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("masturbate", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("masturbate", nsfw=True)
        embed = discord.Embed(description=response, color=0xff1493)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)

    @commands.command(name="orgasm", description="Have an orgasm with someone", usage="[member]")
    async def orgasm(self, ctx, member: discord.Member = None):
        if not self.is_nsfw_allowed(ctx):
            return await ctx.send_warning("Turn on **nsfw** for this channel to use this command")
        if member is None:
            return await ctx.send_warning("Who do you want to have an orgasm with?")
        if member == ctx.author:
            return await ctx.send_warning("You can't do that with yourself! 😅")
        if member.bot:
            return await ctx.send_warning("Don't do that with bots! 🤖")

        response = self.get_action_response("orgasm", ctx.author.display_name, member.display_name)
        gif_url = await self.get_anime_gif("orgasm", nsfw=True)
        embed = discord.Embed(description=response, color=0xff0000)
        if gif_url:
            embed.set_image(url=gif_url)
        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Roleplay(bot))
