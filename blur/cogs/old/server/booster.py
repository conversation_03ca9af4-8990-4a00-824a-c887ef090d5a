import discord
from discord.ext import commands
from tools.checks import Perms


class Booster(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Handle boost events"""
        # Check if user started boosting
        if not before.premium_since and after.premium_since:
            await self.handle_boost_start(after)
        
        # Check if user stopped boosting
        elif before.premium_since and not after.premium_since:
            await self.handle_boost_end(after)

    async def handle_boost_start(self, member):
        """Handle when someone starts boosting"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_config WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    config = await cursor.fetchone()

            if not config:
                return

            # Send boost message
            if config[1]:  # channel_id field
                channel = self.bot.get_channel(config[1])
                if channel:
                    embed = discord.Embed(
                        title="🚀 Server Boosted!",
                        description=f"Thank you {member.mention} for boosting the server!",
                        color=0xff73fa
                    )
                    embed.set_thumbnail(url=member.display_avatar.url)

                    if config[3]:  # boost_message field
                        embed.description = config[3].replace("{user}", member.mention)

                    try:
                        await channel.send(embed=embed)
                    except discord.Forbidden:
                        print(f"Failed to send boost message to {member.guild.name}: Missing permissions in {channel.name}")
                    except Exception as e:
                        print(f"Failed to send boost message to {member.guild.name}: {e}")

            # Give booster role
            if config[2]:  # booster_role_id field
                role = member.guild.get_role(config[2])
                if role and role < member.guild.me.top_role:
                    try:
                        await member.add_roles(role, reason="Server booster")
                    except discord.Forbidden:
                        print(f"Failed to give booster role to {member} in {member.guild.name}: Missing permissions")
                    except Exception as e:
                        print(f"Failed to give booster role to {member} in {member.guild.name}: {e}")

            # Give booster perks
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    perks = await cursor.fetchall()

            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = member.guild.get_role(perk[2])  # perk_value field
                    if role and role < member.guild.me.top_role:
                        try:
                            await member.add_roles(role, reason="Booster perk")
                        except discord.Forbidden:
                            print(f"Failed to give booster perk role to {member} in {member.guild.name}: Missing permissions")
                        except Exception as e:
                            print(f"Failed to give booster perk role to {member} in {member.guild.name}: {e}")

        except Exception as e:
            print(f"Error in handle_boost_start: {e}")

    async def handle_boost_end(self, member):
        """Handle when someone stops boosting"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_config WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    config = await cursor.fetchone()

            if not config:
                return

            # Remove booster role
            if config[2]:  # booster_role_id field
                role = member.guild.get_role(config[2])
                if role and role in member.roles:
                    try:
                        await member.remove_roles(role, reason="No longer boosting")
                    except discord.Forbidden:
                        print(f"Failed to remove booster role from {member} in {member.guild.name}: Missing permissions")
                    except Exception as e:
                        print(f"Failed to remove booster role from {member} in {member.guild.name}: {e}")

            # Remove booster perks
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    perks = await cursor.fetchall()

            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = member.guild.get_role(perk[2])  # perk_value field
                    if role and role in member.roles:
                        try:
                            await member.remove_roles(role, reason="No longer boosting")
                        except discord.Forbidden:
                            print(f"Failed to remove booster perk role from {member} in {member.guild.name}: Missing permissions")
                        except Exception as e:
                            print(f"Failed to remove booster perk role from {member} in {member.guild.name}: {e}")

        except Exception as e:
            print(f"Error in handle_boost_end: {e}")

    @commands.group(
        name="booster",
        description="Server booster perks",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def booster(self, ctx):
        """Server booster system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🚀 Booster System",
                description="Manage server booster perks and rewards",
                color=0xff73fa
            )
            
            embed.add_field(
                name="Commands",
                value="`booster setup [channel] [role]` - Setup booster system\n"
                      "`booster message [message]` - Set boost message\n"
                      "`booster perks add [type] [value]` - Add booster perk\n"
                      "`booster perks remove [type] [value]` - Remove perk\n"
                      "`booster perks list` - List all perks\n"
                      "`booster list` - List all boosters",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @booster.command(
        name="setup",
        description="Setup booster system",
        usage="[channel] <role>"
    )
    @Perms.get_perms("manage_guild")
    async def booster_setup(self, ctx, channel: discord.TextChannel, role: discord.Role = None):
        """Setup booster system"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO booster_config (guild_id, channel_id, booster_role_id)
                           VALUES (%s, %s, %s)
                           ON DUPLICATE KEY UPDATE channel_id = %s, booster_role_id = %s""",
                        (ctx.guild.id, channel.id, role.id if role else None, channel.id, role.id if role else None)
                    )

            role_text = f" with role {role.mention}" if role else ""
            await ctx.send_success(f"Booster system setup in {channel.mention}{role_text}")

        except Exception as e:
            await ctx.send_error(f"Failed to setup booster system: {e}")

    @booster.command(
        name="message",
        description="Set boost message",
        usage="[message]"
    )
    @Perms.get_perms("manage_guild")
    async def booster_message(self, ctx, *, message: str):
        """Set boost message"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO booster_config (guild_id, boost_message)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE boost_message = %s""",
                        (ctx.guild.id, message, message)
                    )

            await ctx.send_success("Boost message updated! Use `{user}` to mention the booster.")

        except Exception as e:
            await ctx.send_error(f"Failed to set boost message: {e}")

    @booster.group(
        name="perks",
        description="Manage booster perks",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def booster_perks(self, ctx):
        """Manage booster perks"""
        embed = discord.Embed(
            title="🎁 Booster Perks",
            description="Manage perks for server boosters",
            color=0xff73fa
        )
        
        embed.add_field(
            name="Commands",
            value="`booster perks add role [role]` - Add role perk\n"
                  "`booster perks remove role [role]` - Remove role perk\n"
                  "`booster perks list` - List all perks",
            inline=False
        )
        
        await ctx.reply(embed=embed)

    @booster_perks.command(
        name="add",
        description="Add booster perk",
        usage="[type] [value]"
    )
    @Perms.get_perms("manage_guild")
    async def perks_add(self, ctx, perk_type: str, *, value: str):
        """Add booster perk"""
        if perk_type.lower() == "role":
            try:
                role = await commands.RoleConverter().convert(ctx, value)
            except commands.BadArgument:
                return await ctx.send_warning("Invalid role!")

            if role >= ctx.guild.me.top_role:
                return await ctx.send_warning("That role is higher than my highest role!")

            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "INSERT INTO booster_perks (guild_id, perk_type, perk_value) VALUES (%s, %s, %s)",
                            (ctx.guild.id, "role", role.id)
                        )

                await ctx.send_success(f"Added booster perk: {role.mention}")

            except Exception as e:
                await ctx.send_error(f"Failed to add booster perk: {e}")
        else:
            await ctx.send_warning("Invalid perk type! Use: `role`")

    @booster_perks.command(
        name="remove",
        description="Remove booster perk",
        usage="[type] [value]"
    )
    @Perms.get_perms("manage_guild")
    async def perks_remove(self, ctx, perk_type: str, *, value: str):
        """Remove booster perk"""
        if perk_type.lower() == "role":
            try:
                role = await commands.RoleConverter().convert(ctx, value)
            except commands.BadArgument:
                return await ctx.send_warning("Invalid role!")

            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM booster_perks WHERE guild_id = %s AND perk_type = %s AND perk_value = %s",
                            (ctx.guild.id, "role", role.id)
                        )
                        affected_rows = cursor.rowcount

                if affected_rows == 0:
                    return await ctx.send_warning("Perk not found!")

                await ctx.send_success(f"Removed booster perk: {role.mention}")

            except Exception as e:
                await ctx.send_error(f"Failed to remove booster perk: {e}")
        else:
            await ctx.send_warning("Invalid perk type! Use: `role`")

    @booster_perks.command(
        name="list",
        description="List all booster perks"
    )
    @Perms.get_perms("manage_guild")
    async def perks_list(self, ctx):
        """List all booster perks"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    perks = await cursor.fetchall()

            if not perks:
                return await ctx.send_warning("No booster perks configured!")

            embed = discord.Embed(
                title="🎁 Booster Perks",
                color=0xff73fa
            )

            role_perks = []
            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = ctx.guild.get_role(perk[2])  # perk_value field
                    if role:
                        role_perks.append(role.mention)

            if role_perks:
                embed.add_field(
                    name="Role Perks",
                    value="\n".join(role_perks),
                    inline=False
                )
            else:
                embed.description = "No valid role perks found."

            await ctx.reply(embed=embed)

        except Exception as e:
            await ctx.send_error(f"Failed to list booster perks: {e}")

    @booster.command(
        name="list",
        description="List all server boosters"
    )
    async def booster_list(self, ctx):
        """List all server boosters"""
        boosters = [member for member in ctx.guild.members if member.premium_since]
        
        if not boosters:
            return await ctx.send_warning("No server boosters found!")
        
        embed = discord.Embed(
            title=f"🚀 Server Boosters ({len(boosters)})",
            color=0xff73fa
        )
        
        description = ""
        for i, booster in enumerate(boosters[:10], 1):
            description += f"{i}. {booster.mention} - <t:{int(booster.premium_since.timestamp())}:R>\n"
        
        if len(boosters) > 10:
            description += f"\n... and {len(boosters) - 10} more boosters"
        
        embed.description = description
        embed.set_footer(text=f"Boost Level: {ctx.guild.premium_tier} | Total Boosts: {ctx.guild.premium_subscription_count}")
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="boost",
        description="Show boost information"
    )
    async def boost_info(self, ctx):
        """Show server boost information"""
        embed = discord.Embed(
            title="🚀 Server Boost Information",
            color=0xff73fa
        )
        
        embed.add_field(
            name="Boost Level",
            value=f"Level {ctx.guild.premium_tier}",
            inline=True
        )
        
        embed.add_field(
            name="Total Boosts",
            value=f"{ctx.guild.premium_subscription_count}",
            inline=True
        )
        
        boosters = [member for member in ctx.guild.members if member.premium_since]
        embed.add_field(
            name="Boosters",
            value=f"{len(boosters)} members",
            inline=True
        )
        
        # Show boost perks
        perks_text = ""
        if ctx.guild.premium_tier >= 1:
            perks_text += "• 50 extra emoji slots\n• 128 Kbps audio quality\n• Custom server invite background\n• Animated server icon\n"
        if ctx.guild.premium_tier >= 2:
            perks_text += "• 150 extra emoji slots\n• 256 Kbps audio quality\n• Server banner\n• 50MB upload limit\n"
        if ctx.guild.premium_tier >= 3:
            perks_text += "• 250 extra emoji slots\n• 384 Kbps audio quality\n• Vanity URL\n• 100MB upload limit\n"
        
        if perks_text:
            embed.add_field(
                name="Current Perks",
                value=perks_text,
                inline=False
            )
        
        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Booster(bot))
