import discord
from discord.ext import commands
from tools.checks import Perms


class Prefix(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="prefix",
        description="Manage server prefix",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def prefix(self, ctx):
        """Manage server prefix"""
        if ctx.invoked_subcommand is None:
            # Show current prefix
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        result = await cursor.fetchone()

                current_prefix = result[0] if result else ","
                await ctx.send_success(f"Current server prefix: `{current_prefix}`")

            except Exception as e:
                await ctx.send_error(f"Failed to get prefix: {e}")

    @prefix.command(
        name="set",
        description="Set server prefix",
        usage="[new_prefix]"
    )
    @Perms.get_perms("manage_guild")
    async def prefix_set(self, ctx, prefix: str = None):
        """Set server prefix"""
        if prefix is None:
            return await ctx.send_warning("Please provide a prefix to set!")

        # Reset to default if "reset" or "default"
        if prefix.lower() in ["reset", "default"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )

                await ctx.send_success("Prefix reset to default: `,`")

            except Exception as e:
                await ctx.send_error(f"Failed to reset prefix: {e}")
            return

        if len(prefix) > 5:
            return await ctx.send_error("Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO prefixes (guild_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.guild.id, prefix, prefix)
                    )

            await ctx.send_success(f"Server prefix set to `{prefix}`")

        except Exception as e:
            await ctx.send_error(f"Failed to set prefix: {e}")

    @commands.group(
        name="selfprefix",
        description="Manage your personal prefix",
        aliases=["sp"],
        invoke_without_command=True
    )
    async def selfprefix(self, ctx):
        """Manage your personal prefix"""
        if ctx.invoked_subcommand is None:
            # Show current selfprefix
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                            (ctx.author.id,)
                        )
                        result = await cursor.fetchone()

                if result:
                    await ctx.send_success(f"Your personal prefix: `{result[0]}`")
                else:
                    # Get server prefix
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM prefixes WHERE guild_id = %s",
                                (ctx.guild.id,)
                            )
                            server_result = await cursor.fetchone()

                    server_prefix = server_result[0] if server_result else ","
                    await ctx.send_success(f"You don't have a personal prefix set. Using server prefix: `{server_prefix}`")

            except Exception as e:
                await ctx.send_error(f"Failed to get selfprefix: {e}")

    @selfprefix.command(
        name="set",
        description="Set your personal prefix",
        usage="[new_prefix]"
    )
    async def selfprefix_set(self, ctx, prefix: str = None):
        """Set your personal prefix"""
        if prefix is None:
            return await ctx.send_warning("Please provide a prefix to set!")

        # Reset to default if "reset" or "default"
        if prefix.lower() in ["reset", "default"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM selfprefixes WHERE user_id = %s",
                            (ctx.author.id,)
                        )

                # Get server prefix
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        server_result = await cursor.fetchone()

                server_prefix = server_result[0] if server_result else ","
                await ctx.send_success(f"Personal prefix reset. Now using server prefix: `{server_prefix}`")

            except Exception as e:
                await ctx.send_error(f"Failed to reset selfprefix: {e}")
            return

        if len(prefix) > 5:
            return await ctx.send_error("Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO selfprefixes (user_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.author.id, prefix, prefix)
                    )

            await ctx.send_success(f"Your personal prefix set to `{prefix}`")

        except Exception as e:
            await ctx.send_error(f"Failed to set selfprefix: {e}")


async def setup(bot):
    await bot.add_cog(Prefix(bot))
