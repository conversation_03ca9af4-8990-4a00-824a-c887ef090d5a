import discord
from discord.ext import commands
from tools.checks import Perms
from typing import Optional


class Config(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="config",
        description="Server configuration commands",
        invoke_without_command=True,
        aliases=["cfg"]
    )
    @Perms.get_perms("manage_guild")
    async def config(self, ctx):
        """Server configuration commands"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="⚙️ Server Configuration",
                description="Configure various bot settings for your server",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Available Commands",
                value="`config prefix` - Set bot prefix\n"
                      "`config welcome` - Configure welcome messages\n"
                      "`config goodbye` - Configure goodbye messages\n"
                      "`config logging` - Configure logging channel\n"
                      "`config view` - View current settings",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @config.command(
        name="prefix",
        description="Set bot prefix",
        usage="[new_prefix]"
    )
    @Perms.get_perms("manage_guild")
    async def config_prefix(self, ctx, prefix: str = None):
        """Set bot prefix"""
        if prefix is None:
            # Show current prefix
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        result = await cursor.fetchone()

                current_prefix = result[0] if result else ","
                await ctx.send_success(f"Current prefix: `{current_prefix}`")

            except Exception as e:
                await ctx.send_error(f"Failed to get prefix: {e}")
            return

        if len(prefix) > 5:
            return await ctx.send_error("Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO prefixes (guild_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.guild.id, prefix, prefix)
                    )

            await ctx.send_success(f"Prefix set to `{prefix}`")

        except Exception as e:
            await ctx.send_error(f"Failed to set prefix: {e}")

    @config.group(
        name="welcome",
        description="Configure welcome messages",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def config_welcome(self, ctx):
        """Configure welcome messages"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="👋 Welcome Configuration",
                description="Configure welcome messages for new members",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`config welcome enable` - Enable welcome messages\n"
                      "`config welcome disable` - Disable welcome messages\n"
                      "`config welcome channel` - Set welcome channel\n"
                      "`config welcome message` - Set welcome message\n"
                      "`config welcome test` - Test welcome message",
                inline=False
            )
            
            embed.add_field(
                name="Variables",
                value="`{user}` - Mention the user\n"
                      "`{server}` - Server name\n"
                      "`{count}` - Member count",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @config_welcome.command(
        name="enable",
        description="Enable welcome messages"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_enable(self, ctx):
        """Enable welcome messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO welcome_config (guild_id, enabled)
                           VALUES (%s, TRUE)
                           ON DUPLICATE KEY UPDATE enabled = TRUE""",
                        (ctx.guild.id,)
                    )

            await ctx.send_success("Welcome messages enabled!")

        except Exception as e:
            await ctx.send_error(f"Failed to enable welcome messages: {e}")

    @config_welcome.command(
        name="disable",
        description="Disable welcome messages"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_disable(self, ctx):
        """Disable welcome messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE welcome_config SET enabled = FALSE WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )

            await ctx.send_success("Welcome messages disabled!")

        except Exception as e:
            await ctx.send_error(f"Failed to disable welcome messages: {e}")

    @config_welcome.command(
        name="channel",
        description="Set welcome channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_channel(self, ctx, channel: discord.TextChannel):
        """Set welcome channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO welcome_config (guild_id, channel_id)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE channel_id = %s""",
                        (ctx.guild.id, channel.id, channel.id)
                    )

            await ctx.send_success(f"Welcome channel set to {channel.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to set welcome channel: {e}")

    @config_welcome.command(
        name="message",
        description="Set welcome message",
        usage="[message]"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_message(self, ctx, *, message: str):
        """Set welcome message"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO welcome_config (guild_id, message)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE message = %s""",
                        (ctx.guild.id, message, message)
                    )

            await ctx.send_success("Welcome message updated!")

        except Exception as e:
            await ctx.send_error(f"Failed to set welcome message: {e}")

    @config_welcome.command(
        name="test",
        description="Test welcome message"
    )
    @Perms.get_perms("manage_guild")
    async def welcome_test(self, ctx):
        """Test welcome message"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM welcome_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    welcome_data = await cursor.fetchone()

            if not welcome_data or not welcome_data[2]:  # enabled field
                return await ctx.send_warning("Welcome messages are not enabled!")

            if not welcome_data[1]:  # channel_id field
                return await ctx.send_warning("Welcome channel is not set!")

            channel = self.bot.get_channel(welcome_data[1])
            if not channel:
                return await ctx.send_error("Welcome channel not found!")

            # Test message
            message = welcome_data[3] if len(welcome_data) > 3 and welcome_data[3] else "Welcome {user} to {server}!"
            message = message.replace("{user}", ctx.author.mention)
            message = message.replace("{server}", ctx.guild.name)
            message = message.replace("{count}", str(ctx.guild.member_count))

            embed = discord.Embed(
                title="🧪 Welcome Test",
                description=message,
                color=self.bot.color
            )
            embed.set_thumbnail(url=ctx.author.display_avatar.url)
            embed.set_footer(text="This is a test message")

            await channel.send(embed=embed)
            await ctx.send_success(f"Test welcome message sent to {channel.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to test welcome message: {e}")

    @config.group(
        name="goodbye",
        description="Configure goodbye messages",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def config_goodbye(self, ctx):
        """Configure goodbye messages"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="👋 Goodbye Configuration",
                description="Configure goodbye messages for leaving members",
                color=self.bot.color
            )

            embed.add_field(
                name="Commands",
                value="`config goodbye enable` - Enable goodbye messages\n"
                      "`config goodbye disable` - Disable goodbye messages\n"
                      "`config goodbye channel` - Set goodbye channel\n"
                      "`config goodbye message` - Set goodbye message\n"
                      "`config goodbye test` - Test goodbye message",
                inline=False
            )

            embed.add_field(
                name="Variables",
                value="`{user}` - Mention the user\n"
                      "`{server}` - Server name\n"
                      "`{count}` - Member count",
                inline=False
            )

            await ctx.reply(embed=embed)

    @config_goodbye.command(
        name="enable",
        description="Enable goodbye messages"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_enable(self, ctx):
        """Enable goodbye messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO goodbye_config (guild_id, enabled)
                           VALUES (%s, TRUE)
                           ON DUPLICATE KEY UPDATE enabled = TRUE""",
                        (ctx.guild.id,)
                    )

            await ctx.send_success("Goodbye messages enabled!")

        except Exception as e:
            await ctx.send_error(f"Failed to enable goodbye messages: {e}")

    @config_goodbye.command(
        name="disable",
        description="Disable goodbye messages"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_disable(self, ctx):
        """Disable goodbye messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE goodbye_config SET enabled = FALSE WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )

            await ctx.send_success("Goodbye messages disabled!")

        except Exception as e:
            await ctx.send_error(f"Failed to disable goodbye messages: {e}")

    @config_goodbye.command(
        name="channel",
        description="Set goodbye channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_channel(self, ctx, channel: discord.TextChannel):
        """Set goodbye channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO goodbye_config (guild_id, channel_id)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE channel_id = %s""",
                        (ctx.guild.id, channel.id, channel.id)
                    )

            await ctx.send_success(f"Goodbye channel set to {channel.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to set goodbye channel: {e}")

    @config_goodbye.command(
        name="message",
        description="Set goodbye message",
        usage="[message]"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_message(self, ctx, *, message: str):
        """Set goodbye message"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO goodbye_config (guild_id, message)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE message = %s""",
                        (ctx.guild.id, message, message)
                    )

            await ctx.send_success("Goodbye message updated!")

        except Exception as e:
            await ctx.send_error(f"Failed to set goodbye message: {e}")

    @config_goodbye.command(
        name="test",
        description="Test goodbye message"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_test(self, ctx):
        """Test goodbye message"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM goodbye_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    goodbye_data = await cursor.fetchone()

            if not goodbye_data or not goodbye_data[2]:  # enabled field
                return await ctx.send_warning("Goodbye messages are not enabled!")

            if not goodbye_data[1]:  # channel_id field
                return await ctx.send_warning("Goodbye channel is not set!")

            channel = self.bot.get_channel(goodbye_data[1])
            if not channel:
                return await ctx.send_error("Goodbye channel not found!")

            # Test message
            message = goodbye_data[3] if len(goodbye_data) > 3 and goodbye_data[3] else "Goodbye {user}! We'll miss you."
            message = message.replace("{user}", str(ctx.author))
            message = message.replace("{server}", ctx.guild.name)
            message = message.replace("{count}", str(ctx.guild.member_count))

            embed = discord.Embed(
                title="🧪 Goodbye Test",
                description=message,
                color=self.bot.color
            )
            embed.set_thumbnail(url=ctx.author.display_avatar.url)
            embed.set_footer(text="This is a test message")

            await channel.send(embed=embed)
            await ctx.send_success(f"Test goodbye message sent to {channel.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to test goodbye message: {e}")

    @config.command(
        name="logging",
        description="Set logging channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def config_logging(self, ctx, channel: discord.TextChannel = None):
        """Set logging channel"""
        if channel is None:
            # Show current logging channel
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT log_channel FROM guild_config WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        result = await cursor.fetchone()

                if result and result[0]:
                    log_channel = self.bot.get_channel(result[0])
                    if log_channel:
                        await ctx.send_success(f"Current logging channel: {log_channel.mention}")
                    else:
                        await ctx.send_warning("Logging channel not found (may have been deleted)")
                else:
                    await ctx.send_warning("No logging channel set")

            except Exception as e:
                await ctx.send_error(f"Failed to get logging channel: {e}")
            return

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO guild_config (guild_id, log_channel)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE log_channel = %s""",
                        (ctx.guild.id, channel.id, channel.id)
                    )

            await ctx.send_success(f"Logging channel set to {channel.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to set logging channel: {e}")

    @config.command(
        name="view",
        description="View current server settings"
    )
    @Perms.get_perms("manage_guild")
    async def config_view(self, ctx):
        """View current server settings"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Get guild config
                    await cursor.execute(
                        "SELECT * FROM guild_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    guild_config = await cursor.fetchone()

                    # Get welcome config
                    await cursor.execute(
                        "SELECT * FROM welcome_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    welcome_config = await cursor.fetchone()

                    # Get goodbye config
                    await cursor.execute(
                        "SELECT * FROM goodbye_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    goodbye_config = await cursor.fetchone()

                    # Get prefix config
                    await cursor.execute(
                        "SELECT * FROM prefixes WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    prefix_config = await cursor.fetchone()

            embed = discord.Embed(
                title=f"⚙️ {ctx.guild.name} Configuration",
                color=self.bot.color
            )

            # Basic settings
            prefix = prefix_config[1] if prefix_config else ","
            log_channel = None
            if guild_config and guild_config[1]:  # log_channel field
                log_channel = self.bot.get_channel(guild_config[1])

            embed.add_field(
                name="Basic Settings",
                value=f"**Prefix:** `{prefix}`\n"
                      f"**Logging:** {log_channel.mention if log_channel else 'Not set'}",
                inline=False
            )

            # Welcome settings
            welcome_status = "❌ Disabled"
            welcome_channel = None
            if welcome_config and welcome_config[2]:  # enabled field
                welcome_status = "✅ Enabled"
                if welcome_config[1]:  # channel_id field
                    welcome_channel = self.bot.get_channel(welcome_config[1])

            embed.add_field(
                name="Welcome Messages",
                value=f"**Status:** {welcome_status}\n"
                      f"**Channel:** {welcome_channel.mention if welcome_channel else 'Not set'}",
                inline=True
            )

            # Goodbye settings
            goodbye_status = "❌ Disabled"
            goodbye_channel = None
            if goodbye_config and goodbye_config[2]:  # enabled field
                goodbye_status = "✅ Enabled"
                if goodbye_config[1]:  # channel_id field
                    goodbye_channel = self.bot.get_channel(goodbye_config[1])

            embed.add_field(
                name="Goodbye Messages",
                value=f"**Status:** {goodbye_status}\n"
                      f"**Channel:** {goodbye_channel.mention if goodbye_channel else 'Not set'}",
                inline=True
            )

            await ctx.reply(embed=embed)

        except Exception as e:
            await ctx.send_error(f"Failed to get server settings: {e}")


async def setup(bot):
    await bot.add_cog(Config(bot))
