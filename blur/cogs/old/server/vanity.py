import discord
from discord.ext import commands
from tools.checks import Perms
import re


class Vanity(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="vanity",
        description="Vanity URL management",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def vanity(self, ctx):
        """Vanity URL management"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🔗 Vanity URL Management",
                description="Manage server vanity URL (requires boost level 3)",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`vanity set [code]` - Set vanity URL\n"
                      "`vanity remove` - Remove vanity URL\n"
                      "`vanity info` - Show current vanity info\n"
                      "`vanity check [code]` - Check if code is available",
                inline=False
            )
            
            embed.add_field(
                name="Requirements",
                value="• Server must be boost level 3\n"
                      "• Code must be 3-32 characters\n"
                      "• Only letters, numbers, and hyphens\n"
                      "• Cannot start/end with hyphen",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @vanity.command(
        name="set",
        description="Set vanity URL",
        usage="[code]"
    )
    @Perms.get_perms("manage_guild")
    async def vanity_set(self, ctx, code: str):
        """Set vanity URL"""
        # Check boost level
        if ctx.guild.premium_tier < 3:
            return await ctx.send_warning("Server must be boost level 3 to use vanity URLs!")
        
        # Validate code
        if not self.validate_vanity_code(code):
            return await ctx.send_warning(
                "Invalid vanity code! Must be 3-32 characters, only letters/numbers/hyphens, "
                "and cannot start or end with a hyphen."
            )
        
        try:
            # Try to set vanity URL
            await ctx.guild.edit(vanity_url_code=code, reason=f"Vanity URL set by {ctx.author}")
            
            embed = discord.Embed(
                title="✅ Vanity URL Set",
                description=f"Vanity URL set to: **discord.gg/{code}**",
                color=0x00ff00
            )
            
            await ctx.reply(embed=embed)
            
        except discord.HTTPException as e:
            if "vanity url code is already taken" in str(e).lower():
                await ctx.send_warning("That vanity code is already taken!")
            elif "invalid vanity url code" in str(e).lower():
                await ctx.send_warning("Invalid vanity code!")
            else:
                await ctx.send_warning(f"Failed to set vanity URL: {e}")
        except Exception as e:
            await ctx.send_warning(f"Error setting vanity URL: {e}")

    @vanity.command(
        name="remove",
        description="Remove vanity URL"
    )
    @Perms.get_perms("manage_guild")
    async def vanity_remove(self, ctx):
        """Remove vanity URL"""
        if not ctx.guild.vanity_url_code:
            return await ctx.send_warning("This server doesn't have a vanity URL!")
        
        try:
            await ctx.guild.edit(vanity_url_code=None, reason=f"Vanity URL removed by {ctx.author}")
            
            embed = discord.Embed(
                title="🗑️ Vanity URL Removed",
                description="Vanity URL has been removed from this server.",
                color=0xff0000
            )
            
            await ctx.reply(embed=embed)
            
        except Exception as e:
            await ctx.send_warning(f"Failed to remove vanity URL: {e}")

    @vanity.command(
        name="info",
        description="Show vanity URL info"
    )
    async def vanity_info(self, ctx):
        """Show vanity URL info"""
        embed = discord.Embed(
            title="🔗 Vanity URL Information",
            color=self.bot.color
        )
        
        if ctx.guild.vanity_url_code:
            embed.add_field(
                name="Current Vanity URL",
                value=f"**discord.gg/{ctx.guild.vanity_url_code}**",
                inline=False
            )
            
            # Get vanity URL usage if possible
            try:
                vanity_invite = await ctx.guild.vanity_invite()
                if vanity_invite:
                    embed.add_field(
                        name="Usage Statistics",
                        value=f"**Uses:** {vanity_invite.uses or 0}",
                        inline=True
                    )
            except:
                pass
        else:
            embed.add_field(
                name="Vanity URL",
                value="Not set",
                inline=False
            )
        
        embed.add_field(
            name="Boost Level",
            value=f"Level {ctx.guild.premium_tier}",
            inline=True
        )
        
        embed.add_field(
            name="Boost Count",
            value=f"{ctx.guild.premium_subscription_count}",
            inline=True
        )
        
        if ctx.guild.premium_tier < 3:
            embed.add_field(
                name="Requirements",
                value="Need boost level 3 for vanity URLs",
                inline=False
            )
        
        await ctx.reply(embed=embed)

    @vanity.command(
        name="check",
        description="Check if vanity code is available",
        usage="[code]"
    )
    async def vanity_check(self, ctx, code: str):
        """Check if vanity code is available"""
        # Validate code format
        if not self.validate_vanity_code(code):
            return await ctx.send_warning(
                "Invalid vanity code format! Must be 3-32 characters, only letters/numbers/hyphens, "
                "and cannot start or end with a hyphen."
            )
        
        # Check if server can use vanity URLs
        if ctx.guild.premium_tier < 3:
            return await ctx.send_warning("Server must be boost level 3 to use vanity URLs!")
        
        embed = discord.Embed(
            title="🔍 Vanity Code Check",
            color=self.bot.color
        )
        
        embed.add_field(
            name="Code",
            value=f"`{code}`",
            inline=True
        )
        
        # Note: We can't actually check availability without trying to set it
        # Discord doesn't provide an API endpoint to check availability
        from config.constants import Emojis
        embed.add_field(
            name="Status",
            value=f"{Emojis.warn} Cannot check availability\nTry setting it to see if it's available",
            inline=False
        )

        embed.add_field(
            name="Format",
            value=f"{Emojis.success} Valid format" if self.validate_vanity_code(code) else f"{Emojis.error} Invalid format",
            inline=True
        )
        
        await ctx.reply(embed=embed)

    def validate_vanity_code(self, code: str) -> bool:
        """Validate vanity code format"""
        # Check length
        if len(code) < 3 or len(code) > 32:
            return False
        
        # Check characters (letters, numbers, hyphens only)
        if not re.match(r'^[a-zA-Z0-9-]+$', code):
            return False
        
        # Cannot start or end with hyphen
        if code.startswith('-') or code.endswith('-'):
            return False
        
        # Cannot have consecutive hyphens
        if '--' in code:
            return False
        
        return True

    @commands.command(
        name="invite",
        description="Get server invite link",
        aliases=["inv"]
    )
    async def server_invite(self, ctx):
        """Get server invite link"""
        embed = discord.Embed(
            title="🔗 Server Invite",
            color=self.bot.color
        )
        
        # Check if server has vanity URL
        if ctx.guild.vanity_url_code:
            embed.add_field(
                name="Vanity URL",
                value=f"**discord.gg/{ctx.guild.vanity_url_code}**",
                inline=False
            )
        
        # Create a regular invite
        try:
            # Try to find an existing permanent invite
            invites = await ctx.guild.invites()
            permanent_invite = None
            
            for invite in invites:
                if invite.max_age == 0 and invite.max_uses == 0:
                    permanent_invite = invite
                    break
            
            if not permanent_invite:
                # Create a new permanent invite
                permanent_invite = await ctx.channel.create_invite(
                    max_age=0,
                    max_uses=0,
                    reason="Server invite command"
                )
            
            embed.add_field(
                name="Regular Invite",
                value=f"**{permanent_invite.url}**",
                inline=False
            )
            
        except discord.Forbidden:
            embed.add_field(
                name="Regular Invite",
                value="❌ Cannot create invite (missing permissions)",
                inline=False
            )
        except Exception as e:
            embed.add_field(
                name="Regular Invite",
                value=f"❌ Error creating invite: {e}",
                inline=False
            )
        
        embed.set_thumbnail(url=ctx.guild.icon.url if ctx.guild.icon else None)
        embed.set_footer(text=f"Server: {ctx.guild.name}")
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="createinvite",
        description="Create a custom invite",
        usage="<max_uses> <max_age_hours>",
        aliases=["makeinvite"]
    )
    @Perms.get_perms("create_instant_invite")
    async def create_invite(self, ctx, max_uses: int = 0, max_age_hours: int = 0):
        """Create a custom invite"""
        if max_uses < 0 or max_uses > 100:
            return await ctx.send_warning("Max uses must be between 0-100!")
        
        if max_age_hours < 0 or max_age_hours > 168:  # Max 1 week
            return await ctx.send_warning("Max age must be between 0-168 hours!")
        
        max_age_seconds = max_age_hours * 3600 if max_age_hours > 0 else 0
        
        try:
            invite = await ctx.channel.create_invite(
                max_age=max_age_seconds,
                max_uses=max_uses,
                reason=f"Custom invite created by {ctx.author}"
            )
            
            embed = discord.Embed(
                title="✅ Invite Created",
                color=0x00ff00
            )
            
            embed.add_field(
                name="Invite URL",
                value=f"**{invite.url}**",
                inline=False
            )
            
            embed.add_field(
                name="Max Uses",
                value=f"{max_uses}" if max_uses > 0 else "Unlimited",
                inline=True
            )
            
            embed.add_field(
                name="Expires",
                value=f"In {max_age_hours} hours" if max_age_hours > 0 else "Never",
                inline=True
            )
            
            await ctx.reply(embed=embed)
            
        except discord.Forbidden:
            await ctx.send_warning("I don't have permission to create invites!")
        except Exception as e:
            await ctx.send_warning(f"Failed to create invite: {e}")


async def setup(bot):
    await bot.add_cog(Vanity(bot))
