import discord
from discord.ext import commands
from tools.checks import Perms
from tools.utils import Whitelist
import datetime


def is_antinuke():
    async def predicate(ctx: commands.Context):
        check = await ctx.bot.db.fetchrow(
            "SELECT * FROM antinuke_toggle WHERE guild_id = $1", ctx.guild.id
        )
        if not check:
            await ctx.send_warning("AntiNuke is **not** enabled in this server")
            return False
        return True
    return commands.check(predicate)


def can_manage():
    async def predicate(ctx: commands.Context):
        if ctx.author.id == ctx.guild.owner_id:
            return True
        
        check = await ctx.bot.db.fetchrow(
            "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
            ctx.guild.id, "antinuke", ctx.author.id, "user"
        )
        
        if not check:
            await ctx.send_warning("You are not an **antinuke admin**")
            return False
        return True
    return commands.check(predicate)


class AntiNuke(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def sendlogs(self, action: str, user: discord.Member, punishment: str):
        """Send antinuke logs"""
        try:
            check = await self.bot.db.fetchrow(
                "SELECT log_channel FROM guild_config WHERE guild_id = $1",
                user.guild.id
            )
            
            if check and check['log_channel']:
                channel = self.bot.get_channel(check['log_channel'])
                if channel:
                    embed = discord.Embed(
                        title="🛡️ AntiNuke Alert",
                        color=0xff0000,
                        timestamp=discord.utils.utcnow()
                    )
                    embed.add_field(name="Action", value=action, inline=True)
                    embed.add_field(name="User", value=f"{user} ({user.id})", inline=True)
                    embed.add_field(name="Punishment", value=punishment, inline=True)
                    embed.set_thumbnail(url=user.display_avatar.url)
                    
                    await channel.send(embed=embed)
        except Exception as e:
            print(f"AntiNuke log error: {e}")

    @commands.Cog.listener()
    async def on_member_ban(self, guild, user):
        """Monitor ban events"""
        if not guild.me.guild_permissions.view_audit_log:
            return
        
        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke WHERE guild_id = $1 AND module = $2",
            guild.id, "ban"
        )
        
        if not check:
            return
        
        async for entry in guild.audit_logs(action=discord.AuditLogAction.ban, limit=1):
            if entry.user.id == self.bot.user.id:
                return
            
            # Check if user is whitelisted
            whitelist_check = await self.bot.db.fetchrow(
                "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
                guild.id, "antinuke", entry.user.id, "user"
            )
            
            if whitelist_check or entry.user.id == guild.owner_id:
                return
            
            punishment = check["punishment"]
            
            if entry.user.top_role.position >= guild.me.top_role.position:
                return
            
            if punishment == "ban":
                await entry.user.ban(reason="AntiNuke: Unauthorized ban")
            elif punishment == "kick":
                await entry.user.kick(reason="AntiNuke: Unauthorized ban")
            else:  # strip
                await entry.user.edit(
                    roles=[
                        role for role in entry.user.roles
                        if not role.permissions.administrator
                        and not role.permissions.ban_members
                        and not role.permissions.kick_members
                        and not role.permissions.manage_guild
                    ],
                    reason="AntiNuke: Unauthorized ban"
                )
            
            await self.sendlogs("Unauthorized Ban", entry.user, punishment)

    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Monitor kick events"""
        if not member.guild.me.guild_permissions.view_audit_log:
            return
        
        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke WHERE guild_id = $1 AND module = $2",
            member.guild.id, "kick"
        )
        
        if not check:
            return
        
        async for entry in member.guild.audit_logs(action=discord.AuditLogAction.kick, limit=1):
            if entry.user.id == self.bot.user.id:
                return
            
            # Check if user is whitelisted
            whitelist_check = await self.bot.db.fetchrow(
                "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
                member.guild.id, "antinuke", entry.user.id, "user"
            )
            
            if whitelist_check or entry.user.id == member.guild.owner_id:
                return
            
            punishment = check["punishment"]
            
            if entry.user.top_role.position >= member.guild.me.top_role.position:
                return
            
            if punishment == "ban":
                await entry.user.ban(reason="AntiNuke: Unauthorized kick")
            elif punishment == "kick":
                await entry.user.kick(reason="AntiNuke: Unauthorized kick")
            else:  # strip
                await entry.user.edit(
                    roles=[
                        role for role in entry.user.roles
                        if not role.permissions.administrator
                        and not role.permissions.ban_members
                        and not role.permissions.kick_members
                        and not role.permissions.manage_guild
                    ],
                    reason="AntiNuke: Unauthorized kick"
                )
            
            await self.sendlogs("Unauthorized Kick", entry.user, punishment)

    @commands.Cog.listener()
    async def on_guild_channel_delete(self, channel):
        """Monitor channel deletion"""
        if not channel.guild.me.guild_permissions.view_audit_log:
            return
        
        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke WHERE guild_id = $1 AND module = $2",
            channel.guild.id, "channel"
        )
        
        if not check:
            return
        
        async for entry in channel.guild.audit_logs(action=discord.AuditLogAction.channel_delete, limit=1):
            if entry.user.id == self.bot.user.id:
                return
            
            # Check if user is whitelisted
            whitelist_check = await self.bot.db.fetchrow(
                "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
                channel.guild.id, "antinuke", entry.user.id, "user"
            )
            
            if whitelist_check or entry.user.id == channel.guild.owner_id:
                return
            
            punishment = check["punishment"]
            
            if entry.user.top_role.position >= channel.guild.me.top_role.position:
                return
            
            if punishment == "ban":
                await entry.user.ban(reason="AntiNuke: Unauthorized channel deletion")
            elif punishment == "kick":
                await entry.user.kick(reason="AntiNuke: Unauthorized channel deletion")
            else:  # strip
                await entry.user.edit(
                    roles=[
                        role for role in entry.user.roles
                        if not role.permissions.administrator
                        and not role.permissions.manage_channels
                        and not role.permissions.manage_guild
                    ],
                    reason="AntiNuke: Unauthorized channel deletion"
                )
            
            await self.sendlogs("Unauthorized Channel Deletion", entry.user, punishment)

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Monitor bot additions"""
        if not member.bot:
            return
        
        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke WHERE guild_id = $1 AND module = $2",
            member.guild.id, "antibot"
        )
        
        if not check:
            return
        
        # Check if bot is whitelisted
        whitelist_check = await self.bot.db.fetchrow(
            "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
            member.guild.id, "antibot", member.id, "user"
        )
        
        if whitelist_check:
            return
        
        # Kick the bot
        await member.kick(reason="AntiNuke: Unauthorized bot")
        
        # Find who added the bot
        async for entry in member.guild.audit_logs(action=discord.AuditLogAction.bot_add, limit=1):
            if entry.user.id == member.guild.owner_id:
                return
            
            # Check if user is whitelisted
            user_whitelist = await self.bot.db.fetchrow(
                "SELECT * FROM whitelist WHERE guild_id = $1 AND module = $2 AND object_id = $3 AND mode = $4",
                member.guild.id, "antinuke", entry.user.id, "user"
            )
            
            if user_whitelist:
                return
            
            punishment = check["punishment"]
            
            if entry.user.top_role.position >= member.guild.me.top_role.position:
                return
            
            if punishment == "ban":
                await entry.user.ban(reason="AntiNuke: Added unauthorized bot")
            elif punishment == "kick":
                await entry.user.kick(reason="AntiNuke: Added unauthorized bot")
            else:  # strip
                await entry.user.edit(
                    roles=[
                        role for role in entry.user.roles
                        if not role.permissions.administrator
                        and not role.permissions.manage_guild
                    ],
                    reason="AntiNuke: Added unauthorized bot"
                )
            
            await self.sendlogs("Added Unauthorized Bot", entry.user, punishment)

    @commands.group(
        name="antinuke",
        description="AntiNuke protection system",
        invoke_without_command=True,
        aliases=["an"]
    )
    async def antinuke(self, ctx):
        """AntiNuke protection system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🛡️ AntiNuke System",
                description="Protect your server from malicious actions",
                color=self.bot.color
            )

            embed.add_field(
                name="Commands",
                value="`antinuke enable` - Enable AntiNuke\n"
                      "`antinuke disable` - Disable AntiNuke\n"
                      "`antinuke settings` - View current settings\n"
                      "`antinuke admin add/remove` - Manage admins\n"
                      "`antinuke ban/kick/channel/botadd` - Configure modules",
                inline=False
            )

            await ctx.reply(embed=embed)

    @antinuke.command(
        name="enable",
        description="Enable AntiNuke protection"
    )
    @Perms.get_perms("administrator")
    async def antinuke_enable(self, ctx):
        """Enable AntiNuke protection"""
        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke_toggle WHERE guild_id = $1", ctx.guild.id
        )

        if check:
            return await ctx.send_warning("AntiNuke is already **enabled**")

        await self.bot.db.execute(
            "INSERT INTO antinuke_toggle (guild_id) VALUES ($1)", ctx.guild.id
        )

        await ctx.send_success("AntiNuke has been **enabled**")

    @antinuke.command(
        name="disable",
        description="Disable AntiNuke protection"
    )
    @can_manage()
    @is_antinuke()
    async def antinuke_disable(self, ctx):
        """Disable AntiNuke protection"""
        await self.bot.db.execute(
            "DELETE FROM antinuke_toggle WHERE guild_id = $1", ctx.guild.id
        )

        await ctx.send_success("AntiNuke has been **disabled**")

    @antinuke.command(
        name="settings",
        description="View AntiNuke settings"
    )
    @is_antinuke()
    async def antinuke_settings(self, ctx):
        """View AntiNuke settings"""
        settings = {
            "ban": "❌",
            "kick": "❌",
            "channel": "❌",
            "antibot": "❌"
        }

        results = await self.bot.db.fetch(
            "SELECT * FROM antinuke WHERE guild_id = $1", ctx.guild.id
        )

        for result in results:
            if result["module"] in settings:
                settings[result["module"]] = "✅"

        embed = discord.Embed(
            title=f"🛡️ AntiNuke Settings for {ctx.guild.name}",
            color=self.bot.color
        )

        embed.add_field(
            name="Protection Modules",
            value=f"**Anti Ban:** {settings['ban']}\n"
                  f"**Anti Kick:** {settings['kick']}\n"
                  f"**Anti Channel Delete:** {settings['channel']}\n"
                  f"**Anti Bot Add:** {settings['antibot']}",
            inline=False
        )

        await ctx.reply(embed=embed)

    @antinuke.group(
        name="admin",
        description="Manage AntiNuke admins",
        invoke_without_command=True
    )
    @is_antinuke()
    async def antinuke_admin(self, ctx):
        """Manage AntiNuke admins"""
        embed = discord.Embed(
            title="👑 AntiNuke Admin Management",
            description="Manage users who can configure AntiNuke",
            color=self.bot.color
        )

        embed.add_field(
            name="Commands",
            value="`antinuke admin add [user]` - Add admin\n"
                  "`antinuke admin remove [user]` - Remove admin\n"
                  "`antinuke admin list` - List admins",
            inline=False
        )

        await ctx.reply(embed=embed)

    @antinuke_admin.command(
        name="add",
        description="Add AntiNuke admin",
        usage="[member]"
    )
    @can_manage()
    async def admin_add(self, ctx, member: discord.Member):
        """Add AntiNuke admin"""
        await Whitelist.whitelist_things(ctx, "antinuke", member)

    @antinuke_admin.command(
        name="remove",
        description="Remove AntiNuke admin",
        usage="[member]"
    )
    @can_manage()
    async def admin_remove(self, ctx, member: discord.Member):
        """Remove AntiNuke admin"""
        await Whitelist.unwhitelist_things(ctx, "antinuke", member)

    @antinuke.group(
        name="ban",
        description="Configure anti-ban protection",
        invoke_without_command=True
    )
    @is_antinuke()
    async def antinuke_ban(self, ctx):
        """Configure anti-ban protection"""
        embed = discord.Embed(
            title="🚫 Anti-Ban Protection",
            description="Protect against unauthorized bans",
            color=self.bot.color
        )

        embed.add_field(
            name="Commands",
            value="`antinuke ban enable [punishment]` - Enable protection\n"
                  "`antinuke ban disable` - Disable protection\n"
                  "**Punishments:** ban, kick, strip",
            inline=False
        )

        await ctx.reply(embed=embed)

    @antinuke_ban.command(
        name="enable",
        description="Enable anti-ban protection",
        usage="[punishment]"
    )
    @can_manage()
    async def ban_enable(self, ctx, punishment: str):
        """Enable anti-ban protection"""
        if punishment.lower() not in ["ban", "kick", "strip"]:
            return await ctx.send_warning("Punishment must be **ban**, **kick**, or **strip**")

        check = await self.bot.db.fetchrow(
            "SELECT * FROM antinuke WHERE guild_id = $1 AND module = $2",
            ctx.guild.id, "ban"
        )

        if check:
            await self.bot.db.execute(
                "UPDATE antinuke SET punishment = $1 WHERE guild_id = $2 AND module = $3",
                punishment.lower(), ctx.guild.id, "ban"
            )
        else:
            await self.bot.db.execute(
                "INSERT INTO antinuke (guild_id, module, punishment) VALUES ($1, $2, $3)",
                ctx.guild.id, "ban", punishment.lower()
            )

        await ctx.send_success(f"Anti-ban protection **enabled** with punishment: **{punishment}**")

    @antinuke_ban.command(
        name="disable",
        description="Disable anti-ban protection"
    )
    @can_manage()
    async def ban_disable(self, ctx):
        """Disable anti-ban protection"""
        await self.bot.db.execute(
            "DELETE FROM antinuke WHERE guild_id = $1 AND module = $2",
            ctx.guild.id, "ban"
        )

        await ctx.send_success("Anti-ban protection **disabled**")


async def setup(bot):
    await bot.add_cog(AntiNuke(bot))
