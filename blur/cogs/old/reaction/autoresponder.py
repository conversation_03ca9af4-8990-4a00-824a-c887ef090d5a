import discord
from discord.ext import commands
from tools.checks import Perms
import re
import aiomysql


class AutoResponder(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle autoresponder triggers"""
        if message.author.bot:
            return
        
        if not message.guild:
            return
        
        # Get all autoresponders for this guild
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoresponders WHERE guild_id = %s",
                    (message.guild.id,)
                )
                autoresponders = await cursor.fetchall()
        
        for ar in autoresponders:
            trigger = ar['trigger'].lower()
            content = message.content.lower()
            
            # Check if trigger matches
            triggered = False
            
            if ar['match_type'] == 'exact':
                triggered = content == trigger
            elif ar['match_type'] == 'contains':
                triggered = trigger in content
            elif ar['match_type'] == 'startswith':
                triggered = content.startswith(trigger)
            elif ar['match_type'] == 'endswith':
                triggered = content.endswith(trigger)
            elif ar['match_type'] == 'regex':
                try:
                    triggered = bool(re.search(trigger, content))
                except:
                    continue
            
            if triggered:
                # Send response
                response = ar['response']
                
                # Replace variables
                response = response.replace("{user}", message.author.mention)
                response = response.replace("{server}", message.guild.name)
                response = response.replace("{channel}", message.channel.mention)
                
                try:
                    if ar['delete_trigger']:
                        await message.delete()
                except:
                    pass
                
                await message.channel.send(response)
                break  # Only trigger first match

    @commands.group(
        name="autoresponder",
        description="Autoresponder system",
        invoke_without_command=True,
        aliases=["ar"]
    )
    @Perms.get_perms("manage_guild")
    async def autoresponder(self, ctx):
        """Autoresponder system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🤖 AutoResponder System",
                description="Automatically respond to messages",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`ar add [trigger] [response]` - Add autoresponder\n"
                      "`ar remove [trigger]` - Remove autoresponder\n"
                      "`ar list` - List all autoresponders\n"
                      "`ar edit [trigger] [new_response]` - Edit response\n"
                      "`ar settings [trigger]` - Configure settings",
                inline=False
            )
            
            embed.add_field(
                name="Variables",
                value="`{user}` - Mention the user\n"
                      "`{server}` - Server name\n"
                      "`{channel}` - Channel mention",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @autoresponder.command(
        name="add",
        description="Add autoresponder",
        usage="[trigger] [response]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_add(self, ctx, trigger: str, *, response: str):
        """Add autoresponder"""
        # Check if trigger already exists
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoresponders WHERE guild_id = %s AND trigger = %s",
                    (ctx.guild.id, trigger)
                )
                existing = await cursor.fetchone()
        
        if existing:
            return await ctx.send_warning("Autoresponder with that trigger already exists!")
        
        # Add autoresponder
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO autoresponders (guild_id, trigger, response, match_type, delete_trigger) VALUES (%s, %s, %s, %s, %s)",
                    (ctx.guild.id, trigger, response, 'contains', False)
                )
        
        await ctx.send_success(f"Added autoresponder for trigger: `{trigger}`")

    @autoresponder.command(
        name="remove",
        description="Remove autoresponder",
        usage="[trigger]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_remove(self, ctx, *, trigger: str):
        """Remove autoresponder"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM autoresponders WHERE guild_id = %s AND trigger = %s",
                    (ctx.guild.id, trigger)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Autoresponder not found!")
        
        await ctx.send_success(f"Removed autoresponder: `{trigger}`")

    @autoresponder.command(
        name="list",
        description="List all autoresponders"
    )
    @Perms.get_perms("manage_guild")
    async def ar_list(self, ctx):
        """List all autoresponders"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoresponders WHERE guild_id = %s ORDER BY trigger",
                    (ctx.guild.id,)
                )
                autoresponders = await cursor.fetchall()
        
        if not autoresponders:
            return await ctx.send_warning("No autoresponders found!")
        
        embed = discord.Embed(
            title="🤖 AutoResponders",
            color=self.bot.color
        )
        
        description = ""
        for i, ar in enumerate(autoresponders[:10], 1):
            description += f"{i}. `{ar['trigger']}` → {ar['response'][:50]}{'...' if len(ar['response']) > 50 else ''}\n"
        
        if len(autoresponders) > 10:
            description += f"\n... and {len(autoresponders) - 10} more"
        
        embed.description = description
        await ctx.reply(embed=embed)

    @autoresponder.command(
        name="edit",
        description="Edit autoresponder response",
        usage="[trigger] [new_response]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_edit(self, ctx, trigger: str, *, new_response: str):
        """Edit autoresponder response"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE autoresponders SET response = %s WHERE guild_id = %s AND trigger = %s",
                    (new_response, ctx.guild.id, trigger)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Autoresponder not found!")
        
        await ctx.send_success(f"Updated autoresponder: `{trigger}`")

    @autoresponder.command(
        name="settings",
        description="Configure autoresponder settings",
        usage="[trigger]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_settings(self, ctx, *, trigger: str):
        """Configure autoresponder settings"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoresponders WHERE guild_id = %s AND trigger = %s",
                    (ctx.guild.id, trigger)
                )
                ar_data = await cursor.fetchone()
        
        if not ar_data:
            return await ctx.send_warning("Autoresponder not found!")
        
        embed = discord.Embed(
            title=f"⚙️ Settings for: {trigger}",
            color=self.bot.color
        )
        
        embed.add_field(
            name="Current Settings",
            value=f"**Match Type:** {ar_data['match_type']}\n"
                  f"**Delete Trigger:** {ar_data['delete_trigger']}\n"
                  f"**Response:** {ar_data['response'][:100]}{'...' if len(ar_data['response']) > 100 else ''}",
            inline=False
        )
        
        embed.add_field(
            name="Commands",
            value=f"`ar matchtype {trigger} [exact/contains/startswith/endswith/regex]`\n"
                  f"`ar delete {trigger} [true/false]`",
            inline=False
        )
        
        await ctx.reply(embed=embed)

    @autoresponder.command(
        name="matchtype",
        description="Set match type for autoresponder",
        usage="[trigger] [type]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_matchtype(self, ctx, trigger: str, match_type: str):
        """Set match type for autoresponder"""
        valid_types = ['exact', 'contains', 'startswith', 'endswith', 'regex']
        
        if match_type.lower() not in valid_types:
            return await ctx.send_warning(f"Invalid match type! Use: {', '.join(valid_types)}")
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE autoresponders SET match_type = %s WHERE guild_id = %s AND trigger = %s",
                    (match_type.lower(), ctx.guild.id, trigger)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Autoresponder not found!")
        
        await ctx.send_success(f"Set match type to `{match_type}` for trigger: `{trigger}`")

    @autoresponder.command(
        name="delete",
        description="Set whether to delete trigger message",
        usage="[trigger] [true/false]"
    )
    @Perms.get_perms("manage_guild")
    async def ar_delete(self, ctx, trigger: str, delete: bool):
        """Set whether to delete trigger message"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE autoresponders SET delete_trigger = %s WHERE guild_id = %s AND trigger = %s",
                    (delete, ctx.guild.id, trigger)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Autoresponder not found!")
        
        status = "enabled" if delete else "disabled"
        await ctx.send_success(f"Delete trigger {status} for: `{trigger}`")

    @commands.group(
        name="autoreact",
        description="Auto reaction system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def autoreact(self, ctx):
        """Auto reaction system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="😀 AutoReact System",
                description="Automatically react to messages",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`autoreact add [trigger] [emoji]` - Add auto reaction\n"
                      "`autoreact remove [trigger]` - Remove auto reaction\n"
                      "`autoreact list` - List all auto reactions",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @autoreact.command(
        name="add",
        description="Add auto reaction",
        usage="[trigger] [emoji]"
    )
    @Perms.get_perms("manage_guild")
    async def autoreact_add(self, ctx, trigger: str, emoji: str):
        """Add auto reaction"""
        # Test if emoji is valid
        try:
            await ctx.message.add_reaction(emoji)
            await ctx.message.remove_reaction(emoji, ctx.guild.me)
        except:
            return await ctx.send_warning("Invalid emoji!")
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO autoreacts (guild_id, trigger, emoji) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE emoji = %s",
                    (ctx.guild.id, trigger, emoji, emoji)
                )
        
        await ctx.send_success(f"Added auto reaction {emoji} for trigger: `{trigger}`")

    @autoreact.command(
        name="remove",
        description="Remove auto reaction",
        usage="[trigger]"
    )
    @Perms.get_perms("manage_guild")
    async def autoreact_remove(self, ctx, *, trigger: str):
        """Remove auto reaction"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM autoreacts WHERE guild_id = %s AND trigger = %s",
                    (ctx.guild.id, trigger)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Auto reaction not found!")
        
        await ctx.send_success(f"Removed auto reaction: `{trigger}`")

    @autoreact.command(
        name="list",
        description="List all auto reactions"
    )
    @Perms.get_perms("manage_guild")
    async def autoreact_list(self, ctx):
        """List all auto reactions"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoreacts WHERE guild_id = %s ORDER BY trigger",
                    (ctx.guild.id,)
                )
                autoreacts = await cursor.fetchall()
        
        if not autoreacts:
            return await ctx.send_warning("No auto reactions found!")
        
        embed = discord.Embed(
            title="😀 AutoReacts",
            color=self.bot.color
        )
        
        description = ""
        for i, ar in enumerate(autoreacts[:15], 1):
            description += f"{i}. `{ar['trigger']}` → {ar['emoji']}\n"
        
        if len(autoreacts) > 15:
            description += f"\n... and {len(autoreacts) - 15} more"
        
        embed.description = description
        await ctx.reply(embed=embed)

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle autoreact triggers"""
        if message.author.bot:
            return
        
        if not message.guild:
            return
        
        # Get all autoreacts for this guild
        async with self.bot.db.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(
                    "SELECT * FROM autoreacts WHERE guild_id = %s",
                    (message.guild.id,)
                )
                autoreacts = await cursor.fetchall()
        
        for ar in autoreacts:
            if ar['trigger'].lower() in message.content.lower():
                try:
                    await message.add_reaction(ar['emoji'])
                except:
                    pass


async def setup(bot):
    await bot.add_cog(AutoResponder(bot))
