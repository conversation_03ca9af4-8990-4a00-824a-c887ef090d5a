import discord
from discord.ext import commands
from tools.checks import Perms
import asyncio
from discord.ui import View, Button


class ReactionRoles(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction role addition"""
        if payload.user_id == self.bot.user.id:
            return
        
        # Check if this is a reaction role
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM reaction_roles WHERE guild_id = %s AND message_id = %s AND emoji = %s",
                    (payload.guild_id, payload.message_id, str(payload.emoji))
                )
                rr_data = await cursor.fetchone()
        
        if not rr_data:
            return
        
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return
        
        member = guild.get_member(payload.user_id)
        if not member:
            return
        
        role = guild.get_role(rr_data[3])  # rr_data[3] is role_id
        if not role:
            return
        
        try:
            await member.add_roles(role, reason="Reaction role")
        except discord.Forbidden:
            pass

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction role removal"""
        if payload.user_id == self.bot.user.id:
            return
        
        # Check if this is a reaction role
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM reaction_roles WHERE guild_id = %s AND message_id = %s AND emoji = %s",
                    (payload.guild_id, payload.message_id, str(payload.emoji))
                )
                rr_data = await cursor.fetchone()
        
        if not rr_data:
            return
        
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return
        
        member = guild.get_member(payload.user_id)
        if not member:
            return
        
        role = guild.get_role(rr_data[3])  # rr_data[3] is role_id
        if not role:
            return
        
        try:
            await member.remove_roles(role, reason="Reaction role removed")
        except discord.Forbidden:
            pass

    @commands.group(
        name="reactionrole",
        description="Reaction role system",
        invoke_without_command=True,
        aliases=["rr"]
    )
    @Perms.get_perms("manage_roles")
    async def reactionrole(self, ctx):
        """Reaction role system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="⚡ Reaction Roles",
                description="Manage reaction roles",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`rr add [message_id] [emoji] [role]` - Add reaction role\n"
                      "`rr remove [message_id] [emoji]` - Remove reaction role\n"
                      "`rr list` - List all reaction roles\n"
                      "`rr clear [message_id]` - Clear all reactions from message\n"
                      "`rr panel [title] [description]` - Create reaction role panel",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @reactionrole.command(
        name="add",
        description="Add reaction role",
        usage="[message_id] [emoji] [role]"
    )
    @Perms.get_perms("manage_roles")
    async def rr_add(self, ctx, message_id: int, emoji: str, role: discord.Role):
        """Add reaction role"""
        # Check if role is manageable
        if role >= ctx.guild.me.top_role:
            return await ctx.send_warning("That role is higher than my highest role!")
        
        if role >= ctx.author.top_role and ctx.author.id != ctx.guild.owner_id:
            return await ctx.send_warning("That role is higher than your highest role!")
        
        # Try to get the message
        try:
            message = await ctx.channel.fetch_message(message_id)
        except discord.NotFound:
            return await ctx.send_warning("Message not found!")
        
        # Add reaction to message
        try:
            await message.add_reaction(emoji)
        except discord.HTTPException:
            return await ctx.send_warning("Invalid emoji or couldn't add reaction!")
        
        # Store in database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO reaction_roles (guild_id, message_id, emoji, role_id) VALUES (%s, %s, %s, %s) ON DUPLICATE KEY UPDATE role_id = %s",
                    (ctx.guild.id, message_id, emoji, role.id, role.id)
                )
        
        await ctx.send_success(f"Added reaction role: {emoji} → {role.mention}")

    @reactionrole.command(
        name="remove",
        description="Remove reaction role",
        usage="[message_id] [emoji]"
    )
    @Perms.get_perms("manage_roles")
    async def rr_remove(self, ctx, message_id: int, emoji: str):
        """Remove reaction role"""
        # Remove from database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM reaction_roles WHERE guild_id = %s AND message_id = %s AND emoji = %s",
                    (ctx.guild.id, message_id, emoji)
                )
                result = cursor.rowcount
        
        if result == 0:
            return await ctx.send_warning("Reaction role not found!")
        
        # Try to remove reaction from message
        try:
            message = await ctx.channel.fetch_message(message_id)
            await message.clear_reaction(emoji)
        except:
            pass
        
        await ctx.send_success(f"Removed reaction role: {emoji}")

    @reactionrole.command(
        name="list",
        description="List all reaction roles"
    )
    @Perms.get_perms("manage_roles")
    async def rr_list(self, ctx):
        """List all reaction roles"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM reaction_roles WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                rr_data = await cursor.fetchall()
        
        if not rr_data:
            return await ctx.send_warning("No reaction roles found!")
        
        embed = discord.Embed(
            title="⚡ Reaction Roles",
            color=self.bot.color
        )
        
        description = ""
        for rr in rr_data[:10]:  # Limit to 10
            # rr is tuple: (guild_id, message_id, emoji, role_id)
            role = ctx.guild.get_role(rr[3])  # role_id
            if role:
                description += f"{rr[2]} → {role.mention} (Message: {rr[1]})\n"  # emoji, role, message_id
        
        if len(rr_data) > 10:
            description += f"\n... and {len(rr_data) - 10} more"
        
        embed.description = description
        await ctx.reply(embed=embed)

    @reactionrole.command(
        name="clear",
        description="Clear all reactions from message",
        usage="[message_id]"
    )
    @Perms.get_perms("manage_roles")
    async def rr_clear(self, ctx, message_id: int):
        """Clear all reaction roles from message"""
        # Remove from database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM reaction_roles WHERE guild_id = %s AND message_id = %s",
                    (ctx.guild.id, message_id)
                )
        
        # Try to clear reactions from message
        try:
            message = await ctx.channel.fetch_message(message_id)
            await message.clear_reactions()
        except:
            pass
        
        await ctx.send_success("Cleared all reaction roles from message")

    @reactionrole.command(
        name="panel",
        description="Create reaction role panel",
        usage="[title] [description]"
    )
    @Perms.get_perms("manage_roles")
    async def rr_panel(self, ctx, title: str, *, description: str):
        """Create reaction role panel"""
        embed = discord.Embed(
            title=title,
            description=description,
            color=self.bot.color
        )
        
        embed.set_footer(text="React below to get roles!")
        
        message = await ctx.send(embed=embed)
        
        await ctx.send_success(f"Created reaction role panel! Message ID: `{message.id}`\nUse `{ctx.prefix}rr add {message.id} [emoji] [role]` to add roles.")



    # Button Role Commands
    @commands.group(
        name="buttonrole",
        description="Button role system",
        invoke_without_command=True,
        aliases=["br"]
    )
    @Perms.get_perms("manage_roles")
    async def buttonrole(self, ctx):
        """Button role system"""
        try:
            if ctx.invoked_subcommand is None:
                embed = discord.Embed(
                    title="🔘 Button Role System",
                    description="Create interactive button roles",
                    color=self.bot.color
                )

                embed.add_field(
                    name="Commands",
                    value="`buttonrole create [title] [description]` - Create button role panel\n"
                          "`buttonrole add [message_id] [role] [label] [emoji] [style]` - Add button\n"
                          "`buttonrole remove [message_id] [button_id]` - Remove button\n"
                          "`buttonrole list` - List button role panels",
                    inline=False
                )

                embed.add_field(
                    name="Button Styles",
                    value="`primary` - Blue\n`secondary` - Gray\n`success` - Green\n`danger` - Red",
                    inline=False
                )

                await ctx.reply(embed=embed)
        except Exception as e:
            print(f"Buttonrole command error: {e}")
            await ctx.send_error("An error occurred while processing the buttonrole command.")

    @buttonrole.command(
        name="create",
        description="Create a button role panel",
        usage="[title] [description]"
    )
    @Perms.get_perms("manage_roles")
    async def buttonrole_create(self, ctx, title: str, *, description: str = None):
        """Create a button role panel"""
        try:
            embed = discord.Embed(
                title=title,
                description=description or "Click a button to get a role!",
                color=self.bot.color
            )

            view = ButtonRoleView(self.bot, [])
            message = await ctx.send(embed=embed, view=view)

            # Store the panel in database
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO button_role_panels (guild_id, message_id, title, description) VALUES (%s, %s, %s, %s)",
                        (ctx.guild.id, message.id, title, description)
                    )

            await ctx.send_success(f"Created button role panel! Message ID: `{message.id}`")
        except Exception as e:
            print(f"Buttonrole create error: {e}")
            await ctx.send_error("Failed to create button role panel.")

    @buttonrole.command(
        name="add",
        description="Add a button to a panel",
        usage="[message_id] [role] [label] <emoji> <style>"
    )
    @Perms.get_perms("manage_roles")
    async def buttonrole_add(self, ctx, message_id: int, role: discord.Role, label: str, emoji: str = None, style: str = "primary"):
        """Add a button to a button role panel"""
        # Check if panel exists
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM button_role_panels WHERE guild_id = %s AND message_id = %s",
                    (ctx.guild.id, message_id)
                )
                panel = await cursor.fetchone()

        if not panel:
            return await ctx.send_error("Button role panel not found!")

        # Validate style
        style_map = {
            "primary": discord.ButtonStyle.primary,
            "secondary": discord.ButtonStyle.secondary,
            "success": discord.ButtonStyle.success,
            "danger": discord.ButtonStyle.danger
        }

        if style.lower() not in style_map:
            return await ctx.send_error("Invalid style! Use: primary, secondary, success, or danger")

        # Add button to database
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO button_roles (guild_id, message_id, role_id, label, emoji, style) VALUES (%s, %s, %s, %s, %s, %s)",
                    (ctx.guild.id, message_id, role.id, label, emoji, style.lower())
                )

        # Update the message
        await self._update_button_panel(ctx.guild, message_id)

        await ctx.send_success(f"Added button for {role.mention}")

    async def _update_button_panel(self, guild, message_id):
        """Update a button role panel"""
        try:
            # Get panel data
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM button_role_panels WHERE guild_id = %s AND message_id = %s",
                        (guild.id, message_id)
                    )
                    panel = await cursor.fetchone()

            if not panel:
                return

            # Get buttons
            async with self.bot.db.acquire() as conn2:
                async with conn2.cursor() as cursor2:
                    await cursor2.execute(
                        "SELECT * FROM button_roles WHERE guild_id = %s AND message_id = %s",
                        (guild.id, message_id)
                    )
                    buttons = await cursor2.fetchall()

            # Find the message
            for channel in guild.text_channels:
                try:
                    message = await channel.fetch_message(message_id)
                    break
                except:
                    continue
            else:
                return

            # Create new view
            view = ButtonRoleView(self.bot, buttons)

            # Update message
            # panel is tuple: (guild_id, message_id, title, description)
            embed = discord.Embed(
                title=panel[2],  # title
                description=panel[3] or "Click a button to get a role!",  # description
                color=self.bot.color
            )

            await message.edit(embed=embed, view=view)

        except Exception as e:
            print(f"Error updating button panel: {e}")


class ButtonRoleView(View):
    def __init__(self, bot, buttons):
        super().__init__(timeout=None)
        self.bot = bot

        style_map = {
            "primary": discord.ButtonStyle.primary,
            "secondary": discord.ButtonStyle.secondary,
            "success": discord.ButtonStyle.success,
            "danger": discord.ButtonStyle.danger
        }

        for button_data in buttons:
            # button_data is tuple: (id, guild_id, message_id, role_id, label, emoji, style)
            button = Button(
                label=button_data[4],  # label
                emoji=button_data[5],  # emoji
                style=style_map.get(button_data[6], discord.ButtonStyle.primary),  # style
                custom_id=f"buttonrole_{button_data[3]}"  # role_id
            )
            button.callback = self.button_callback
            self.add_item(button)

    async def button_callback(self, interaction):
        """Handle button role clicks"""
        try:
            role_id = int(interaction.data['custom_id'].split('_')[1])
            role = interaction.guild.get_role(role_id)

            if not role:
                return await interaction.response.send_message("Role not found!", ephemeral=True)

            member = interaction.user

            if role in member.roles:
                await member.remove_roles(role, reason="Button role removal")
                await interaction.response.send_message(f"Removed {role.mention}!", ephemeral=True)
            else:
                await member.add_roles(role, reason="Button role assignment")
                await interaction.response.send_message(f"Added {role.mention}!", ephemeral=True)

        except Exception as e:
            await interaction.response.send_message("An error occurred!", ephemeral=True)
            print(f"Button role error: {e}")


async def setup(bot):
    await bot.add_cog(ReactionRoles(bot))
