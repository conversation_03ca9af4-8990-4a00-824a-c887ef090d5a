import discord
from discord.ext.commands import Cog
from discord.ui import View, Button, Select


class HelpPaginationView(View):
    def __init__(self, pages, author_id, timeout=180):
        super().__init__(timeout=timeout)
        self.pages = pages
        self.current_page = 0
        self.author_id = author_id

        # Update button states
        self.update_buttons()

    def update_buttons(self):
        """Update button states based on current page"""
        self.previous_button.disabled = self.current_page == 0
        self.next_button.disabled = self.current_page == len(self.pages) - 1

        # Update page counter
        for item in self.children:
            if hasattr(item, 'label') and '/' in str(item.label):
                item.label = f"{self.current_page + 1}/{len(self.pages)}"

    @discord.ui.button(emoji="⬅️", style=discord.ButtonStyle.blurple)
    async def previous_button(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.author_id:
            return await interaction.response.send_message("This is not your help menu!", ephemeral=True)

        if self.current_page > 0:
            self.current_page -= 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(label="1/1", style=discord.ButtonStyle.gray, disabled=True)
    async def page_counter(self, interaction: discord.Interaction, button: Button):
        pass  # This button is just for display

    @discord.ui.button(emoji="➡️", style=discord.ButtonStyle.blurple)
    async def next_button(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.author_id:
            return await interaction.response.send_message("This is not your help menu!", ephemeral=True)

        if self.current_page < len(self.pages) - 1:
            self.current_page += 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(emoji="🗑️", style=discord.ButtonStyle.red)
    async def delete_button(self, interaction: discord.Interaction, button: Button):
        if interaction.user.id != self.author_id:
            return await interaction.response.send_message("This is not your help menu!", ephemeral=True)

        await interaction.response.edit_message(content="Help menu deleted.", embed=None, view=None)


class CategorySelectView(View):
    def __init__(self, bot, author_id, timeout=180):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.author_id = author_id

        # Create category select menu
        self.add_item(CategorySelect(bot, author_id))


class CategorySelect(Select):
    def __init__(self, bot, author_id):
        self.bot = bot
        self.author_id = author_id

        # Define command categories with emojis
        categories = {
            "🛡️ Moderation": "moderation",
            "📊 Information": "information",
            "⚙️ Server": "server",
            "🎮 Fun": "fun",
            "💰 Economy": "economy",
            "📈 Levels": "levels",
            "🎭 Roleplay": "roleplay",
            "🔧 Utility": "utility",
            "🎵 Music": "music",
            "🎪 Miscellaneous": "miscellaneous"
        }

        options = []
        for display_name, category in categories.items():
            # Count commands in this category
            count = len([cmd for cmd in bot.commands if self.get_command_category(cmd) == category])
            if count > 0:  # Only add categories that have commands
                options.append(discord.SelectOption(
                    label=display_name,
                    description=f"{count} commands",
                    value=category
                ))

        super().__init__(
            placeholder="Choose a command category...",
            options=options,
            min_values=1,
            max_values=1
        )

    def get_command_category(self, command):
        """Determine command category based on cog name or command properties"""
        if not command.cog:
            return "miscellaneous"

        cog_name = command.cog.__class__.__name__.lower()

        # Map cog names to categories
        category_mapping = {
            "moderation": "moderation",
            "automod": "moderation",
            "information": "information",
            "info": "information",
            "utility": "utility",
            "config": "server",
            "booster": "server",
            "vanity": "server",
            "fun": "fun",
            "games": "fun",
            "economy": "economy",
            "leveling": "levels",
            "roleplay": "roleplay",
            "music": "music",
            "owner": "utility",
            "help": "information"
        }

        return category_mapping.get(cog_name, "miscellaneous")

    async def callback(self, interaction: discord.Interaction):
        if interaction.user.id != self.author_id:
            return await interaction.response.send_message("This is not your help menu!", ephemeral=True)

        category = self.values[0]

        # Get commands for this category
        commands_in_category = []
        for command in self.bot.commands:
            if self.get_command_category(command) == category:
                commands_in_category.append(command)

        if not commands_in_category:
            return await interaction.response.send_message("No commands found in this category!", ephemeral=True)

        # Create help pages for this category
        help_cog = self.bot.get_cog("HelpCommands")
        if help_cog:
            await help_cog.show_category_commands(interaction, commands_in_category, category)


class HelpCommands(Cog):
    def __init__(self, bot):
        self.bot = bot

    def get_command_info(self, command, ctx=None):
        """Extract command information from command object"""
        if not command or not hasattr(command, 'name'):
            return {
                'name': 'Unknown',
                'description': 'No description available',
                'usage': ',unknown',
                'example': ',unknown',
                'aliases': [],
                'permissions': 'None'
            }

        # Get the bot prefix
        prefix = ','  # Default prefix
        if ctx and hasattr(ctx, 'prefix'):
            prefix = ctx.prefix
        elif ctx and ctx.bot:
            try:
                if hasattr(ctx.bot, 'get_prefix'):
                    prefix_result = ctx.bot.get_prefix(ctx.message)
                    if isinstance(prefix_result, list):
                        prefix = prefix_result[0] if prefix_result else ','
                    else:
                        prefix = prefix_result or ','
            except:
                prefix = ','

        # Get usage and example safely
        command_name = getattr(command, 'name', 'unknown')
        usage = getattr(command, 'usage', None)
        example = getattr(command, 'example', None)

        # Build usage string
        if usage:
            usage = f"{command_name} {usage}" if not usage.startswith(command_name) else usage
        else:
            usage = command_name

        # Build example string
        if example:
            example = f"{command_name} {example}" if not example.startswith(command_name) else example
        else:
            example = command_name

        # Add prefix if not present
        if not usage.startswith(prefix):
            usage = f"{prefix}{usage}"
        if not example.startswith(prefix):
            example = f"{prefix}{example}"

        info = {
            'name': command_name,
            'description': getattr(command, 'description', 'No description available') or 'No description available',
            'usage': usage,
            'example': example,
            'aliases': getattr(command, 'aliases', []) or [],
            'permissions': 'None'
        }

        # Extract permission from decorators
        for check in command.checks:
            check_str = str(check)
            if 'owner' in check_str.lower():
                info['permissions'] = 'Bot Owner'
                break
            elif 'manage_guild' in check_str.lower():
                info['permissions'] = 'Manage Server'
                break
            elif 'manage_roles' in check_str.lower():
                info['permissions'] = 'Manage Roles'
                break
            elif 'manage_messages' in check_str.lower():
                info['permissions'] = 'Manage Messages'
                break
            elif 'kick_members' in check_str.lower():
                info['permissions'] = 'Kick Members'
                break
            elif 'ban_members' in check_str.lower():
                info['permissions'] = 'Ban Members'
                break

        return info

    @discord.ext.commands.command(
        name="help",
        description="Show help for commands",
        usage="help [command]",
        aliases=['h']
    )
    async def help(self, ctx, *, command_name: str = None):
        """Show help for commands"""
        try:
            if not command_name:
                # Show main help menu with category selection
                await self.show_main_help(ctx)
                return

            # Special mappings for common aliases to group commands
            group_mappings = {
                'level': 'levels',  # ,help level -> show levels group
                'lvl': 'levels',    # ,help lvl -> show levels group
                'rank': 'levels',   # ,help rank -> show levels group (if user wants group help)
                'eco': 'economy',   # ,help eco -> show economy group (if exists)
                'mod': 'moderation' # ,help mod -> show moderation group (if exists)
            }

            # Check if the command name should be mapped to a group
            mapped_command_name = group_mappings.get(command_name.lower(), command_name.lower())

            # Find the command (try original name first, then mapped name)
            command = self.bot.get_command(command_name.lower())
            if not command and mapped_command_name != command_name.lower():
                command = self.bot.get_command(mapped_command_name)

            if not command:
                # Try to find if it's a partial match for a group command
                possible_groups = []
                for cmd in self.bot.commands:
                    if isinstance(cmd, discord.ext.commands.Group) and cmd.name:
                        # Check if command name matches or is similar
                        try:
                            if (command_name.lower() == cmd.name.lower() or
                                command_name.lower() in cmd.name.lower() or
                                cmd.name.lower().startswith(command_name.lower())):
                                possible_groups.append(cmd)
                        except (AttributeError, TypeError):
                            # Skip commands with None names or other issues
                            continue

                if possible_groups:
                    # If we found one exact match, show it
                    exact_match = next((cmd for cmd in possible_groups if cmd.name and cmd.name.lower() == command_name.lower()), None)
                    if exact_match:
                        return await self.show_group_help(ctx, exact_match)
                    # Otherwise show the first match
                    return await self.show_group_help(ctx, possible_groups[0])

                return await ctx.send_error(f"Command `{command_name}` not found!")

            # Special case: if user typed "level", "lvl", or "rank", prioritize the "levels" group
            if command_name.lower() in ['level', 'lvl', 'rank']:
                levels_group = self.bot.get_command('levels')
                if levels_group and isinstance(levels_group, discord.ext.commands.Group):
                    return await self.show_group_help(ctx, levels_group)

            # Check if it's a group command
            if isinstance(command, discord.ext.commands.Group):
                await self.show_group_help(ctx, command)
            else:
                # Check if this command is part of a group (has parent)
                if hasattr(command, 'parent') and command.parent:
                    # Show all subcommands of the parent group
                    await self.show_group_help(ctx, command.parent)
                else:
                    # Show specific command help
                    await self.show_command_help(ctx, command)

        except Exception as e:
            print(f"Help command error: {e}")
            await ctx.send_error(f"An error occurred while showing help. Please try again or contact support.")

    async def show_main_help(self, ctx):
        """Show main help menu with category selection"""
        embed = discord.Embed(
            title="🤖 Bot Help Menu",
            description="Select a category below to view commands, or use `,help <command>` for specific help.",
            color=self.bot.color
        )

        # Add some general info
        embed.add_field(
            name="📋 Quick Info",
            value=f"• **Total Commands:** {len([cmd for cmd in self.bot.commands])}\n"
                  f"• **Prefix:** `,`\n"
                  f"• **Support:** [Join Server](https://discord.gg/blur)",
            inline=False
        )

        embed.add_field(
            name="🔍 Usage Examples",
            value="`,help ban` - Show ban command help\n"
                  "`,help levels` - Show levels group commands\n"
                  "`,h economy` - Show economy commands",
            inline=False
        )

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.display_avatar.url)

        view = CategorySelectView(self.bot, ctx.author.id)
        await ctx.reply(embed=embed, view=view)

    async def show_category_commands(self, interaction, commands_list, category):
        """Show commands for a specific category with pagination"""
        # Sort commands alphabetically
        commands_list.sort(key=lambda x: x.name)

        # Create pages (10 commands per page)
        pages = []
        commands_per_page = 10

        for i in range(0, len(commands_list), commands_per_page):
            page_commands = commands_list[i:i + commands_per_page]

            # Category display names
            category_names = {
                "moderation": "🛡️ Moderation Commands",
                "information": "📊 Information Commands",
                "server": "⚙️ Server Commands",
                "fun": "🎮 Fun Commands",
                "economy": "💰 Economy Commands",
                "levels": "📈 Level Commands",
                "roleplay": "🎭 Roleplay Commands",
                "utility": "🔧 Utility Commands",
                "music": "🎵 Music Commands",
                "miscellaneous": "🎪 Miscellaneous Commands"
            }

            embed = discord.Embed(
                title=category_names.get(category, f"{category.title()} Commands"),
                color=self.bot.color
            )

            command_list = []
            for cmd in page_commands:
                info = self.get_command_info(cmd)
                aliases = f" ({', '.join(info['aliases'])})" if info['aliases'] else ""
                command_list.append(f"`,{cmd.name}`{aliases} - {info['description'][:50]}{'...' if len(info['description']) > 50 else ''}")

            embed.description = "\n".join(command_list)
            embed.set_footer(
                text=f"Page {len(pages) + 1} ∙ Use ,help <command> for detailed help ∙ Module: {category}",
                icon_url=interaction.user.display_avatar.url
            )

            pages.append(embed)

        if len(pages) == 1:
            await interaction.response.edit_message(embed=pages[0], view=None)
        else:
            # Add page numbers to footer
            for i, page in enumerate(pages, 1):
                page.set_footer(
                    text=f"Page {i}/{len(pages)} ({len(commands_list)} entries) ∙ Module: {category}",
                    icon_url=interaction.user.display_avatar.url
                )

            view = HelpPaginationView(pages, interaction.user.id)
            await interaction.response.edit_message(embed=pages[0], view=view)

    async def show_group_help(self, ctx, group):
        """Show help for a command group with pagination"""
        if not group or not hasattr(group, 'commands'):
            return await ctx.send_error("Invalid command group!")

        subcommands = list(group.commands)

        if not subcommands:
            group_name = getattr(group, 'name', 'unknown')
            return await ctx.send_error(f"No subcommands found for `{group_name}`")

        # Sort subcommands alphabetically
        subcommands.sort(key=lambda x: x.name)

        # Create pages - first page is group overview, then individual subcommands
        pages = []
        module_name = group.cog.__class__.__name__.lower() if group.cog and hasattr(group.cog, '__class__') else 'misc'
        total_pages = len(subcommands) + 1

        # First page: Group command overview
        group_info = self.get_command_info(group, ctx)
        group_aliases = ", ".join(group_info['aliases']) if group_info['aliases'] else "n/a"

        # Get group parameters (if any)
        group_params = "subcommand"
        if hasattr(group, 'usage') and group.usage:
            group_params = group.usage

        overview_embed = discord.Embed(
            title=f"Group Command: {group.name}",
            description=group_info['description'],
            color=self.bot.color
        )
        overview_embed.set_author(name=ctx.author.name, icon_url=ctx.author.display_avatar.url)
        overview_embed.add_field(name="Aliases", value=group_aliases, inline=True)
        overview_embed.add_field(name="Parameters", value=group_params, inline=True)
        overview_embed.add_field(name="Information", value=group_info['permissions'], inline=True)
        overview_embed.add_field(
            name="Usage",
            value=f"```Syntax: {group.name} (subcommand) <args>\nExample: {group.name} {subcommands[0].name if subcommands else 'subcommand'}```",
            inline=False
        )
        overview_embed.set_footer(text=f"Page 1/{total_pages} ({len(subcommands)} entries) ∙ Module: {module_name}")
        pages.append(overview_embed)

        # Individual subcommand pages
        for i, subcommand in enumerate(subcommands, 2):
            info = self.get_command_info(subcommand, ctx)
            aliases_text = ", ".join(info['aliases']) if info['aliases'] else "n/a"

            # Get parameters from usage
            params = "n/a"
            if hasattr(subcommand, 'usage') and subcommand.usage:
                params = subcommand.usage
            elif hasattr(subcommand, 'signature') and subcommand.signature:
                params = subcommand.signature

            embed = discord.Embed(
                title=f"Command: {group.name} {subcommand.name}",
                description=info['description'],
                color=self.bot.color
            )
            embed.set_author(name=ctx.author.name, icon_url=ctx.author.display_avatar.url)
            embed.add_field(name="Aliases", value=aliases_text, inline=True)
            embed.add_field(name="Parameters", value=params, inline=True)
            embed.add_field(name="Information", value=info['permissions'], inline=True)

            # Build usage syntax
            usage_syntax = f"{group.name} {subcommand.name}"
            if params != "n/a":
                usage_syntax += f" {params}"

            example_syntax = f"{group.name} {subcommand.name}"
            if hasattr(subcommand, 'example') and subcommand.example:
                example_syntax = f"{group.name} {subcommand.example}"
            elif subcommand.name == "add" and "role" in params.lower():
                example_syntax = f"{group.name} {subcommand.name} @Role 10"
            elif subcommand.name in ["reset", "cleanup", "lock", "unlock", "roles"]:
                example_syntax = f"{group.name} {subcommand.name}"

            embed.add_field(
                name="Usage",
                value=f"```Syntax: {usage_syntax}\nExample: {example_syntax}```",
                inline=False
            )
            embed.set_footer(text=f"Page {i}/{total_pages} ({len(subcommands)} entries) ∙ Module: {module_name}")
            pages.append(embed)

        if len(pages) == 1:
            await ctx.reply(embed=pages[0])
        else:
            view = HelpPaginationView(pages, ctx.author.id)
            await ctx.reply(embed=pages[0], view=view)

    async def show_command_help(self, ctx, command):
        """Show detailed help for a specific command"""
        info = self.get_command_info(command, ctx)

        # Format aliases
        aliases_text = ", ".join(info['aliases']) if info['aliases'] else "n/a"

        embed = discord.Embed(
            title=f"Command: {info['name']}",
            description=info['description'],
            color=self.bot.color
        )
        embed.set_author(name=ctx.author.name, icon_url=ctx.author.display_avatar.url)
        embed.add_field(name="Aliases", value=aliases_text, inline=True)
        embed.add_field(name="Parameters", value="See usage below", inline=True)
        embed.add_field(name="Information", value=info['permissions'], inline=True)
        embed.add_field(
            name="Usage",
            value=f"```Syntax: {info['usage']}\nExample: {info['example']}```",
            inline=False
        )
        embed.set_footer(
            text=f"Page 1/1 (1 entry) ∙ Module: {command.cog.__class__.__name__.lower() if command.cog else 'misc'}"
        )

        await ctx.reply(embed=embed)

    @discord.ext.commands.command(description="embed syntax help", help="utility")
    async def embedhelp(self, ctx):
        """Show embed syntax help"""
        embed = discord.Embed(
            title="📝 Embed Syntax Help",
            description="Learn how to create custom embeds",
            color=self.bot.color
        )

        embed.add_field(
            name="Basic Syntax",
            value="`{title: Your Title} $v {description: Your Description}`",
            inline=False
        )

        embed.add_field(
            name="Available Fields",
            value="`title:` - Embed title\n"
                  "`description:` - Embed description\n"
                  "`color:` - Embed color (hex or name)\n"
                  "`thumbnail:` - Thumbnail image URL\n"
                  "`image:` - Main image URL\n"
                  "`url:` - Embed URL\n"
                  "`timestamp` - Add current timestamp",
            inline=False
        )

        embed.add_field(
            name="Author & Footer",
            value="`author: name && icon_url && url`\n"
                  "`footer: text && icon_url`",
            inline=False
        )

        embed.add_field(
            name="Fields",
            value="`field: name && value && inline`\n"
                  "Or: `field: name: Field Name && value: Field Value && inline`",
            inline=False
        )

        embed.add_field(
            name="Placeholders",
            value="`{user.name}` - User display name\n"
                  "`{user.mention}` - User mention\n"
                  "`{guild.name}` - Server name\n"
                  "`{channel.name}` - Channel name",
            inline=False
        )

        embed.add_field(
            name="Example",
            value="```{title: Welcome!} $v {description: Hello {user.mention}} $v {color: blue} $v {field: Server && {guild.name} && true}```",
            inline=False
        )

        embed.set_footer(text="Use $v to separate different parts of the embed")
        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(HelpCommands(bot))
