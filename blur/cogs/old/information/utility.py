from random import choice
from discord import Embed, Member, User, Spotify, Role, TextChannel, Color
from discord.ext.commands import (
    Context,
    command,
    Cog,
    Author,
    is_owner,
    group,
    AutoShardedBot as Bot,
)
import discord, uwuipy, datetime, io, arrow, aiogtts, os, asyncio
from tools.checks import Perms
from typing import Union
from discord.ui import View, Button
import requests
from deep_translator import GoogleTranslator
from discord.ext import tasks
from io import BytesIO
import time
# from ttapi import Tik<PERSON>okA<PERSON>
from aiogtts import aiogTTS

import urllib.request
from bs4 import BeautifulSoup
from PIL import Image
import numpy as np
import json
import humanize
import instaloader
from utils.parse import EmbedBuilder
from datetime import datetime
# import fortnite_api


# api = fortnite_api.FortniteAPI(api_key="633b1210-acc9-4a0e-8569-b3bd7bcbb036")
# tiktok = TikTokApi(debug=False)
DISCORD_API_LINK = "https://discord.com/api/invite/"


def human_format(number):
    if number > 999:
        return humanize.naturalsize(number, False, True)
    return number


@tasks.loop(seconds=10)
async def bday_task(bot: Bot):
    async with bot.db.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT * FROM birthday")
            results = await cursor.fetchall()
    for result in results:
        if (
            arrow.get(result[1]).day == arrow.utcnow().day  # bday is 2nd column
            and arrow.get(result[1]).month == arrow.utcnow().month
        ):
            if result[2] == "false":  # state is 3rd column
                member = await bot.fetch_user(result[0])  # user_id is 1st column
                if member:
                    try:
                        await member.send(f"🎂 Happy birthday gang <@{member.id}>!!")
                        async with bot.db.acquire() as conn:
                            async with conn.cursor() as cursor:
                                await cursor.execute(
                                    "UPDATE birthday SET state = %s WHERE user_id = %s",
                                    ("true", result[0])
                                )
                    except:
                        continue
        else:
            if result[2] == "true":  # state is 3rd column
                async with bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "UPDATE birthday SET state = %s WHERE user_id = %s",
                            ("false", result[0])  # user_id is 1st column
                        )


class Utility(Cog):
    __is_hidden_event__ = True

    def __init__(self, bot: Bot):
        self.bot = bot

        self.cake = "🎂"

    async def make_request(self, url: str, action: str = "get", params: dict = None):
        r = await self.bot.session.get(url, params=params)
        if action == "get":
            return await r.json()
        if action == "read":
            return await r.read()

    async def bday_send(self, ctx: Context, message: str) -> discord.Message:
        return await ctx.reply(
            embed=discord.Embed(
                color=self.bot.color,
                description=f"{self.cake} {ctx.author.mention}: {message}",
            )
        )

    @Cog.listener()
    async def on_ready(self):
        await self.bot.wait_until_ready()
        bday_task.start(self.bot)

    @command(description="get user's avatar", help="utility", usage="<user>")
    async def avatar(self, ctx: Context, *, user: User = Author):
        embed = Embed(
            color=self.bot.color,
            title=f"{user.name}'s avatar",
            url=user.display_avatar.url,
        )
        embed.set_image(url=user.display_avatar.url)
        await ctx.reply(embed=embed)

    @command(description="get user's banner", help="utility", usage="<user>")
    async def banner(self, ctx: Context, *, user: User = Author):
        user = await self.bot.fetch_user(user.id)
        if not user.banner:
            return await ctx.send_warning(f"**{user}** doesn't have a banner")
        embed = Embed(
            color=self.bot.color,
            title=f"{user.name}'s banner",
            url=user.banner.url,
        )
        embed.set_image(url=user.banner.url)
        await ctx.reply(embed=embed)

    @command(description="get information about a user", help="utility", usage="<user>")
    async def userinfo(self, ctx: Context, *, user: Union[Member, User] = Author):
        if isinstance(user, Member):
            embed = Embed(color=self.bot.color, timestamp=datetime.now())
            embed.set_author(name=f"{user}", icon_url=user.display_avatar.url)
            embed.set_thumbnail(url=user.display_avatar.url)
            embed.add_field(name="Name", value=user.mention, inline=True)
            embed.add_field(name="ID", value=user.id, inline=True)
            embed.add_field(
                name="Joined",
                value=f"<t:{int(user.joined_at.timestamp())}:R>",
                inline=True,
            )
            embed.add_field(
                name="Created",
                value=f"<t:{int(user.created_at.timestamp())}:R>",
                inline=True,
            )
            if len(user.roles) > 1:
                embed.add_field(
                    name=f"Roles ({len(user.roles)-1})",
                    value=" ".join([r.mention for r in user.roles[::-1][:-1]]),
                    inline=False,
                )
            embed.set_footer(text=f"Requested by {ctx.author}")
        else:
            embed = Embed(color=self.bot.color, timestamp=datetime.now())
            embed.set_author(name=f"{user}", icon_url=user.display_avatar.url)
            embed.set_thumbnail(url=user.display_avatar.url)
            embed.add_field(name="Name", value=user.mention, inline=True)
            embed.add_field(name="ID", value=user.id, inline=True)
            embed.add_field(
                name="Created",
                value=f"<t:{int(user.created_at.timestamp())}:R>",
                inline=True,
            )
            embed.set_footer(text=f"Requested by {ctx.author}")
        await ctx.reply(embed=embed)

    @command(description="get information about the server", help="utility")
    async def serverinfo(self, ctx: Context):
        guild = ctx.guild
        embed = Embed(color=self.bot.color, timestamp=datetime.now())
        embed.set_author(name=guild.name, icon_url=guild.icon)
        embed.set_thumbnail(url=guild.icon)

        # Basic info
        embed.add_field(name="Owner", value=guild.owner.mention, inline=True)
        embed.add_field(name="ID", value=guild.id, inline=True)
        embed.add_field(
            name="Created",
            value=f"<t:{int(guild.created_at.timestamp())}:R>",
            inline=True,
        )

        # Member statistics
        total_members = guild.member_count
        humans = len([m for m in guild.members if not m.bot])
        bots = len([m for m in guild.members if m.bot])

        # Member count field with detailed breakdown
        member_info = f"👥 {total_members:,} members\n"
        member_info += f"👤 {humans:,} humans\n"
        member_info += f"🤖 {bots:,} bots"
        embed.add_field(name="Members", value=member_info, inline=True)

        # Channel info
        text_channels = len(guild.text_channels)
        voice_channels = len(guild.voice_channels)
        categories = len(guild.categories)
        channel_info = f"💬 {text_channels} text\n🔊 {voice_channels} voice\n📁 {categories} categories"
        embed.add_field(name="Channels", value=channel_info, inline=True)

        # Other info
        embed.add_field(name="Roles", value=len(guild.roles), inline=True)
        embed.add_field(name="Boosts", value=guild.premium_subscription_count, inline=True)
        embed.add_field(name="Boost Level", value=guild.premium_tier, inline=True)

        # Additional stats in footer
        embed.set_footer(text=f"Requested by {ctx.author}")
        await ctx.reply(embed=embed)

    @command(description="translate text or reply to a message", help="utility", usage="[language] [text] or reply to message", aliases=["tr"])
    async def translate(self, ctx: Context, target_lang: str = "en", *, text: str = None):
        """Translate text or reply to a message to translate it"""
        try:
            # Check if replying to a message
            if ctx.message.reference and ctx.message.reference.message_id:
                try:
                    replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
                    text_to_translate = replied_message.content

                    # Handle special cases for replies
                    if target_lang == "-" or (target_lang == "en" and text == "-"):
                        target_lang = "en"  # Default to English
                    elif text == "-":
                        # If text is "-", use the target_lang as specified
                        pass
                    elif text is not None:
                        # If there's additional text, use it instead
                        text_to_translate = text

                except discord.NotFound:
                    return await ctx.send_error("Could not find the message you replied to!")
                except discord.Forbidden:
                    return await ctx.send_error("I don't have permission to read that message!")
            else:
                # Not replying to a message, use provided text
                if text is None:
                    return await ctx.send_error("Please provide text to translate or reply to a message!")
                text_to_translate = text

            if not text_to_translate or text_to_translate.strip() == "":
                return await ctx.send_error("No text found to translate!")

            # Validate target language
            if target_lang == "-":
                target_lang = "en"

            # Translate the text
            translator = GoogleTranslator(source='auto', target=target_lang)
            translated = translator.translate(text_to_translate)

            if not translated:
                return await ctx.send_error("Translation failed! Please try again.")

            # Detect source language
            detected_lang = translator.detect(text_to_translate)

            embed = Embed(
                title="🌐 Translation",
                color=self.bot.color
            )

            # Original text (truncate if too long)
            original_text = text_to_translate[:1000] + "..." if len(text_to_translate) > 1000 else text_to_translate
            embed.add_field(
                name=f"Original ({detected_lang.upper() if detected_lang else 'Unknown'})",
                value=f"```{original_text}```",
                inline=False
            )

            # Translated text (truncate if too long)
            translated_text = translated[:1000] + "..." if len(translated) > 1000 else translated
            embed.add_field(
                name=f"Translated ({target_lang.upper()})",
                value=f"```{translated_text}```",
                inline=False
            )

            embed.set_footer(text=f"Requested by {ctx.author}")
            await ctx.reply(embed=embed)

        except Exception as e:
            await ctx.send_error(f"Translation error: {str(e)}")

    @command(description="check bot's latency", help="utility")
    async def ping(self, ctx: Context):
        embed = Embed(
            color=self.bot.color,
            description=f"You have a ping of around **{round(self.bot.latency * 1000)}ms**",
        )
        await ctx.reply(embed=embed)

    @command(description="check bot's uptime", help="utility")
    async def uptime(self, ctx: Context):
        uptime = int(time.time() - self.bot.uptime)
        embed = Embed(
            color=self.bot.color,
            description=f"⏰ **{humanize.naturaldelta(uptime)}**",
        )
        await ctx.reply(embed=embed)

    @command(description="get member count", help="utility")
    async def membercount(self, ctx: Context):
        embed = Embed(
            color=self.bot.color,
            description=f"👥 **{ctx.guild.member_count:,}** members",
        )
        await ctx.reply(embed=embed)

    @command(description="get bot information", help="utility")
    async def botinfo(self, ctx: Context):
        embed = Embed(color=self.bot.color, timestamp=datetime.now())
        embed.set_author(name=self.bot.user.name, icon_url=self.bot.user.display_avatar.url)
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        embed.add_field(name="Servers", value=len(self.bot.guilds), inline=True)
        embed.add_field(name="Users", value=len(self.bot.users), inline=True)
        embed.add_field(name="Commands", value=len(self.bot.commands), inline=True)
        embed.add_field(name="Latency", value=f"{round(self.bot.latency * 1000)}ms", inline=True)
        uptime = int(time.time() - self.bot.uptime)
        embed.add_field(name="Uptime", value=humanize.naturaldelta(uptime), inline=True)
        embed.set_footer(text=f"Requested by {ctx.author}")
        await ctx.reply(embed=embed)

    @command(description="get information about a website", help="utility", usage="[url]")
    async def website(self, ctx: Context, url: str):
        """Get information about a website"""
        # Add protocol if not present
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Validate URL format
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        if not url_pattern.match(url):
            return await ctx.send_warning("Invalid URL format! Please provide a valid website URL.")

        try:
            # Create loading message
            loading_embed = Embed(
                color=self.bot.color,
                description="🔍 Fetching website information..."
            )
            message = await ctx.reply(embed=loading_embed)

            # Make request with timeout
            async with self.bot.session.get(url, timeout=10) as response:
                status_code = response.status
                content_type = response.headers.get('content-type', 'Unknown')
                server = response.headers.get('server', 'Unknown')

                # Only parse HTML content
                if 'text/html' in content_type.lower():
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')

                    # Extract title
                    title_tag = soup.find('title')
                    title = title_tag.get_text().strip() if title_tag else "No title found"

                    # Extract description
                    description = "No description found"
                    meta_desc = soup.find('meta', attrs={'name': 'description'})
                    if meta_desc and meta_desc.get('content'):
                        description = meta_desc.get('content').strip()
                    else:
                        # Try og:description
                        og_desc = soup.find('meta', attrs={'property': 'og:description'})
                        if og_desc and og_desc.get('content'):
                            description = og_desc.get('content').strip()

                    # Extract favicon
                    favicon_url = None
                    favicon = soup.find('link', attrs={'rel': 'icon'}) or soup.find('link', attrs={'rel': 'shortcut icon'})
                    if favicon and favicon.get('href'):
                        favicon_href = favicon.get('href')
                        if favicon_href.startswith('//'):
                            favicon_url = 'https:' + favicon_href
                        elif favicon_href.startswith('/'):
                            from urllib.parse import urljoin
                            favicon_url = urljoin(url, favicon_href)
                        elif favicon_href.startswith('http'):
                            favicon_url = favicon_href
                else:
                    title = "Non-HTML Content"
                    description = f"Content type: {content_type}"
                    favicon_url = None

                # Create result embed
                embed = Embed(
                    title="🌐 Website Information",
                    color=0x00ff00 if status_code == 200 else 0xffaa00 if status_code < 400 else 0xff0000
                )

                # Truncate title and description if too long
                if len(title) > 256:
                    title = title[:253] + "..."
                if len(description) > 1024:
                    description = description[:1021] + "..."

                embed.add_field(name="URL", value=f"[{url}]({url})", inline=False)
                embed.add_field(name="Title", value=title, inline=False)
                embed.add_field(name="Description", value=description, inline=False)
                embed.add_field(name="Status Code", value=f"{status_code} {'✅' if status_code == 200 else '⚠️' if status_code < 400 else '❌'}", inline=True)
                embed.add_field(name="Content Type", value=content_type, inline=True)
                embed.add_field(name="Server", value=server, inline=True)

                if favicon_url:
                    embed.set_thumbnail(url=favicon_url)

                embed.set_footer(text=f"Requested by {ctx.author}")

                await message.edit(embed=embed)

        except asyncio.TimeoutError:
            error_embed = Embed(
                color=0xff0000,
                description="❌ Request timed out! The website took too long to respond."
            )
            await message.edit(embed=error_embed)
        except Exception as e:
            error_embed = Embed(
                color=0xff0000,
                description=f"❌ Failed to fetch website information: {str(e)}"
            )
            await message.edit(embed=error_embed)


async def setup(bot):
    await bot.add_cog(Utility(bot))
