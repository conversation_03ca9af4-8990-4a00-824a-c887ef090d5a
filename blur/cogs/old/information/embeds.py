import discord
from discord import Embed
from discord.ext.commands import Context, command, Cog
from tools.checks import Perms
from utils.parse import EmbedBuilder

    
# Advanced Embed System Classes
class EmbedCodeView(discord.ui.View):
    def __init__(self, embed_code: str, author_id: int, timeout: float = 60.0):
        super().__init__(timeout=timeout)
        self.embed_code = embed_code
        self.author_id = author_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("You're not the **author** of this embed!", ephemeral=True)
            return False
        return True

    @discord.ui.button(label='Link', style=discord.ButtonStyle.secondary)
    async def send_link(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message(
            f"```{self.embed_code}```",
            ephemeral=True
        )


class EmbedBuilderView(discord.ui.View):
    def __init__(self, author_id: int, ctx=None, timeout: float = 300.0):
        super().__init__(timeout=timeout)
        self.author_id = author_id
        self.ctx = ctx
        self.embed_data = {
            'title': None,
            'description': None,
            'color': None,
            'content': None,
            'author_name': None,
            'author_url': None,
            'author_icon': None,
            'footer_text': None,
            'footer_icon': None,
            'timestamp': False,
            'image': None,
            'thumbnail': None
        }

    def _process_placeholders(self, text: str) -> str:
        """Process placeholders in text"""
        if not text or not self.ctx:
            return text

        # User placeholders
        text = text.replace('{user.name}', self.ctx.author.display_name)
        text = text.replace('{user.username}', self.ctx.author.name)
        text = text.replace('{user.id}', str(self.ctx.author.id))
        text = text.replace('{user.mention}', self.ctx.author.mention)
        text = text.replace('{user.avatar}', str(self.ctx.author.display_avatar.url))
        text = text.replace('{user}', self.ctx.author.mention)

        # Guild placeholders
        if hasattr(self.ctx, 'guild') and self.ctx.guild:
            text = text.replace('{guild.name}', self.ctx.guild.name)
            text = text.replace('{guild.id}', str(self.ctx.guild.id))
            text = text.replace('{guild.member_count}', str(self.ctx.guild.member_count))
            text = text.replace('{server}', self.ctx.guild.name)
            text = text.replace('{server.id}', str(self.ctx.guild.id))
            text = text.replace('{count}', str(self.ctx.guild.member_count))
            if self.ctx.guild.icon:
                text = text.replace('{guild.icon}', str(self.ctx.guild.icon.url))

        # Channel placeholders
        if hasattr(self.ctx, 'channel') and self.ctx.channel:
            text = text.replace('{channel.name}', self.ctx.channel.name)
            text = text.replace('{channel.id}', str(self.ctx.channel.id))
            text = text.replace('{channel.mention}', self.ctx.channel.mention)

        return text
    
    def create_embed(self):
        try:
            embed = discord.Embed()
            embed.set_author(name="Embed Creation")
            embed.description = "Use the buttons below to customize this embed. You can click the Code button to copy this embed or use embed preview test to show this embed."

            if self.embed_data['title']:
                embed.title = self._process_placeholders(self.embed_data['title'])
            if self.embed_data['description']:
                embed.description = self._process_placeholders(self.embed_data['description'])
            if self.embed_data['color']:
                embed.color = self.embed_data['color']
            if self.embed_data['author_name']:
                embed.set_author(
                    name=self._process_placeholders(self.embed_data['author_name']),
                    url=self._process_placeholders(self.embed_data['author_url']) if self.embed_data['author_url'] else None,
                    icon_url=self._process_placeholders(self.embed_data['author_icon']) if self.embed_data['author_icon'] else None
                )
            if self.embed_data['footer_text']:
                embed.set_footer(
                    text=self._process_placeholders(self.embed_data['footer_text']),
                    icon_url=self._process_placeholders(self.embed_data['footer_icon']) if self.embed_data['footer_icon'] else None
                )
            if self.embed_data['timestamp']:
                embed.timestamp = discord.utils.utcnow()
            if self.embed_data['image']:
                embed.set_image(url=self._process_placeholders(self.embed_data['image']))
            if self.embed_data['thumbnail']:
                embed.set_thumbnail(url=self._process_placeholders(self.embed_data['thumbnail']))

            return embed
        except Exception as e:
            print(f"Error creating embed in EmbedBuilderView: {e}")
            # Return a basic embed on error
            return discord.Embed(
                title="Embed Builder Error",
                description="An error occurred while creating the embed.",
                color=0xff0000
            )
    
    def generate_code(self):
        code_parts = []
        
        if self.embed_data['title']:
            code_parts.append(f"{{title: {self.embed_data['title']}}}")
        if self.embed_data['description']:
            code_parts.append(f"{{description: {self.embed_data['description']}}}")
        if self.embed_data['color']:
            code_parts.append(f"{{color: #{self.embed_data['color']:06x}}}")
        if self.embed_data['author_name']:
            author_parts = [f"name: {self.embed_data['author_name']}"]
            if self.embed_data['author_url']:
                author_parts.append(f"url: {self.embed_data['author_url']}")
            if self.embed_data['author_icon']:
                author_parts.append(f"icon: {self.embed_data['author_icon']}")
            code_parts.append(f"{{author: {' && '.join(author_parts)}}}")
        if self.embed_data['footer_text']:
            footer_parts = [f"text: {self.embed_data['footer_text']}"]
            if self.embed_data['footer_icon']:
                footer_parts.append(f"icon: {self.embed_data['footer_icon']}")
            code_parts.append(f"{{footer: {' && '.join(footer_parts)}}}")
        if self.embed_data['timestamp']:
            code_parts.append("{timestamp}")
        if self.embed_data['image']:
            code_parts.append(f"{{image: {self.embed_data['image']}}}")
        if self.embed_data['thumbnail']:
            code_parts.append(f"{{thumbnail: {self.embed_data['thumbnail']}}}")
        
        return "$v".join(code_parts) if code_parts else "{description: Use the buttons below to customize this embed.}"

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            # Send error message directly to interaction
            error_embed = discord.Embed(
                color=0xfc6464,  # Error color
                description=f"<@{interaction.user.id}>: You're not the **author** of this embed!"
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
            return False
        return True

    @discord.ui.button(label='Edit Basic Information', style=discord.ButtonStyle.secondary)
    async def edit_basic(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = BasicInfoModal(self)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Edit Author', style=discord.ButtonStyle.secondary)
    async def edit_author(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = AuthorModal(self)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Edit Footer', style=discord.ButtonStyle.secondary)
    async def edit_footer(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = FooterModal(self)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Edit Image', style=discord.ButtonStyle.secondary)
    async def edit_image(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = ImageModal(self)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Code', style=discord.ButtonStyle.secondary)
    async def get_code(self, interaction: discord.Interaction, button: discord.ui.Button):
        code = self.generate_code()
        await interaction.response.send_message(f"```{code}```", ephemeral=True)


class BasicInfoModal(discord.ui.Modal):
    def __init__(self, builder_view):
        super().__init__(title="Edit Basic Information")
        self.builder_view = builder_view
        
        self.title_input = discord.ui.TextInput(
            label="Title",
            placeholder="Enter embed title",
            default=builder_view.embed_data['title'] or "",
            required=False,
            max_length=256
        )
        self.description_input = discord.ui.TextInput(
            label="Description",
            placeholder="Enter embed description",
            default=builder_view.embed_data['description'] if builder_view.embed_data['description'] != "Use the buttons below to customize this embed. You can click the Code button to copy this embed or use embed preview test to show this embed." else "",
            required=False,
            style=discord.TextStyle.paragraph,
            max_length=4000
        )
        self.color_input = discord.ui.TextInput(
            label="Hex Color (without #)",
            placeholder="e.g. ff0000 for red",
            default=f"{builder_view.embed_data['color']:06x}" if builder_view.embed_data['color'] else "",
            required=False,
            max_length=6
        )
        self.content_input = discord.ui.TextInput(
            label="Message Content",
            placeholder="Text outside the embed",
            default=builder_view.embed_data['content'] or "",
            required=False,
            max_length=2000
        )
        
        self.add_item(self.title_input)
        self.add_item(self.description_input)
        self.add_item(self.color_input)
        self.add_item(self.content_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            self.builder_view.embed_data['title'] = self.title_input.value if self.title_input.value else None
            self.builder_view.embed_data['description'] = self.description_input.value if self.description_input.value else None
            self.builder_view.embed_data['content'] = self.content_input.value if self.content_input.value else None

            if self.color_input.value:
                try:
                    self.builder_view.embed_data['color'] = int(self.color_input.value, 16)
                except ValueError:
                    pass
            else:
                self.builder_view.embed_data['color'] = None

            embed = self.builder_view.create_embed()
            await interaction.response.edit_message(
                content=self.builder_view.embed_data['content'],
                embed=embed,
                view=self.builder_view
            )
        except Exception as e:
            print(f"Error in BasicInfoModal.on_submit: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        f"❌ Error updating basic info: {str(e)}",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        f"❌ Error updating basic info: {str(e)}",
                        ephemeral=True
                    )
            except Exception as follow_error:
                print(f"Error sending error message: {follow_error}")


class AuthorModal(discord.ui.Modal):
    def __init__(self, builder_view):
        super().__init__(title="Edit Author")
        self.builder_view = builder_view
        
        self.author_name_input = discord.ui.TextInput(
            label="Author Text",
            placeholder="Enter author name",
            default=builder_view.embed_data['author_name'] or "",
            required=False,
            max_length=256
        )
        self.author_url_input = discord.ui.TextInput(
            label="Author URL",
            placeholder="Enter author URL",
            default=builder_view.embed_data['author_url'] or "",
            required=False,
            max_length=2000
        )
        self.author_icon_input = discord.ui.TextInput(
            label="Author Image",
            placeholder="Enter author icon URL",
            default=builder_view.embed_data['author_icon'] or "",
            required=False,
            max_length=2000
        )
        
        self.add_item(self.author_name_input)
        self.add_item(self.author_url_input)
        self.add_item(self.author_icon_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            self.builder_view.embed_data['author_name'] = self.author_name_input.value.strip() if self.author_name_input.value.strip() else None
            self.builder_view.embed_data['author_url'] = self.author_url_input.value.strip() if self.author_url_input.value.strip() else None
            self.builder_view.embed_data['author_icon'] = self.author_icon_input.value.strip() if self.author_icon_input.value.strip() else None

            embed = self.builder_view.create_embed()
            await interaction.response.edit_message(
                content=self.builder_view.embed_data['content'],
                embed=embed,
                view=self.builder_view
            )
        except Exception as e:
            print(f"Error in AuthorModal.on_submit: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        f"❌ Error updating author: {str(e)}",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        f"❌ Error updating author: {str(e)}",
                        ephemeral=True
                    )
            except Exception as follow_error:
                print(f"Error sending error message: {follow_error}")


class FooterModal(discord.ui.Modal):
    def __init__(self, builder_view):
        super().__init__(title="Edit Footer")
        self.builder_view = builder_view
        
        self.footer_text_input = discord.ui.TextInput(
            label="Footer Text",
            placeholder="Enter footer text",
            default=builder_view.embed_data['footer_text'] or "",
            required=False,
            max_length=2048
        )
        self.footer_icon_input = discord.ui.TextInput(
            label="Footer Image",
            placeholder="Enter footer icon URL",
            default=builder_view.embed_data['footer_icon'] or "",
            required=False,
            max_length=2000
        )
        self.timestamp_input = discord.ui.TextInput(
            label="Timestamp (yes/no)",
            placeholder="Enter 'yes' to add timestamp",
            default="",
            required=False,
            max_length=3
        )
        
        self.add_item(self.footer_text_input)
        self.add_item(self.footer_icon_input)
        self.add_item(self.timestamp_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            self.builder_view.embed_data['footer_text'] = self.footer_text_input.value.strip() if self.footer_text_input.value.strip() else None
            self.builder_view.embed_data['footer_icon'] = self.footer_icon_input.value.strip() if self.footer_icon_input.value.strip() else None
            self.builder_view.embed_data['timestamp'] = self.timestamp_input.value.lower().strip() == 'yes'

            embed = self.builder_view.create_embed()
            await interaction.response.edit_message(
                content=self.builder_view.embed_data['content'],
                embed=embed,
                view=self.builder_view
            )
        except Exception as e:
            print(f"Error in FooterModal.on_submit: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        f"❌ Error updating footer: {str(e)}",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        f"❌ Error updating footer: {str(e)}",
                        ephemeral=True
                    )
            except Exception as follow_error:
                print(f"Error sending error message: {follow_error}")


class ImageModal(discord.ui.Modal):
    def __init__(self, builder_view):
        super().__init__(title="Edit Image")
        self.builder_view = builder_view
        
        self.image_input = discord.ui.TextInput(
            label="Image",
            placeholder="Enter image URL",
            default=builder_view.embed_data['image'] or "",
            required=False,
            max_length=2000
        )
        self.thumbnail_input = discord.ui.TextInput(
            label="Thumbnail",
            placeholder="Enter thumbnail URL",
            default=builder_view.embed_data['thumbnail'] or "",
            required=False,
            max_length=2000
        )
        
        self.add_item(self.image_input)
        self.add_item(self.thumbnail_input)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Update image data without validation - allow anything
            image_url = self.image_input.value.strip() if self.image_input.value.strip() else None
            thumbnail_url = self.thumbnail_input.value.strip() if self.thumbnail_input.value.strip() else None

            self.builder_view.embed_data['image'] = image_url
            self.builder_view.embed_data['thumbnail'] = thumbnail_url

            # Create updated embed
            embed = self.builder_view.create_embed()

            # Update the message
            await interaction.response.edit_message(
                content=self.builder_view.embed_data['content'],
                embed=embed,
                view=self.builder_view
            )
        except Exception as e:
            print(f"Error in ImageModal.on_submit: {e}")
            # Try to respond with an error message if we haven't responded yet
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        f"❌ Error updating image: {str(e)}",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        f"❌ Error updating image: {str(e)}",
                        ephemeral=True
                    )
            except Exception as follow_error:
                print(f"Error sending error message: {follow_error}")


class EmbedCommands(Cog):
    def __init__(self, bot):
        self.bot = bot

    @command(description="Interactive embed builder with buttons", help="utility", aliases=["be"])
    @Perms.get_perms("manage_messages")
    async def buildembed(self, ctx: Context):
        """Interactive embed builder"""
        try:
            view = EmbedBuilderView(ctx.author.id, ctx)
            embed = view.create_embed()
            await ctx.send(embed=embed, view=view)
        except Exception as e:
            print(f"Buildembed error: {e}")
            await ctx.send_error(f"Failed to create embed builder: {e}")

    @command(description="Create custom embeds using embed code syntax", help="utility", usage="[embed_code]", aliases=["ce"])
    @Perms.get_perms("manage_messages")
    async def createembed(self, ctx: Context, *, embed_code: str):
        """Create custom embeds using embed code syntax"""
        try:
            builder = EmbedBuilder(ctx)
            result = builder.parse_embed_string(embed_code)

            # Send the result (embed and/or content)
            embed = result.get('embed')
            content = result.get('content')
            view = result.get('view')

            # Make sure we have something to send
            if not embed and not content:
                await ctx.send_error("No content to send!")
                return

            await ctx.send(
                content=content,
                embed=embed,
                view=view
            )

        except Exception as e:
            await ctx.send_error(f"Error creating embed: {str(e)}")

    @command(description="Get the embed code from a message", help="utility", usage="[message_id/link]")
    @Perms.get_perms("manage_messages")
    async def copyembed(self, ctx: Context, message_link_or_id: str = None):
        """Copy embed code from an existing message"""
        if not message_link_or_id:
            return await ctx.send_error("Please provide a message ID or link!")

        try:
            # Parse message link or ID
            if message_link_or_id.startswith('https://discord.com/channels/'):
                # Extract IDs from message link
                parts = message_link_or_id.split('/')
                if len(parts) >= 3:
                    channel_id = int(parts[-2])
                    message_id = int(parts[-1])
                    channel = self.bot.get_channel(channel_id)
                else:
                    return await ctx.send_error("Invalid message link format!")
            else:
                # Assume it's a message ID in current channel
                try:
                    message_id = int(message_link_or_id)
                    channel = ctx.channel
                except ValueError:
                    return await ctx.send_error("Invalid message ID!")

            if not channel:
                return await ctx.send_error("Channel not found or I don't have access!")

            # Fetch the message
            try:
                message = await channel.fetch_message(message_id)
            except discord.NotFound:
                return await ctx.send_error("Message not found!")
            except discord.Forbidden:
                return await ctx.send_error("I don't have permission to read that message!")

            # Check if message has embeds or content
            if not message.embeds and not message.content:
                return await ctx.send_error("That message doesn't have any embeds or content!")

            embed_code_parts = []

            # Add content outside embed if it exists
            if message.content:
                embed_code_parts.append(f"{{content: {message.content}}}")

            # Process embed if it exists
            if message.embeds:
                embed = message.embeds[0]  # Get first embed

                # Title
                if embed.title:
                    embed_code_parts.append(f"{{title: {embed.title}}}")

                # Description
                if embed.description:
                    embed_code_parts.append(f"{{description: {embed.description}}}")

                # Color
                if embed.color:
                    embed_code_parts.append(f"{{color: #{embed.color.value:06x}}}")

                # Author
                if embed.author:
                    author_parts = []
                    if embed.author.name:
                        author_parts.append(f"name: {embed.author.name}")
                    if embed.author.icon_url:
                        author_parts.append(f"icon: {embed.author.icon_url}")
                    if embed.author.url:
                        author_parts.append(f"url: {embed.author.url}")
                    if author_parts:
                        embed_code_parts.append(f"{{author: {' && '.join(author_parts)}}}")

                # Fields
                for field in embed.fields:
                    field_parts = [f"name: {field.name}", f"value: {field.value}"]
                    if field.inline:
                        field_parts.append("inline")
                    embed_code_parts.append(f"{{field: {' && '.join(field_parts)}}}")

                # Footer
                if embed.footer:
                    footer_parts = []
                    if embed.footer.text:
                        footer_parts.append(f"text: {embed.footer.text}")
                    if embed.footer.icon_url:
                        footer_parts.append(f"icon: {embed.footer.icon_url}")
                    if footer_parts:
                        embed_code_parts.append(f"{{footer: {' && '.join(footer_parts)}}}")

                # Thumbnail
                if embed.thumbnail:
                    embed_code_parts.append(f"{{thumbnail: {embed.thumbnail.url}}}")

                # Image
                if embed.image:
                    embed_code_parts.append(f"{{image: {embed.image.url}}}")

                # Timestamp
                if embed.timestamp:
                    embed_code_parts.append("{timestamp}")

                # URL
                if embed.url:
                    embed_code_parts.append(f"{{url: {embed.url}}}")

            # Join with $v
            embed_code = "$v".join(embed_code_parts)

            if not embed_code:
                return await ctx.send_error("Could not extract embed code!")

            # Create embed to display the code
            code_embed = Embed(
                title="Embed Code Extracted",
                description=f"```{embed_code[:1900] if len(embed_code) > 1900 else embed_code}```",
                color=0xa4ec7c
            )

            if len(embed_code) > 1900:
                code_embed.set_footer(text="Code truncated - use Link button for full code")

            # Create view with link button
            view = EmbedCodeView(embed_code, ctx.author.id)
            await ctx.send(embed=code_embed, view=view)

        except Exception as e:
            await ctx.send_error(f"Error copying embed: {e}")


async def setup(bot):
    await bot.add_cog(EmbedCommands(bot))
