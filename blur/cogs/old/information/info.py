import discord
from discord.ext import commands
from typing import Optional


class Info(commands.Cog):
    def __init__(self, bot):
        self.bot = bot



    @commands.command(
        name="botinvite",
        description="Get bot invite link"
    )
    async def invite(self, ctx):
        """Get bot invite link"""
        permissions = discord.Permissions(
            administrator=True  # You can customize permissions here
        )
        
        invite_url = discord.utils.oauth_url(
            self.bot.user.id,
            permissions=permissions,
            scopes=("bot", "applications.commands")
        )
        
        embed = discord.Embed(
            title="Invite Me!",
            description=f"Click [here]({invite_url}) to invite me to your server!",
            color=self.bot.color
        )
        
        embed.add_field(
            name="Permissions",
            value="Administrator (recommended for full functionality)",
            inline=False
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        
        view = discord.ui.View()
        view.add_item(
            discord.ui.Button(
                label="Invite Bot",
                url=invite_url,
                style=discord.ButtonStyle.link
            )
        )
        
        await ctx.reply(embed=embed, view=view)

    @commands.command(
        name="support",
        description="Get support server link"
    )
    async def support(self, ctx):
        """Get support server link"""
        embed = discord.Embed(
            title="Need Help?",
            description="Join our support server for help and updates!",
            color=self.bot.color
        )
        
        # You can add your support server invite here
        support_url = "https://discord.gg/your-support-server"
        
        embed.add_field(
            name="Support Server",
            value=f"[Click here to join]({support_url})",
            inline=False
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="stats",
        description="Get bot statistics",
        aliases=["statistics"]
    )
    async def stats(self, ctx):
        """Get bot statistics"""
        embed = discord.Embed(
            title=f"{self.bot.user.name} Statistics",
            color=self.bot.color
        )
        
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        
        # Basic stats
        embed.add_field(name="Servers", value=f"{len(self.bot.guilds):,}", inline=True)
        embed.add_field(name="Users", value=f"{len(self.bot.users):,}", inline=True)
        embed.add_field(name="Commands", value=f"{len(self.bot.commands):,}", inline=True)
        
        # Channel stats
        text_channels = sum(len(guild.text_channels) for guild in self.bot.guilds)
        voice_channels = sum(len(guild.voice_channels) for guild in self.bot.guilds)
        
        embed.add_field(name="Text Channels", value=f"{text_channels:,}", inline=True)
        embed.add_field(name="Voice Channels", value=f"{voice_channels:,}", inline=True)
        embed.add_field(name="Latency", value=f"{self.bot.ping}ms", inline=True)
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="version",
        description="Get bot version info",
        aliases=["v"]
    )
    async def version(self, ctx):
        """Get bot version info"""
        embed = discord.Embed(
            title="Version Information",
            color=self.bot.color
        )
        
        embed.add_field(name="Bot Version", value="1.0.0", inline=True)
        embed.add_field(name="Discord.py Version", value=discord.__version__, inline=True)
        embed.add_field(name="Python Version", value="3.11+", inline=True)
        
        embed.set_footer(text="Combined Bot - The Ultimate Multi-Purpose Bot")
        
        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(Info(bot))
