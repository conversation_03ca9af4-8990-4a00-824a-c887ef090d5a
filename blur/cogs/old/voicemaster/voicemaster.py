import discord
from discord.ext import commands
from tools.checks import Perms
from config.constants import Emojis


class VoiceMasterView(discord.ui.View):
    """Voice Master control panel with 6 buttons"""

    def __init__(self, bot):
        super().__init__(timeout=None)  # Persistent view
        self.bot = bot

    async def check_voice_ownership(self, interaction: discord.Interaction):
        """Check if user owns the voice channel they're in"""
        if not interaction.user.voice:
            await interaction.response.send_message("❌ You're not in a voice channel!", ephemeral=True)
            return None

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM temp_voice WHERE guild_id = %s AND channel_id = %s AND owner_id = %s",
                        (interaction.guild.id, interaction.user.voice.channel.id, interaction.user.id)
                    )
                    temp_data = await cursor.fetchone()

            if not temp_data:
                await interaction.response.send_message("❌ You don't own this voice channel!", ephemeral=True)
                return None

            return interaction.user.voice.channel
        except Exception as e:
            await interaction.response.send_message(f"❌ Database error: {e}", ephemeral=True)
            return None

    @discord.ui.button(emoji=Emojis.lock, style=discord.ButtonStyle.grey, row=0)
    async def lock_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Lock voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        await channel.set_permissions(interaction.guild.default_role, connect=False)
        await interaction.response.send_message(f"{Emojis.lock} Voice channel locked!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.unlock, style=discord.ButtonStyle.grey, row=0)
    async def unlock_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Unlock voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        await channel.set_permissions(interaction.guild.default_role, connect=True)
        await interaction.response.send_message(f"{Emojis.unlock} Voice channel unlocked!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.ghost, style=discord.ButtonStyle.grey, row=0)
    async def ghost_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Ghost voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        await channel.set_permissions(interaction.guild.default_role, view_channel=False)
        await interaction.response.send_message(f"{Emojis.ghost} Voice channel ghosted!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.unghost, style=discord.ButtonStyle.grey, row=0)
    async def reveal_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Reveal voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        await channel.set_permissions(interaction.guild.default_role, view_channel=True)
        await interaction.response.send_message(f"{Emojis.unghost} Voice channel revealed!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.claim, style=discord.ButtonStyle.grey, row=0)
    async def claim_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Claim voice channel button"""
        if not interaction.user.voice:
            await interaction.response.send_message("❌ You're not in a voice channel!", ephemeral=True)
            return

        channel = interaction.user.voice.channel

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT owner_id FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                        (interaction.guild.id, channel.id)
                    )
                    temp_data = await cursor.fetchone()

            if not temp_data:
                await interaction.response.send_message("❌ This is not a temporary voice channel!", ephemeral=True)
                return

            # Check if current owner is still in the channel
            current_owner = interaction.guild.get_member(temp_data[0])
            if current_owner and current_owner.voice and current_owner.voice.channel == channel:
                await interaction.response.send_message("❌ The current owner is still in the channel!", ephemeral=True)
                return

            # Transfer ownership
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET owner_id = %s WHERE guild_id = %s AND channel_id = %s",
                        (interaction.user.id, interaction.guild.id, channel.id)
                    )

            await interaction.response.send_message(f"👑 You claimed ownership of {channel.mention}!", ephemeral=True)

        except Exception as e:
            await interaction.response.send_message(f"❌ Error claiming channel: {e}", ephemeral=True)

    @discord.ui.button(emoji=Emojis.disconnect, style=discord.ButtonStyle.grey, row=1)
    async def disconnect_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Disconnect member from voice channel button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        # Get all members in the channel except the owner
        members_in_channel = [member for member in channel.members if member.id != interaction.user.id]

        if not members_in_channel:
            await interaction.response.send_message("❌ No other members in your voice channel to disconnect!", ephemeral=True)
            return

        # Create member selection dropdown
        member_options = []
        for member in members_in_channel[:25]:  # Discord limit of 25 options
            member_options.append(discord.SelectOption(
                label=member.display_name,
                value=str(member.id),
                description=f"@{member.name}"
            ))

        class MemberSelect(discord.ui.Select):
            def __init__(self):
                super().__init__(placeholder="Choose a member to disconnect...", options=member_options)

            async def callback(self, select_interaction):
                try:
                    member_id = int(self.values[0])
                    member_to_kick = interaction.guild.get_member(member_id)

                    if not member_to_kick:
                        await select_interaction.response.send_message("❌ Member not found!", ephemeral=True)
                        return

                    if member_to_kick not in channel.members:
                        await select_interaction.response.send_message("❌ That member is no longer in your voice channel!", ephemeral=True)
                        return

                    # Disconnect the selected member
                    await member_to_kick.move_to(None)

                    disconnect_embed = discord.Embed(
                        title="🔌 Member Disconnected",
                        description=f"**{member_to_kick.display_name}** has been disconnected from {channel.mention}",
                        color=0xff0000
                    )

                    await select_interaction.response.send_message(embed=disconnect_embed, ephemeral=True)

                except Exception as e:
                    await select_interaction.response.send_message(f"❌ Failed to disconnect member: {e}", ephemeral=True)

        # Add "Disconnect All" option if there are multiple members
        if len(members_in_channel) > 1:
            member_options.append(discord.SelectOption(
                label="🔌 Disconnect All Members",
                value="disconnect_all",
                description="Disconnect all members from the channel",
                emoji="🔌"
            ))

        class MemberSelectWithAll(discord.ui.Select):
            def __init__(self):
                super().__init__(placeholder="Choose a member to disconnect...", options=member_options)

            async def callback(self, select_interaction):
                try:
                    if self.values[0] == "disconnect_all":
                        # Disconnect all members except owner
                        disconnected_count = 0
                        for member in channel.members:
                            if member.id != interaction.user.id:
                                try:
                                    await member.move_to(None)
                                    disconnected_count += 1
                                except:
                                    pass

                        disconnect_embed = discord.Embed(
                            title="🔌 All Members Disconnected",
                            description=f"Disconnected **{disconnected_count}** members from {channel.mention}",
                            color=0xff0000
                        )

                        await select_interaction.response.send_message(embed=disconnect_embed, ephemeral=True)
                    else:
                        member_id = int(self.values[0])
                        member_to_kick = interaction.guild.get_member(member_id)

                        if not member_to_kick:
                            await select_interaction.response.send_message("❌ Member not found!", ephemeral=True)
                            return

                        if member_to_kick not in channel.members:
                            await select_interaction.response.send_message("❌ That member is no longer in your voice channel!", ephemeral=True)
                            return

                        # Disconnect the selected member
                        await member_to_kick.move_to(None)

                        disconnect_embed = discord.Embed(
                            title="🔌 Member Disconnected",
                            description=f"**{member_to_kick.display_name}** has been disconnected from {channel.mention}",
                            color=0xff0000
                        )

                        await select_interaction.response.send_message(embed=disconnect_embed, ephemeral=True)

                except Exception as e:
                    await select_interaction.response.send_message(f"❌ Failed to disconnect member: {e}", ephemeral=True)

        view = discord.ui.View()
        if len(members_in_channel) > 1:
            view.add_item(MemberSelectWithAll())
        else:
            view.add_item(MemberSelect())

        await interaction.response.send_message("🔌 Select a member to disconnect:", view=view, ephemeral=True)

    @discord.ui.button(emoji=Emojis.activity, style=discord.ButtonStyle.grey, row=1)
    async def activity_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Start activity button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        # Create activity selection dropdown
        activities = [
            ("YouTube Together", "880218394199220334"),
            ("Poker Night", "755827207812677713"),
            ("Betrayal.io", "773336526917861400"),
            ("Fishington.io", "814288819477020702"),
            ("Chess In The Park", "832012774040141894"),
            ("Checkers In The Park", "832013003968348200"),
            ("Blazing 8s", "832025144389533716"),
            ("Land-io", "903769130790969345"),
            ("Putt Party", "945737671223947305"),
            ("Bobble League", "947957217959759964"),
            ("Ask Away", "976052223358406656"),
            ("Word Snacks", "879863976006127627")
        ]

        activity_options = []
        for name, app_id in activities[:10]:  # Discord limit of 25 options, we'll use 10
            activity_options.append(discord.SelectOption(label=name, value=app_id))

        class ActivitySelect(discord.ui.Select):
            def __init__(self):
                super().__init__(placeholder="Choose an activity...", options=activity_options)

            async def callback(self, select_interaction):
                try:
                    invite = await channel.create_invite(
                        target_type=discord.InviteTarget.embedded_application,
                        target_application_id=int(self.values[0]),
                        reason="Voice Master activity"
                    )

                    activity_embed = discord.Embed(
                        title="🎮 Activity Started!",
                        description=f"[Click here to join the activity!]({invite.url})",
                        color=0x00ff00
                    )

                    await select_interaction.response.send_message(embed=activity_embed, ephemeral=True)
                except Exception as e:
                    await select_interaction.response.send_message(f"❌ Failed to start activity: {e}", ephemeral=True)

        view = discord.ui.View()
        view.add_item(ActivitySelect())

        await interaction.response.send_message("🎮 Select an activity to start:", view=view, ephemeral=True)

    @discord.ui.button(emoji=Emojis.info, style=discord.ButtonStyle.grey, row=1)
    async def info_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Channel info button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        # Get channel info
        members_list = [member.display_name for member in channel.members]

        embed = discord.Embed(
            title=f"ℹ️ {channel.name}",
            color=self.bot.color
        )
        embed.add_field(name="Owner", value=interaction.user.mention, inline=True)
        embed.add_field(name="Members", value=f"{len(channel.members)}/{channel.user_limit or '∞'}", inline=True)
        embed.add_field(name="Created", value=f"<t:{int(channel.created_at.timestamp())}:R>", inline=True)

        if members_list:
            embed.add_field(
                name=f"Members in Channel ({len(members_list)})",
                value="\n".join(members_list) if len(members_list) <= 10 else "\n".join(members_list[:10]) + f"\n... and {len(members_list) - 10} more",
                inline=False
            )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(emoji=Emojis.increase, style=discord.ButtonStyle.grey, row=1)
    async def increase_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Increase user limit button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        current_limit = channel.user_limit or 0
        if current_limit >= 99:
            await interaction.response.send_message("❌ User limit is already at maximum (99)!", ephemeral=True)
            return

        new_limit = current_limit + 1
        await channel.edit(user_limit=new_limit)

        await interaction.response.send_message(f"➕ User limit increased to {new_limit}!", ephemeral=True)

    @discord.ui.button(emoji=Emojis.decrease, style=discord.ButtonStyle.grey, row=1)
    async def decrease_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Decrease user limit button"""
        channel = await self.check_voice_ownership(interaction)
        if not channel:
            return

        current_limit = channel.user_limit or 0
        if current_limit <= 1:
            await interaction.response.send_message("❌ User limit cannot be decreased below 1!", ephemeral=True)
            return

        new_limit = current_limit - 1 if current_limit > 0 else 0
        await channel.edit(user_limit=new_limit)

        await interaction.response.send_message(f"➖ User limit decreased to {new_limit if new_limit > 0 else 'No limit'}!", ephemeral=True)


class VoiceMaster(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice channel events"""
        # Check if user joined a voice master channel (the "create vc" channel)
        if after.channel:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT * FROM voicemaster WHERE guild_id = %s AND channel_id = %s",
                            (member.guild.id, after.channel.id)
                        )
                        vm_data = await cursor.fetchone()

                if vm_data:
                    # Create temporary voice channel
                    category = after.channel.category
                    temp_channel = await member.guild.create_voice_channel(
                        name=f"{member.display_name}'s Channel",
                        category=category,
                        user_limit=0  # No limit by default
                    )

                    # Move user to temp channel
                    await member.move_to(temp_channel)

                    # Store temp channel info
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "INSERT INTO temp_voice (guild_id, channel_id, owner_id) VALUES (%s, %s, %s)",
                                (member.guild.id, temp_channel.id, member.id)
                            )

                    # Give VM role if configured
                    try:
                        async with self.bot.db.acquire() as conn:
                            async with conn.cursor() as cursor:
                                await cursor.execute(
                                    "SELECT vm_role_id FROM voicemaster_config WHERE guild_id = %s",
                                    (member.guild.id,)
                                )
                                role_data = await cursor.fetchone()

                        if role_data and role_data[0]:
                            role = member.guild.get_role(role_data[0])
                            if role and role not in member.roles:
                                await member.add_roles(role, reason="Voice Master user")
                    except:
                        pass

            except Exception as e:
                print(f"Voice Master creation error: {e}")

        # Check if user left a temp voice channel
        if before.channel:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT * FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                            (member.guild.id, before.channel.id)
                        )
                        temp_data = await cursor.fetchone()

                if temp_data and len(before.channel.members) == 0:
                    # Delete empty temp channel
                    await before.channel.delete(reason="Empty voice channel")
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "DELETE FROM temp_voice WHERE channel_id = %s",
                                (before.channel.id,)
                            )

                # Remove VM role when leaving any voice channel
                if temp_data:
                    try:
                        async with self.bot.db.acquire() as conn:
                            async with conn.cursor() as cursor:
                                await cursor.execute(
                                    "SELECT vm_role_id FROM voicemaster_config WHERE guild_id = %s",
                                    (member.guild.id,)
                                )
                                role_data = await cursor.fetchone()

                        if role_data and role_data[0]:
                            role = member.guild.get_role(role_data[0])
                            if role and role in member.roles:
                                await member.remove_roles(role, reason="Left Voice Master channel")
                    except:
                        pass

            except Exception as e:
                print(f"Voice Master cleanup error: {e}")

    @commands.group(
        name="voicemaster",
        description="Voice Master system",
        invoke_without_command=True,
        aliases=["vm"]
    )
    async def voicemaster(self, ctx):
        """Voice Master system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🎤 Voice Master System",
                description="Create and manage temporary voice channels",
                color=self.bot.color
            )

            embed.add_field(
                name="🔧 Setup Commands",
                value="`vm setup [channel]` - Set voice master channel\n"
                      "`vm reset` - Reset voice master system\n"
                      "`vm role @role` - Set role for VM users",
                inline=False
            )

            embed.add_field(
                name="🎮 Control Commands",
                value="`vm lock/unlock` - Lock/unlock your channel\n"
                      "`vm hide/unhide` - Hide/unhide your channel\n"
                      "`vm claim` - Claim ownership of channel\n"
                      "`vm disconnect` - Disconnect from channel",
                inline=False
            )

            embed.add_field(
                name="⚙️ Management Commands",
                value="`vm limit [number]` - Set user limit\n"
                      "`vm rename [name]` - Rename your channel\n"
                      "`vm status [text]` - Set channel status\n"
                      "`vm transfer @user` - Transfer ownership",
                inline=False
            )

            embed.add_field(
                name="👥 User Commands",
                value="`vm permit @user` - Allow user to join\n"
                      "`vm reject @user` - Deny user access",
                inline=False
            )

            embed.add_field(
                name="🎛️ Control Panel",
                value="When you create a voice channel, you'll receive a control panel with 6 buttons for quick access!",
                inline=False
            )

            await ctx.reply(embed=embed)

    @voicemaster.command(
        name="setup",
        description="Setup voice master system"
    )
    @Perms.get_perms("manage_channels")
    async def vm_setup(self, ctx):
        """Setup voice master system"""
        try:
            # Send loading message
            loading_embed = discord.Embed(
                color=self.bot.color,
                description=f"{Emojis.loading} @{ctx.author.display_name}: Please wait while we setup VoiceMaster channels for you"
            )
            message = await ctx.reply(embed=loading_embed)

            # Create Voice Master category
            category = await ctx.guild.create_category(
                name="Voice Master",
                reason="Voice Master setup"
            )

            # Create panel text channel
            panel_channel = await ctx.guild.create_text_channel(
                name="panel",
                category=category,
                reason="Voice Master panel channel"
            )

            # Create "create vc" voice channel
            create_vc = await ctx.guild.create_voice_channel(
                name="create vc",
                category=category,
                reason="Voice Master create channel"
            )

            # Store in database
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO voicemaster (guild_id, channel_id) VALUES (%s, %s) ON DUPLICATE KEY UPDATE channel_id = %s",
                        (ctx.guild.id, create_vc.id, create_vc.id)
                    )

            # Create the control panel embed (simple, no text descriptions)
            embed = discord.Embed(
                description="Use the buttons below to control your voice channel.",
                color=self.bot.color
            )
            embed.set_author(name="VoiceMaster Panel")
            embed.set_thumbnail(url=self.bot.user.display_avatar.url)

            # Create the view with buttons
            view = VoiceMasterView(self.bot)

            # Send the panel to the panel channel
            await panel_channel.send(embed=embed, view=view)

            # Update with success message
            success_embed = discord.Embed(
                color=0x00ff00,
                description=f"{Emojis.success} @{ctx.author.display_name}: Finished setting up the VoiceMaster channels. A category and two channels have been created, you can move the channels or rename them if you want."
            )
            await message.edit(embed=success_embed)

        except Exception as e:
            await ctx.send_error(f"Failed to setup Voice Master: {e}")

    @voicemaster.command(
        name="reset",
        description="Reset voice master system"
    )
    @Perms.get_perms("manage_channels")
    async def vm_reset(self, ctx):
        """Reset voice master system"""
        try:
            # Send loading message
            loading_embed = discord.Embed(
                color=self.bot.color,
                description=f"{Emojis.loading} @{ctx.author.display_name}: Please wait while we reset VoiceMaster system for you"
            )
            message = await ctx.reply(embed=loading_embed)

            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Get the voice master channel to find the category
                    await cursor.execute(
                        "SELECT channel_id FROM voicemaster WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    vm_data = await cursor.fetchone()

                    # Find and delete the Voice Master category and its channels
                    if vm_data:
                        vm_channel = self.bot.get_channel(vm_data[0])
                        if vm_channel and vm_channel.category:
                            category = vm_channel.category
                            # Delete all channels in the Voice Master category
                            for channel in category.channels:
                                try:
                                    await channel.delete(reason="Voice Master system reset")
                                except:
                                    pass
                            # Delete the category itself
                            try:
                                await category.delete(reason="Voice Master system reset")
                            except:
                                pass

                    # Get all temp channels for this guild
                    await cursor.execute(
                        "SELECT channel_id FROM temp_voice WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    temp_channels = await cursor.fetchall()

                    # Delete temp channels
                    for temp_channel_data in temp_channels:
                        channel = self.bot.get_channel(temp_channel_data[0])
                        if channel:
                            try:
                                await channel.delete(reason="Voice Master system reset")
                            except:
                                pass

                    # Clear database entries
                    await cursor.execute(
                        "DELETE FROM voicemaster WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    await cursor.execute(
                        "DELETE FROM temp_voice WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    await cursor.execute(
                        "DELETE FROM voicemaster_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )

            # Update with success message
            success_embed = discord.Embed(
                color=0x00ff00,
                description=f"{Emojis.success} @{ctx.author.display_name}: VoiceMaster system has been completely reset and all channels deleted"
            )
            await message.edit(embed=success_embed)

        except Exception as e:
            await ctx.send_error(f"Failed to reset Voice Master: {e}")

    @voicemaster.command(
        name="role",
        description="Set role for Voice Master users",
        usage="[role]"
    )
    @Perms.get_perms("manage_roles")
    async def vm_role(self, ctx, role: discord.Role):
        """Set role for Voice Master users"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO voicemaster_config (guild_id, vm_role_id) VALUES (%s, %s) ON DUPLICATE KEY UPDATE vm_role_id = %s",
                        (ctx.guild.id, role.id, role.id)
                    )

            embed = discord.Embed(
                title="👥 Voice Master Role Set",
                description=f"Users in Voice Master channels will now receive {role.mention}",
                color=0x00ff00
            )
            embed.add_field(
                name="Role Information",
                value=f"**Name:** {role.name}\n**Color:** {role.color}\n**Members:** {len(role.members)}",
                inline=False
            )

            await ctx.reply(embed=embed)

        except Exception as e:
            await ctx.send_error(f"Failed to set Voice Master role: {e}")

    async def check_vc_owner(self, ctx):
        """Check if user owns a temp voice channel"""
        if not ctx.author.voice:
            await ctx.send_warning("You're not in a voice channel!")
            return None

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM temp_voice WHERE guild_id = %s AND channel_id = %s AND owner_id = %s",
                        (ctx.guild.id, ctx.author.voice.channel.id, ctx.author.id)
                    )
                    temp_data = await cursor.fetchone()

            if not temp_data:
                await ctx.send_warning("You don't own this voice channel!")
                return None

            return ctx.author.voice.channel
        except Exception as e:
            await ctx.send_error(f"Database error: {e}")
            return None

    # 6 Button Commands
    @voicemaster.command(
        name="lock",
        description="Lock your voice channel"
    )
    async def vm_lock(self, ctx):
        """Lock voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(ctx.guild.default_role, connect=False)

        embed = discord.Embed(
            title="🔒 Voice Channel Locked",
            description=f"{channel.mention} has been locked",
            color=0xff0000
        )
        embed.add_field(name="Action", value="Channel locked for @everyone", inline=True)
        embed.add_field(name="Owner", value=ctx.author.mention, inline=True)

        await ctx.reply(embed=embed)

    @voicemaster.command(
        name="unlock",
        description="Unlock your voice channel"
    )
    async def vm_unlock(self, ctx):
        """Unlock voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(ctx.guild.default_role, connect=True)

        embed = discord.Embed(
            title="🔓 Voice Channel Unlocked",
            description=f"{channel.mention} has been unlocked",
            color=0x00ff00
        )
        embed.add_field(name="Action", value="Channel unlocked for @everyone", inline=True)
        embed.add_field(name="Owner", value=ctx.author.mention, inline=True)

        await ctx.reply(embed=embed)

    @voicemaster.command(
        name="hide",
        description="Hide your voice channel"
    )
    async def vm_hide(self, ctx):
        """Hide voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(ctx.guild.default_role, view_channel=False)

        embed = discord.Embed(
            title="👁️ Voice Channel Hidden",
            description=f"{channel.mention} has been hidden",
            color=0x808080
        )
        embed.add_field(name="Action", value="Channel hidden from @everyone", inline=True)
        embed.add_field(name="Owner", value=ctx.author.mention, inline=True)

        await ctx.reply(embed=embed)

    @voicemaster.command(
        name="unhide",
        description="Unhide your voice channel"
    )
    async def vm_unhide(self, ctx):
        """Unhide voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(ctx.guild.default_role, view_channel=True)

        embed = discord.Embed(
            title="👁️ Voice Channel Unhidden",
            description=f"{channel.mention} is now visible",
            color=0x00ff00
        )
        embed.add_field(name="Action", value="Channel visible to @everyone", inline=True)
        embed.add_field(name="Owner", value=ctx.author.mention, inline=True)

        await ctx.reply(embed=embed)

    @voicemaster.command(
        name="claim",
        description="Claim ownership of voice channel"
    )
    async def vm_claim(self, ctx):
        """Claim voice channel ownership"""
        if not ctx.author.voice:
            return await ctx.send_warning("You're not in a voice channel!")

        channel = ctx.author.voice.channel

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT owner_id FROM temp_voice WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    temp_data = await cursor.fetchone()

            if not temp_data:
                return await ctx.send_warning("This is not a temporary voice channel!")

            # Check if current owner is still in the channel
            current_owner = ctx.guild.get_member(temp_data[0])
            if current_owner and current_owner.voice and current_owner.voice.channel == channel:
                return await ctx.send_warning("The current owner is still in the channel!")

            # Transfer ownership
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET owner_id = %s WHERE guild_id = %s AND channel_id = %s",
                        (ctx.author.id, ctx.guild.id, channel.id)
                    )

            embed = discord.Embed(
                title="👑 Channel Ownership Claimed",
                description=f"You now own {channel.mention}",
                color=0xffd700
            )
            embed.add_field(name="New Owner", value=ctx.author.mention, inline=True)
            embed.add_field(name="Channel", value=channel.mention, inline=True)

            await ctx.reply(embed=embed)

        except Exception as e:
            await ctx.send_error(f"Failed to claim channel: {e}")

    @voicemaster.command(
        name="disconnect",
        description="Disconnect from voice channel"
    )
    async def vm_disconnect(self, ctx):
        """Disconnect from voice channel"""
        if not ctx.author.voice:
            return await ctx.send_warning("You're not in a voice channel!")

        channel_name = ctx.author.voice.channel.name
        await ctx.author.move_to(None)

        embed = discord.Embed(
            title="🔌 Disconnected",
            description=f"You have been disconnected from **{channel_name}**",
            color=0xff0000
        )

        await ctx.reply(embed=embed)

    # Keep only essential commands - buttons handle most functionality
    @voicemaster.command(
        name="limit",
        description="Set user limit for your voice channel",
        usage="[number]"
    )
    async def vm_limit(self, ctx, limit: int):
        """Set voice channel limit"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if limit < 0 or limit > 99:
            return await ctx.send_warning("Limit must be between 0-99!")

        await channel.edit(user_limit=limit)
        await ctx.send_success(f"User limit set to {limit if limit > 0 else 'No limit'}")

    @voicemaster.command(
        name="rename",
        description="Rename your voice channel",
        usage="[name]",
        aliases=["name"]
    )
    async def vm_rename(self, ctx, *, name: str):
        """Rename voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if len(name) > 100:
            return await ctx.send_warning("Name too long! Maximum 100 characters.")

        await channel.edit(name=name)
        await ctx.send_success(f"Channel renamed to **{name}**")

    @voicemaster.command(
        name="permit",
        description="Allow a user to join your voice channel",
        usage="[user]"
    )
    async def vm_permit(self, ctx, member: discord.Member):
        """Permit user to join voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(member, connect=True)
        await ctx.send_success(f"{member.mention} can now join your voice channel")

    @voicemaster.command(
        name="reject",
        description="Deny a user access to your voice channel",
        usage="[user]"
    )
    async def vm_reject(self, ctx, member: discord.Member):
        """Reject user from voice channel"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        await channel.set_permissions(member, connect=False)
        if member in channel.members:
            await member.move_to(None)

        await ctx.send_success(f"{member.mention} has been denied access to your voice channel")

    @voicemaster.command(
        name="status",
        description="Set your voice channel status",
        usage="[status]"
    )
    async def vm_status(self, ctx, *, status: str):
        """Set voice channel status"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if len(status) > 500:
            return await ctx.send_warning("Status too long! Maximum 500 characters.")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET status = %s WHERE guild_id = %s AND channel_id = %s",
                        (status, ctx.guild.id, channel.id)
                    )

            await ctx.send_success(f"Channel status updated: **{status}**")

        except Exception as e:
            await ctx.send_error(f"Failed to update status: {e}")

    @voicemaster.command(
        name="transfer",
        description="Transfer ownership of your voice channel",
        usage="[user]"
    )
    async def vm_transfer(self, ctx, member: discord.Member):
        """Transfer voice channel ownership"""
        channel = await self.check_vc_owner(ctx)
        if not channel:
            return

        if member not in channel.members:
            return await ctx.send_warning("That user is not in your voice channel!")

        if member.bot:
            return await ctx.send_warning("You cannot transfer ownership to a bot!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE temp_voice SET owner_id = %s WHERE guild_id = %s AND channel_id = %s",
                        (member.id, ctx.guild.id, channel.id)
                    )

            await ctx.send_success(f"Ownership transferred to {member.mention}")

        except Exception as e:
            await ctx.send_error(f"Failed to transfer ownership: {e}")


async def setup(bot):
    await bot.add_cog(VoiceMaster(bot))
