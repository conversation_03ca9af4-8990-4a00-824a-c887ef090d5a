import discord
from discord.ext import commands
from tools.checks import Perms
import math
import random
from collections import defaultdict
import time


class Leveling(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.cooldowns = defaultdict(float)
        self.voice_cooldowns = defaultdict(float)

    def get_ratelimit(self, message):
        """Check if user is on cooldown"""
        user_id = message.author.id
        current_time = time.time()
        
        if current_time - self.cooldowns[user_id] < 60:  # 1 minute cooldown
            return True
        
        self.cooldowns[user_id] = current_time
        return False

    def calculate_level(self, xp):
        """Calculate level from XP"""
        return int(math.sqrt(xp / 100))

    def calculate_xp_for_level(self, level):
        """Calculate XP needed for a level"""
        return (level ** 2) * 100

    @commands.Cog.listener()
    async def on_message(self, message):
        """Give XP for messages"""
        if not message.guild:
            return
        
        if message.author.bot:
            return
        
        # Check if leveling is enabled
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM leveling_config WHERE guild_id = %s",
                    (message.guild.id,)
                )
                config = await cursor.fetchone()
        
        if not config or not config[1]:  # config[1] is enabled
            return
        
        # Check cooldown
        if self.get_ratelimit(message):
            return
        
        # Get user's current data
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM levels WHERE guild_id = %s AND user_id = %s",
                    (message.guild.id, message.author.id)
                )
                user_data = await cursor.fetchone()
        
        # XP to give (random between 15-25)
        xp_gain = random.randint(15, 25)
        
        if not user_data:
            # Create new user
            new_xp = xp_gain
            new_level = self.calculate_level(new_xp)
            
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO levels (guild_id, user_id, xp, level) VALUES (%s, %s, %s, %s)",
                        (message.guild.id, message.author.id, new_xp, new_level)
                    )
        else:
            # Update existing user - user_data is tuple: (guild_id, user_id, xp, level)
            old_level = user_data[3]  # level
            new_xp = user_data[2] + xp_gain  # xp
            new_level = self.calculate_level(new_xp)
            
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE levels SET xp = %s, level = %s WHERE guild_id = %s AND user_id = %s",
                        (new_xp, new_level, message.guild.id, message.author.id)
                    )
            
            # Check if user leveled up
            if new_level > old_level:
                await self.handle_levelup(message, new_level, config)

    async def handle_levelup(self, message, new_level, config):
        """Handle level up event"""
        # Send level up message
        if config and len(config) > 2 and config[2]:  # config[2] is channel_id
            channel = self.bot.get_channel(config[2])
        else:
            channel = message.channel
        
        if channel:
            embed = discord.Embed(
                title="🎉 Level Up!",
                description=f"{message.author.mention} reached level **{new_level}**!",
                color=self.bot.color
            )
            embed.set_thumbnail(url=message.author.display_avatar.url)
            
            await channel.send(embed=embed)
        
        # Check for role rewards
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM level_roles WHERE guild_id = %s AND level = %s",
                    (message.guild.id, new_level)
                )
                role_reward = await cursor.fetchone()
        
        if role_reward:
            role = message.guild.get_role(role_reward[2])  # role_reward[2] is role_id
            if role and role not in message.author.roles:
                try:
                    await message.author.add_roles(role, reason=f"Level {new_level} reward")
                    
                    embed = discord.Embed(
                        title="🎁 Role Reward!",
                        description=f"{message.author.mention} received {role.mention} for reaching level **{new_level}**!",
                        color=self.bot.color
                    )
                    
                    if config and len(config) > 2 and config[2]:  # config[2] is channel_id
                        channel = self.bot.get_channel(config[2])
                        if channel:
                            await channel.send(embed=embed)
                    
                except discord.Forbidden:
                    pass

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Give XP for being in voice channels"""
        if member.bot:
            return

        # Check if user joined a voice channel or is already in one
        if after.channel and not after.self_deaf and not after.self_mute:
            # Check if leveling is enabled
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT enabled FROM leveling_config WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    config = await cursor.fetchone()

            if not config or not config[0]:  # config[0] is enabled
                return

            # Check voice cooldown (5 minutes for voice XP)
            user_id = member.id
            current_time = time.time()

            if current_time - self.voice_cooldowns[user_id] < 300:  # 5 minute cooldown
                return

            self.voice_cooldowns[user_id] = current_time

            # Get user's current data
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM levels WHERE guild_id = %s AND user_id = %s",
                        (member.guild.id, member.id)
                    )
                    user_data = await cursor.fetchone()

            # XP to give for voice (10-20 XP, less than messages)
            xp_gain = random.randint(10, 20)

            if not user_data:
                # Create new user
                new_xp = xp_gain
                new_level = self.calculate_level(new_xp)

                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "INSERT INTO levels (guild_id, user_id, xp, level) VALUES (%s, %s, %s, %s)",
                            (member.guild.id, member.id, new_xp, new_level)
                        )
            else:
                # Update existing user
                old_level = user_data[3]  # level
                new_xp = user_data[2] + xp_gain  # xp
                new_level = self.calculate_level(new_xp)

                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "UPDATE levels SET xp = %s, level = %s WHERE guild_id = %s AND user_id = %s",
                            (new_xp, new_level, member.guild.id, member.id)
                        )

                # Check for level up
                if new_level > old_level:
                    await self.handle_level_up(member, new_level)

    async def handle_level_up(self, member, new_level):
        """Handle level up notifications and role rewards"""
        # Get leveling config
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM leveling_config WHERE guild_id = %s",
                    (member.guild.id,)
                )
                config = await cursor.fetchone()

        # Send level up message if channel is configured
        if config and len(config) > 2 and config[2]:  # config[2] is channel_id
            channel = self.bot.get_channel(config[2])
            if channel:
                embed = discord.Embed(
                    title="🎉 Level Up!",
                    description=f"{member.mention} reached level **{new_level}**!",
                    color=self.bot.color
                )
                embed.set_thumbnail(url=member.display_avatar.url)

                try:
                    await channel.send(embed=embed)
                except discord.Forbidden:
                    pass

        # Check for role rewards
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM level_roles WHERE guild_id = %s AND level = %s",
                    (member.guild.id, new_level)
                )
                role_reward = await cursor.fetchone()

        if role_reward:
            role = member.guild.get_role(role_reward[2])  # role_reward[2] is role_id
            if role and role not in member.roles:
                try:
                    await member.add_roles(role, reason=f"Level {new_level} reward")

                    embed = discord.Embed(
                        title="🎁 Role Reward!",
                        description=f"{member.mention} received {role.mention} for reaching level **{new_level}**!",
                        color=self.bot.color
                    )

                    if config and len(config) > 2 and config[2]:  # config[2] is channel_id
                        channel = self.bot.get_channel(config[2])
                        if channel:
                            await channel.send(embed=embed)

                except discord.Forbidden:
                    pass

    @commands.command(
        name="rank",
        description="Check your or someone's rank",
        usage="<member>",
        aliases=["level", "lvl"]
    )
    async def rank(self, ctx, member: discord.Member = None):
        """Check rank/level"""
        member = member or ctx.author
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM levels WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )
                user_data = await cursor.fetchone()
        
        if not user_data:
            if member == ctx.author:
                # Create new user starting at level 1 with 100 XP
                initial_xp = 100  # Level 1 requires 100 XP
                initial_level = 1

                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "INSERT INTO levels (guild_id, user_id, xp, level) VALUES (%s, %s, %s, %s)",
                            (ctx.guild.id, member.id, initial_xp, initial_level)
                        )

                # Set user_data for the rest of the function
                user_data = (ctx.guild.id, member.id, initial_xp, initial_level)
            else:
                return await ctx.send_warning(f"**{member}** doesn't have any XP yet!")
        
        # Get user's rank
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """SELECT COUNT(*) + 1 as rank FROM levels
                       WHERE guild_id = %s AND (xp > %s OR (xp = %s AND user_id < %s))""",
                    (ctx.guild.id, user_data[2], user_data[2], member.id)  # user_data[2] is xp
                )
                rank_data = await cursor.fetchone()
        
        rank = rank_data[0]  # rank_data is a tuple
        current_level = user_data[3]  # user_data[3] is level
        current_xp = user_data[2]  # user_data[2] is xp
        
        # Calculate XP for next level
        next_level_xp = self.calculate_xp_for_level(current_level + 1)
        current_level_xp = self.calculate_xp_for_level(current_level)
        xp_needed = next_level_xp - current_xp
        xp_progress = current_xp - current_level_xp
        xp_for_level = next_level_xp - current_level_xp
        
        embed = discord.Embed(
            title=f"{member.display_name}'s Rank",
            color=self.bot.color
        )
        
        embed.add_field(name="📊 Rank", value=f"#{rank}", inline=True)
        embed.add_field(name="📈 Level", value=f"{current_level}", inline=True)
        embed.add_field(name="✨ Total XP", value=f"{current_xp:,}", inline=True)
        
        embed.add_field(
            name="📋 Progress",
            value=f"{xp_progress}/{xp_for_level} XP\n({xp_needed:,} XP needed for level {current_level + 1})",
            inline=False
        )
        
        embed.set_thumbnail(url=member.display_avatar.url)
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="leaderboard",
        description="View server leaderboard",
        aliases=["lb", "top"]
    )
    async def leaderboard(self, ctx):
        """View server leaderboard"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """SELECT user_id, xp, level FROM levels
                       WHERE guild_id = %s
                       ORDER BY xp DESC
                       LIMIT 10""",
                    (ctx.guild.id,)
                )
                top_users = await cursor.fetchall()
        
        if not top_users:
            return await ctx.send_warning("No one has any XP yet!")
        
        embed = discord.Embed(
            title=f"🏆 {ctx.guild.name} Leaderboard",
            color=self.bot.color
        )
        
        description = ""
        for i, user_data in enumerate(top_users, 1):
            # user_data is a tuple: (user_id, xp, level)
            user = self.bot.get_user(user_data[0])
            if user:
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                description += f"{medal} **{user.display_name}** - Level {user_data[2]} ({user_data[1]:,} XP)\n"
        
        embed.description = description
        embed.set_thumbnail(url=ctx.guild.icon)
        
        await ctx.reply(embed=embed)

    @commands.group(
        name="leveling",
        description="Leveling system configuration",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def leveling(self, ctx):
        """Leveling system configuration"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="📈 Leveling System",
                description="Configure the leveling system for your server\n\n**XP Sources:**\n• Messages: 15-25 XP (1 min cooldown)\n• Voice Chat: 10-20 XP (5 min cooldown)",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`leveling enable` - Enable leveling\n"
                      "`leveling disable` - Disable leveling\n"
                      "`leveling channel` - Set level up channel\n"
                      "`levels` - Advanced level management",
                inline=False
            )
            
            await ctx.reply(embed=embed)

    @leveling.command(
        name="enable",
        description="Enable leveling system"
    )
    @Perms.get_perms("manage_guild")
    async def leveling_enable(self, ctx):
        """Enable leveling system"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """INSERT INTO leveling_config (guild_id, enabled)
                       VALUES (%s, TRUE)
                       ON CONFLICT (guild_id) DO UPDATE SET enabled = TRUE""",
                    (ctx.guild.id,)
                )
        
        await ctx.send_success("Leveling system **enabled**!")

    @leveling.command(
        name="disable",
        description="Disable leveling system"
    )
    @Perms.get_perms("manage_guild")
    async def leveling_disable(self, ctx):
        """Disable leveling system"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE leveling_config SET enabled = FALSE WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
        
        await ctx.send_success("Leveling system **disabled**!")

    @leveling.command(
        name="channel",
        description="Set level up announcement channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def leveling_channel(self, ctx, channel: discord.TextChannel = None):
        """Set level up channel"""
        if channel is None:
            # Remove channel (use current channel)
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO leveling_config (guild_id, channel_id)
                           VALUES (%s, NULL)
                           ON CONFLICT (guild_id) DO UPDATE SET channel_id = NULL""",
                        (ctx.guild.id,)
                    )
            
            await ctx.send_success("Level up messages will now be sent in the same channel as the message")
        else:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO leveling_config (guild_id, channel_id)
                           VALUES (%s, %s)
                           ON CONFLICT (guild_id) DO UPDATE SET channel_id = %s""",
                        (ctx.guild.id, channel.id, channel.id)
                    )
            
            await ctx.send_success(f"Level up messages will now be sent in {channel.mention}")



    @commands.group(
        name="levels",
        description="Leveling system management",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def levels_main(self, ctx):
        """Main levels command"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="📈 Levels Management",
                description="Manage the leveling system for your server\n\n**XP Sources:**\n• Messages: 15-25 XP (1 min cooldown)\n• Voice Chat: 10-20 XP (5 min cooldown)",
                color=self.bot.color
            )

            embed.add_field(
                name="System Commands",
                value="`levels unlock` - Enable leveling system\n"
                      "`levels lock` - Disable leveling system\n"
                      "`levels reset` - Reset all members' levels\n"
                      "`levels cleanup` - Reset levels for absent members",
                inline=False
            )

            embed.add_field(
                name="Role Commands",
                value="`levels roles` - View all XP roles\n"
                      "`levels add [level] [role]` - Create level role\n"
                      "`levels remove [level]` - Remove level role",
                inline=False
            )

            embed.add_field(
                name="User Commands",
                value="`setlevel [user] [level]` - Set a user's level",
                inline=False
            )

            await ctx.reply(embed=embed)

    @levels_main.command(
        name="unlock",
        description="Enable leveling system"
    )
    @Perms.get_perms("manage_guild")
    async def levels_unlock(self, ctx):
        """Enable leveling system"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """INSERT INTO leveling_config (guild_id, enabled)
                       VALUES (%s, TRUE)
                       ON DUPLICATE KEY UPDATE enabled = TRUE""",
                    (ctx.guild.id,)
                )

        await ctx.send_success("Leveling system **unlocked** (enabled)!")

    @levels_main.command(
        name="lock",
        description="Disable leveling system"
    )
    @Perms.get_perms("manage_guild")
    async def levels_lock(self, ctx):
        """Disable leveling system"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE leveling_config SET enabled = FALSE WHERE guild_id = %s",
                    (ctx.guild.id,)
                )

        await ctx.send_success("Leveling system **locked** (disabled)!")

    @levels_main.command(
        name="reset",
        description="Reset all members' levels and XP"
    )
    @Perms.get_perms("manage_guild")
    async def levels_reset(self, ctx):
        """Reset all members' levels and XP"""
        # Get count first
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT COUNT(*) FROM levels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                count = await cursor.fetchone()

        if count[0] == 0:
            return await ctx.send_warning("No members have any XP to reset!")

        # Confirm action
        from config.constants import Emojis, Colors
        embed = discord.Embed(
            title=f"{Emojis.warn} Confirm Reset",
            description=f"This will reset **{count[0]}** members' levels and XP.\n\n**This action cannot be undone!**",
            color=Colors.error
        )

        view = discord.ui.View(timeout=30)

        async def confirm_callback(interaction):
            if interaction.user != ctx.author:
                return await interaction.response.send_message("Only the command author can confirm this.", ephemeral=True)

            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM levels WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )

            await interaction.response.edit_message(
                embed=discord.Embed(
                    title="✅ Reset Complete",
                    description=f"Reset **{count[0]}** members' levels and XP!",
                    color=self.bot.color
                ),
                view=None
            )

        async def cancel_callback(interaction):
            if interaction.user != ctx.author:
                return await interaction.response.send_message("Only the command author can cancel this.", ephemeral=True)

            await interaction.response.edit_message(
                embed=discord.Embed(
                    title="❌ Reset Cancelled",
                    description="No levels were reset.",
                    color=self.bot.color
                ),
                view=None
            )

        confirm_button = discord.ui.Button(label="Confirm Reset", style=discord.ButtonStyle.danger)
        cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary)

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback

        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await ctx.reply(embed=embed, view=view)

    @levels_main.command(
        name="cleanup",
        description="Reset levels for absent members"
    )
    @Perms.get_perms("manage_guild")
    async def levels_cleanup(self, ctx):
        """Reset levels for absent members"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT user_id FROM levels WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                all_users = await cursor.fetchall()

        if not all_users:
            return await ctx.send_warning("No members have any XP to cleanup!")

        # Check which users are no longer in the server
        absent_users = []
        for user_data in all_users:
            user_id = user_data[0]
            member = ctx.guild.get_member(user_id)
            if not member:
                absent_users.append(user_id)

        if not absent_users:
            return await ctx.send_success("No absent members found! All users with XP are still in the server.")

        # Remove absent users
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                for user_id in absent_users:
                    await cursor.execute(
                        "DELETE FROM levels WHERE guild_id = %s AND user_id = %s",
                        (ctx.guild.id, user_id)
                    )

        await ctx.send_success(f"Cleaned up **{len(absent_users)}** absent members' levels and XP!")

    @levels_main.command(
        name="roles",
        description="View all XP roles"
    )
    @Perms.get_perms("manage_guild")
    async def levels_roles(self, ctx):
        """View all XP roles"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT level, role_id FROM level_roles WHERE guild_id = %s ORDER BY level",
                    (ctx.guild.id,)
                )
                rewards = await cursor.fetchall()

        if not rewards:
            return await ctx.send_warning("No XP roles configured!")

        embed = discord.Embed(
            title="🎁 XP Roles",
            color=self.bot.color
        )

        role_list = []
        for level, role_id in rewards:
            role = ctx.guild.get_role(role_id)
            if role:
                role_list.append(f"Level **{level}** → {role.mention}")
            else:
                role_list.append(f"Level **{level}** → <@&{role_id}> (deleted)")

        embed.description = "\n".join(role_list)
        embed.set_footer(text=f"{len(rewards)} role(s)")

        await ctx.reply(embed=embed)

    @levels_main.command(
        name="add",
        description="Create level role",
        usage="[level] [role]"
    )
    @Perms.get_perms("manage_guild")
    async def levels_add(self, ctx, level: int, role: discord.Role):
        """Create level role"""
        if level < 1:
            return await ctx.send_warning("Level must be 1 or higher!")

        if role >= ctx.guild.me.top_role:
            return await ctx.send_warning("That role is higher than my highest role!")

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """INSERT INTO level_roles (guild_id, level, role_id)
                       VALUES (%s, %s, %s)
                       ON DUPLICATE KEY UPDATE role_id = %s""",
                    (ctx.guild.id, level, role.id, role.id)
                )

        await ctx.send_success(f"Added {role.mention} as reward for level **{level}**!")

    @levels_main.command(
        name="remove",
        description="Remove level role",
        usage="[level]"
    )
    @Perms.get_perms("manage_guild")
    async def levels_remove(self, ctx, level: int):
        """Remove level role"""
        if level < 1:
            return await ctx.send_warning("Level must be 1 or higher!")

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM level_roles WHERE guild_id = %s AND level = %s",
                    (ctx.guild.id, level)
                )

                if cursor.rowcount == 0:
                    return await ctx.send_warning(f"No role reward found for level **{level}**!")

        await ctx.send_success(f"Removed role reward for level **{level}**!")

    @commands.command(
        name="setlevel",
        description="Set a user's level",
        usage="[user] [level]"
    )
    @Perms.get_perms("manage_guild")
    async def setlevel(self, ctx, member: discord.Member, level: int):
        """Set a user's level"""
        if level < 0:
            return await ctx.send_warning("Level must be 0 or higher!")

        if level > 1000:
            return await ctx.send_warning("Level cannot be higher than 1000!")

        # Calculate XP for the level
        new_xp = self.calculate_xp_for_level(level)

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                # Check if user exists
                await cursor.execute(
                    "SELECT * FROM levels WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )
                existing = await cursor.fetchone()

                if existing:
                    # Update existing user
                    await cursor.execute(
                        "UPDATE levels SET xp = %s, level = %s WHERE guild_id = %s AND user_id = %s",
                        (new_xp, level, ctx.guild.id, member.id)
                    )
                else:
                    # Create new user
                    await cursor.execute(
                        "INSERT INTO levels (guild_id, user_id, xp, level) VALUES (%s, %s, %s, %s)",
                        (ctx.guild.id, member.id, new_xp, level)
                    )

        await ctx.send_success(f"Set **{member.display_name}**'s level to **{level}** ({new_xp:,} XP)")


async def setup(bot):
    await bot.add_cog(Leveling(bot))
