import discord
from discord.ext import commands
from tools.ext import embed


class TestOld(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(name="testold")
    async def test_old(self, ctx):
        """Test command to verify old folder loading works"""
        await embed.success(ctx, "Old folder commands are working!")


async def setup(bot):
    await bot.add_cog(TestOld(bot))
