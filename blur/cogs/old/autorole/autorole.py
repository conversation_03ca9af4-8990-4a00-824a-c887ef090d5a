import discord
from discord.ext import commands
from tools.checks import Perms


class AutoRole(commands.Cog):
    """Auto role assignment system"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Auto assign role to new members"""
        try:
            # Get autorole from database
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT autorole FROM guild_config WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    result = await cursor.fetchone()
                    autorole_id = result[0] if result else None
            
            if autorole_id:
                role = member.guild.get_role(autorole_id)
                if role and role < member.guild.me.top_role:
                    await member.add_roles(role, reason="Auto role assignment")
        except Exception as e:
            print(f"AutoRole error: {e}")
    
    @commands.group(
        name="autorole",
        description="Auto role system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_roles")
    async def autorole(self, ctx):
        """Auto role system"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🎭 Auto Role System",
                description="Automatically assign roles to new members",
                color=self.bot.color
            )
            
            embed.add_field(
                name="Commands",
                value="`autorole set [role]` - Set auto role\n"
                      "`autorole remove` - Remove auto role\n"
                      "`autorole view` - View current auto role",
                inline=False
            )
            
            await ctx.reply(embed=embed)
    
    @autorole.command(
        name="set",
        description="Set auto role for new members",
        usage="[role]"
    )
    @Perms.get_perms("manage_roles")
    async def autorole_set(self, ctx, role: discord.Role):
        """Set auto role for new members"""
        if role >= ctx.guild.me.top_role:
            return await ctx.send_warning("I cannot assign roles higher than my highest role!")
        
        if role >= ctx.author.top_role and ctx.author != ctx.guild.owner:
            return await ctx.send_warning("You cannot set a role higher than your highest role!")
        
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO guild_config (guild_id, autorole) VALUES (%s, %s) ON DUPLICATE KEY UPDATE autorole = %s",
                    (ctx.guild.id, role.id, role.id)
                )
        
        await ctx.send_success(f"Set auto role to {role.mention}")
    
    @autorole.command(
        name="remove",
        description="Remove auto role"
    )
    @Perms.get_perms("manage_roles")
    async def autorole_remove(self, ctx):
        """Remove auto role"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE guild_config SET autorole = NULL WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
        
        await ctx.send_success("Removed auto role")
    
    @autorole.command(
        name="view",
        description="View current auto role"
    )
    async def autorole_view(self, ctx):
        """View current auto role"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT autorole FROM guild_config WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                result = await cursor.fetchone()
                autorole_id = result[0] if result else None
        
        if not autorole_id:
            return await ctx.send_warning("No auto role set!")
        
        role = ctx.guild.get_role(autorole_id)
        if not role:
            return await ctx.send_warning("Auto role not found! It may have been deleted.")
        
        embed = discord.Embed(
            title="🎭 Auto Role",
            description=f"Current auto role: {role.mention}",
            color=self.bot.color
        )
        
        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(AutoRole(bot))
