import discord, humanfriendly, json, datetime, asyncio
from discord.ext import commands
from discord import Message, Member, User, Embed, PermissionOverwrite
from discord.ext.commands import cooldown, BucketType, CooldownMapping
from tools.checks import Perms, Mod
from tools.utils import <PERSON><PERSON>taff, GoodRole, Invoke
from typing import Union
from discord import Embed
from tools.utils import <PERSON>bedBuilder, GoodRole, NoStaff


class ClearMod(discord.ui.View):
    def __init__(self, ctx: commands.Context):
        super().__init__()
        self.ctx = ctx
        self.status = False

    @discord.ui.button(emoji="<:check:1124998964358426675>")
    async def yes(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.client.ext.send_warning(
                interaction, "You are not the author of this embed"
            )
        async with interaction.client.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (interaction.guild.id,)
                )
                check = await cursor.fetchone()
        channelid = check[1]  # channel_id is 2nd column
        roleid = check[3]     # role_id is 4th column
        logsid = check[2]     # jail_id is 3rd column
        channel = interaction.guild.get_channel(channelid)
        role = interaction.guild.get_role(roleid)
        logs = interaction.guild.get_channel(logsid)
        try:
            await channel.delete()
        except:
            pass
        try:
            await role.delete()
        except:
            pass
        try:
            await logs.delete()
        except:
            pass
        async with interaction.client.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM `mod` WHERE guild_id = %s", (interaction.guild.id,)
                )
        self.status = True
        return await interaction.response.edit_message(
            view=None,
            embed=discord.Embed(
                color=interaction.client.color,
                description=f"{interaction.client.yes} {interaction.user.mention}: Disabled",
            ),
        )

    @discord.ui.button(emoji="<:stop:1124999008142774303>")
    async def no(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.client.ext.send_warning(
                interaction, "You are not the author of this embed"
            )
        await interaction.response.edit_message(
            embed=discord.Embed(
                color=interaction.client.color, description="aborting action.."
            ),
            view=None,
        )
        self.status = True

    async def on_timeout(self) -> None:
        if self.status == False:
            for item in self.children:
                item.disabled = True

            await self.message.edit(view=self)


class ModConfig:
    async def sendlogs(
        bot: commands.AutoShardedBot,
        action: str,
        author: discord.Member,
        victim: Union[discord.Member, discord.User],
        reason: str,
    ):
        async with bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT channel_id FROM `mod` WHERE guild_id = %s", (author.guild.id,)
                )
                check = await cursor.fetchone()

        if check:
            async with bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT count FROM cases WHERE guild_id = %s", (author.guild.id,)
                    )
                    res = await cursor.fetchone()

            case = int(res[0]) + 1 if res else 1
            async with bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "UPDATE cases SET count = %s WHERE guild_id = %s", (case, author.guild.id)
                    )
            embed = discord.Embed(color=bot.color, title=f"case #{case} | {action}")
            embed.add_field(
                name="User:", value=f"```{victim}({victim.id})```", inline=False
            )
            embed.add_field(
                name="Moderator:", value=f"```{author}({author.id})```", inline=False
            )
            embed.add_field(name="Reason:", value=f"```{reason}```", inline=False)
            embed.set_thumbnail(url=victim.avatar)
            try:
                await author.guild.get_channel(int(check["channel_id"])).send(
                    embed=embed
                )
            except:
                pass

    async def send_dm(
        ctx: commands.Context, member: discord.Member, action: str, reason: str
    ):
        view = discord.ui.View()
        view.add_item(
            discord.ui.Button(label=f"sent from {ctx.guild.name}", disabled=True)
        )
        embed = discord.Embed(
            color=ctx.bot.color,
            description=f"You have been **{action}** in {ctx.guild.name}\n{f'reason: {reason}' if reason != 'No reason provided' else ''}",
        )
        try:
            await member.send(embed=embed)
        except:
            pass


class Mod(commands.Cog):
    __is_hidden_event__ = True

    def __init__(self, bot: commands.AutoShardedBot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_guild_channel_create(self, channel):
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (channel.guild.id,)
                )
                check = await cursor.fetchone()
        if check:
            await channel.set_permissions(
                channel.guild.get_role(int(check[3])),  # role_id is 4th column (index 3)
                view_channel=False,
                reason="overwriting permissions for jail role",
            )

    @commands.command(
        description="disable the moderation features in your server", help="moderation"
    )
    @Perms.get_perms("administrator")
    async def unsetme(self, ctx: commands.Context):
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()
        if not check:
            return await ctx.send_warning(
                "Moderation is **not** enabled in this server"
            )
        view = ClearMod(ctx)
        view.message = await ctx.reply(
            view=view,
            embed=discord.Embed(
                color=self.bot.color,
                description=f"{ctx.author.mention} Are you sure you want to **disable** jail?",
            ),
        )

    @commands.command(
        description="enable the moaderation features in your server", help="moderation"
    )
    @Perms.get_perms("administrator")
    async def setme(self, ctx: commands.Context):
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()
        if check:
            return await ctx.send_warning(
                "Moderation is **already** enabled in this server"
            )
        await ctx.typing()
        role = await ctx.guild.create_role(name="blur-jail")
        for channel in ctx.guild.channels:
            await channel.set_permissions(role, view_channel=False)
        overwrite = {
            role: discord.PermissionOverwrite(view_channel=True),
            ctx.guild.default_role: discord.PermissionOverwrite(view_channel=False),
        }
        over = {ctx.guild.default_role: discord.PermissionOverwrite(view_channel=False)}
        category = await ctx.guild.create_category(name="blur mod", overwrites=over)
        text = await ctx.guild.create_text_channel(
            name="mod-logs", overwrites=over, category=category
        )
        jai = await ctx.guild.create_text_channel(
            name="jail", overwrites=overwrite, category=category
        )
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO `mod` (guild_id, channel_id, jail_id, role_id) VALUES (%s, %s, %s, %s)",
                    (ctx.guild.id, text.id, jai.id, role.id)
                )
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO cases (guild_id, count) VALUES (%s, %s)",
                    (ctx.guild.id, 0)
                )
        return await ctx.send_success("Enabled **moderation** for this server")

    @commands.command(
        description="ban a member from your server",
        help="moderation",
        usage="[member] <reason>",
    )
    @Perms.get_perms("ban_members")
    async def ban(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        await ctx.guild.ban(user=member, reason=reason + " | {}".format(ctx.author))
        await ModConfig.send_dm(ctx, member, "banned", reason)
        await ModConfig.sendlogs(
            self.bot, "ban", ctx.author, member, reason + " | " + str(ctx.author)
        )
        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(f"**{member}** was banned - {reason}")

    @commands.command(
        description="kick a member from your server",
        help="moderation",
        usage="[member] <reason>",
    )
    @Perms.get_perms("kick_members")
    async def kick(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        await ctx.guild.kick(user=member, reason=reason + " | {}".format(ctx.author))
        await ModConfig.send_dm(ctx, member, "kicked", reason)
        await ModConfig.sendlogs(
            self.bot, "kick", ctx.author, member, reason + " | " + str(ctx.author)
        )
        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(f"**{member}** was kicked - {reason}")

    @commands.command(
        description="mute members in your server",
        help="moderation",
        brief="moderate members",
        usage="[member] [time] <reason>",
        aliases=["timeout"],
    )
    @Perms.get_perms("moderate_members")
    async def mute(
        self,
        ctx: commands.Context,
        member: NoStaff,
        time: str = "60s",
        *,
        reason="No reason provided",
    ):
        tim = humanfriendly.parse_timespan(time)
        until = discord.utils.utcnow() + datetime.timedelta(seconds=tim)
        await member.timeout(until, reason=reason + " | {}".format(ctx.author))
        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(
                f"**{member}** has been muted for {humanfriendly.format_timespan(tim)} | {reason}"
            )
        await ModConfig.sendlogs(
            self.bot,
            "mute",
            ctx.author,
            member,
            reason + " | " + humanfriendly.format_timespan(tim),
        )
        await ModConfig.send_dm(
            ctx, member, "muted", reason + " | " + humanfriendly.format_timespan(tim)
        )

    @commands.command(
        description="unmute a member in your server",
        help="moderation",
        brief="moderate members",
        usage="[member] <reason>",
        aliases=["untimeout"],
    )
    @Perms.get_perms("moderate_members")
    async def unmute(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        if not member.is_timed_out():
            return await ctx.send_warning(f"**{member}** is not muted")
        await member.edit(
            timed_out_until=None, reason=reason + " | {}".format(ctx.author)
        )
        await ModConfig.sendlogs(self.bot, "unmute", ctx.author, member, reason)
        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(f"unmuted **{member}**")

    @commands.command(
        description="unban an user from your server",
        help="moderation",
        usage="[member]",
    )
    async def unban(
        self,
        ctx: commands.Context,
        member: discord.User,
        *,
        reason: str = "No reason provided",
    ):
        try:
            await ctx.guild.unban(
                user=member, reason=reason + " | {}".format(ctx.author)
            )
            if not await Invoke.invoke_send(ctx, member, reason):
                await ctx.send_success(f"**{member}** has been unbanned")
        except discord.NotFound:
            return await ctx.send_warning(f"**{member}** is not banned")

    @commands.command(
        aliases=["p"],
        description="bulk delete messages",
        help="moderation",
        brief="manage messages",
        usage="[messages]",
    )
    @Perms.get_perms("manage_messages")
    async def purge(
        self, ctx: commands.Context, amount: int, *, member: NoStaff = None
    ):
        if member is None:
            await ctx.channel.purge(
                limit=amount + 1, bulk=True, reason=f"purge invoked by {ctx.author}"
            )
            return
        messages = []
        async for m in ctx.channel.history():
            if m.author.id == member.id:
                messages.append(m)
            if len(messages) == amount:
                break
        messages.append(ctx.message)
        await ctx.channel.delete_messages(messages)
        return

    @commands.command(
        description="lock a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def lock(self, ctx: commands.Context, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.send_messages = False
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite)
        return await ctx.send_success(f"Locked {channel.mention}")

    @commands.command(
        description="unlock a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def unlock(self, ctx: commands.Context, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.send_messages = True
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite)
        return await ctx.send_success(f"Unlocked {channel.mention}")

    @commands.command(
        description="hide a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def hide(self, ctx: commands.Context, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.view_channel = False
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite)
        return await ctx.send_success(f"Hidden {channel.mention}")

    @commands.command(
        description="unhide a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def unhide(self, ctx: commands.Context, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        overwrite.view_channel = True
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrite)
        return await ctx.send_success(f"Unhidden {channel.mention}")

    @commands.command(
        description="jail a member", help="moderation", usage="[member] <reason>"
    )
    @Perms.get_perms("moderate_members")
    @Mod.is_mod_configured()
    async def jail(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Jail a member"""
        # Get jail configuration
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()

        if not check:
            return await ctx.send_warning("Jail system is not configured!")

        jail_role = ctx.guild.get_role(check[3])  # role_id is 4th column
        if not jail_role:
            return await ctx.send_warning("Jail role not found!")

        if jail_role in member.roles:
            return await ctx.send_warning(f"**{member}** is already jailed!")

        # Remove all roles and add jail role
        old_roles = [role for role in member.roles if role != ctx.guild.default_role]
        await member.edit(roles=[jail_role], reason=f"Jailed by {ctx.author}: {reason}")

        # Store old roles for unjail
        role_ids = [str(role.id) for role in old_roles]
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO jailed_users (guild_id, user_id, roles) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE roles = %s",
                    (ctx.guild.id, member.id, ",".join(role_ids), ",".join(role_ids))
                )

        await ModConfig.sendlogs(
            self.bot, "jail", ctx.author, member, reason + " | " + str(ctx.author)
        )
        await ModConfig.send_dm(ctx, member, "jailed", reason)

        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(f"**{member}** was jailed - {reason}")

    @commands.command(
        description="unjail a member", help="moderation", usage="[member] <reason>"
    )
    @Perms.get_perms("moderate_members")
    @Mod.is_mod_configured()
    async def unjail(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Unjail a member"""
        # Get jail configuration
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()

        if not check:
            return await ctx.send_warning("Jail system is not configured!")

        jail_role = ctx.guild.get_role(check[3])  # role_id is 4th column
        if not jail_role:
            return await ctx.send_warning("Jail role not found!")

        if jail_role not in member.roles:
            return await ctx.send_warning(f"**{member}** is not jailed!")

        # Get old roles
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT roles FROM jailed_users WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )
                jailed_data = await cursor.fetchone()

        # Restore old roles
        roles_to_add = [ctx.guild.default_role]
        if jailed_data and jailed_data[0]:
            role_ids = jailed_data[0].split(",")
            for role_id in role_ids:
                if role_id:
                    role = ctx.guild.get_role(int(role_id))
                    if role:
                        roles_to_add.append(role)

        await member.edit(roles=roles_to_add, reason=f"Unjailed by {ctx.author}: {reason}")

        # Remove from jailed users
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM jailed_users WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )

        await ModConfig.sendlogs(
            self.bot, "unjail", ctx.author, member, reason + " | " + str(ctx.author)
        )

        if not await Invoke.invoke_send(ctx, member, reason):
            await ctx.send_success(f"**{member}** was unjailed - {reason}")




async def setup(bot):
    await bot.add_cog(Mod(bot))
