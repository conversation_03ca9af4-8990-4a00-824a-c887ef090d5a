import discord
from discord.ext import commands
from tools.checks import Perms
import typing


class AutoMod(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def get_automod_rule(self, guild, rule_name):
        """Get existing AutoMod rule by name"""
        try:
            rules = await guild.fetch_automod_rules()
            for rule in rules:
                if rule.name == rule_name:
                    return rule
            return None
        except:
            return None

    async def get_filtered_words(self, guild_id):
        """Get filtered words from database"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT word FROM chatfilter WHERE guild_id = %s",
                        (guild_id,)
                    )
                    words = await cursor.fetchall()
                    return [word[0] for word in words] if words else []
        except:
            return []

    async def create_or_update_keyword_rule(self, guild, keywords):
        """Create or update keyword filter rule"""
        try:
            rule = await self.get_automod_rule(guild, "Keyword Filter")

            if not keywords:
                # If no keywords, disable or delete the rule
                if rule:
                    await rule.edit(enabled=False)
                return True

            if rule:
                # Update existing rule
                await rule.edit(
                    trigger=discord.AutoModTrigger(
                        keyword_filter=discord.AutoModKeywordFilter(keywords)
                    ),
                    enabled=True
                )
            else:
                # Create new rule
                await guild.create_automod_rule(
                    name="Keyword Filter",
                    event_type=discord.AutoModEventType.message_send,
                    trigger=discord.AutoModTrigger(
                        keyword_filter=discord.AutoModKeywordFilter(keywords)
                    ),
                    actions=[
                        discord.AutoModAction(
                            type=discord.AutoModActionType.block_message
                        )
                    ],
                    enabled=True
                )
            return True
        except Exception as e:
            print(f"Error creating/updating keyword rule: {e}")
            return False

    async def toggle_invite_rule(self, guild, enabled):
        """Toggle Discord invite blocking rule"""
        try:
            rule = await self.get_automod_rule(guild, "Block Invites")

            if rule:
                await rule.edit(enabled=enabled)
            elif enabled:
                # Create new rule
                await guild.create_automod_rule(
                    name="Block Invites",
                    event_type=discord.AutoModEventType.message_send,
                    trigger=discord.AutoModTrigger(
                        presets=[discord.AutoModPresets.discord_invites]
                    ),
                    actions=[
                        discord.AutoModAction(
                            type=discord.AutoModActionType.block_message
                        )
                    ],
                    enabled=True
                )
            return True
        except Exception as e:
            print(f"Error toggling invite rule: {e}")
            return False

    @commands.group(
        name="automod",
        description="Discord AutoMod configuration",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def automod(self, ctx):
        """Discord AutoMod configuration"""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="🤖 Discord AutoMod System",
                description="Configure Discord's built-in AutoMod features",
                color=self.bot.color
            )

            embed.add_field(
                name="Commands",
                value="`automod invites enable/disable` - Toggle invite blocking\n"
                      "`automod add [word]` - Add filtered word\n"
                      "`automod remove [word]` - Remove filtered word\n"
                      "`automod list` - Show filtered words\n"
                      "`automod bypass @role/#channel` - Toggle exemptions\n"
                      "`automod exemptions` - Show current exemptions",
                inline=False
            )

            await ctx.reply(embed=embed)

    @automod.command(
        name="invites",
        description="Toggle Discord invite blocking",
        usage="[enable/disable]"
    )
    @Perms.get_perms("manage_guild")
    async def automod_invites(self, ctx, action: str):
        """Toggle invite blocking using Discord AutoMod"""
        if action.lower() not in ["enable", "disable"]:
            return await ctx.send_warning("Action must be **enable** or **disable**")

        enabled = action.lower() == "enable"

        success = await self.toggle_invite_rule(ctx.guild, enabled)

        if success:
            status = "enabled" if enabled else "disabled"
            await ctx.send_success(f"Discord invite blocking {status}")
        else:
            await ctx.send_error("Failed to update AutoMod rule. Make sure I have the **Manage Server** permission.")

    @automod.command(
        name="add",
        description="Add word to filter",
        usage="[word]"
    )
    @Perms.get_perms("manage_guild")
    async def automod_add(self, ctx, *, word: str):
        """Add word to keyword filter"""
        if len(word) > 60:
            return await ctx.send_warning("Word must be 60 characters or less")

        # Add to database
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT IGNORE INTO chatfilter (guild_id, word) VALUES (%s, %s)",
                        (ctx.guild.id, word.lower())
                    )
        except:
            return await ctx.send_error("Failed to add word to database")

        # Update AutoMod rule
        keywords = await self.get_filtered_words(ctx.guild.id)
        success = await self.create_or_update_keyword_rule(ctx.guild, keywords)

        if success:
            await ctx.send_success(f"Added `{word}` to keyword filter")
        else:
            await ctx.send_error("Failed to update AutoMod rule. Make sure I have the **Manage Server** permission.")

    @automod.command(
        name="remove",
        description="Remove word from filter",
        usage="[word]"
    )
    @Perms.get_perms("manage_guild")
    async def automod_remove(self, ctx, *, word: str):
        """Remove word from keyword filter"""
        # Remove from database
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM chatfilter WHERE guild_id = %s AND word = %s",
                        (ctx.guild.id, word.lower())
                    )
                    if cursor.rowcount == 0:
                        return await ctx.send_warning(f"`{word}` is not in the filter")
        except:
            return await ctx.send_error("Failed to remove word from database")

        # Update AutoMod rule
        keywords = await self.get_filtered_words(ctx.guild.id)
        success = await self.create_or_update_keyword_rule(ctx.guild, keywords)

        if success:
            await ctx.send_success(f"Removed `{word}` from keyword filter")
        else:
            await ctx.send_error("Failed to update AutoMod rule")

    @automod.command(
        name="list",
        description="Show filtered words"
    )
    @Perms.get_perms("manage_guild")
    async def automod_list(self, ctx):
        """Show current filtered words"""
        keywords = await self.get_filtered_words(ctx.guild.id)

        if not keywords:
            return await ctx.send_warning("No words are currently filtered")

        embed = discord.Embed(
            title="🔍 Filtered Words",
            description=f"**{len(keywords)}** words currently filtered",
            color=self.bot.color
        )

        # Split into chunks of 20 words per field
        for i in range(0, len(keywords), 20):
            chunk = keywords[i:i+20]
            field_name = f"Words {i+1}-{min(i+20, len(keywords))}"
            field_value = "```\n" + "\n".join(chunk) + "\n```"
            embed.add_field(name=field_name, value=field_value, inline=False)

        await ctx.reply(embed=embed)

    @automod.command(
        name="bypass",
        description="Toggle AutoMod exemptions for roles/channels",
        usage="[@role/#channel]"
    )
    @Perms.get_perms("manage_guild")
    async def automod_bypass(self, ctx, target: typing.Union[discord.Role, discord.TextChannel]):
        """Toggle AutoMod exemptions for roles or channels"""
        try:
            rules = await ctx.guild.fetch_automod_rules()
            updated_count = 0
            action_taken = None

            for rule in rules:
                if rule.name in ["Keyword Filter", "Block Invites"]:
                    if isinstance(target, discord.Role):
                        # Handle role exemptions
                        current_exempt_roles = list(rule.exempt_roles) if rule.exempt_roles else []

                        if target in current_exempt_roles:
                            # Remove exemption
                            current_exempt_roles.remove(target)
                            await rule.edit(exempt_roles=current_exempt_roles)
                            action_taken = "removed"
                            updated_count += 1
                        else:
                            # Add exemption
                            current_exempt_roles.append(target)
                            await rule.edit(exempt_roles=current_exempt_roles)
                            action_taken = "added"
                            updated_count += 1

                    elif isinstance(target, discord.TextChannel):
                        # Handle channel exemptions
                        current_exempt_channels = list(rule.exempt_channels) if rule.exempt_channels else []

                        if target in current_exempt_channels:
                            # Remove exemption
                            current_exempt_channels.remove(target)
                            await rule.edit(exempt_channels=current_exempt_channels)
                            action_taken = "removed"
                            updated_count += 1
                        else:
                            # Add exemption
                            current_exempt_channels.append(target)
                            await rule.edit(exempt_channels=current_exempt_channels)
                            action_taken = "added"
                            updated_count += 1

            if updated_count > 0:
                await ctx.send_success(f"{action_taken.title()} {target.mention} {'to' if action_taken == 'added' else 'from'} AutoMod exemptions ({updated_count} rules updated)")
            else:
                await ctx.send_warning("No AutoMod rules found to update. Create some rules first with `automod invites enable` or `automod add [word]`")

        except Exception as e:
            print(f"Error toggling exemption: {e}")
            await ctx.send_error("Failed to update AutoMod exemptions. Make sure I have the **Manage Server** permission.")

    @automod.command(
        name="exemptions",
        description="Show current AutoMod exemptions"
    )
    @Perms.get_perms("manage_guild")
    async def automod_exemptions(self, ctx):
        """Show current AutoMod exemptions"""
        try:
            rules = await ctx.guild.fetch_automod_rules()

            embed = discord.Embed(
                title="🛡️ AutoMod Exemptions",
                description="Current roles and channels that bypass AutoMod",
                color=self.bot.color
            )

            found_rules = False
            for rule in rules:
                if rule.name in ["Keyword Filter", "Block Invites"]:
                    found_rules = True
                    exempt_roles = [role.mention for role in rule.exempt_roles] if rule.exempt_roles else []
                    exempt_channels = [channel.mention for channel in rule.exempt_channels] if rule.exempt_channels else []

                    field_value = ""
                    if exempt_roles:
                        field_value += f"**Roles:** {', '.join(exempt_roles)}\n"
                    if exempt_channels:
                        field_value += f"**Channels:** {', '.join(exempt_channels)}\n"

                    if not field_value:
                        field_value = "No exemptions"

                    embed.add_field(
                        name=f"📋 {rule.name}",
                        value=field_value,
                        inline=False
                    )

            if not found_rules:
                embed.description = "No AutoMod rules found. Create some rules first with `automod invites enable` or `automod add [word]`"

            await ctx.reply(embed=embed)

        except Exception as e:
            print(f"Error listing exemptions: {e}")
            await ctx.send_error("Failed to fetch AutoMod exemptions")


async def setup(bot):
    await bot.add_cog(AutoMod(bot))
