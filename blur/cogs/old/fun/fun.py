import discord, io, random, asyncio, aiohttp
from discord.ext import commands
from typing import List
from io import BytesIO
import requests
import json
import time
import dateutil
from tools.checks import Perms, Joint


class TypeRace:
    """TypeRace backend variables"""

    async def getanswer():
        async with aiohttp.ClientSession() as cs:
            async with cs.get("https://www.mit.edu/~ecprice/wordlist.100000") as r:
                byte = await r.read()
                data = str(byte, "utf-8")
                data = data.splitlines()
                mes = ""
                for _ in range(10):
                    mes = f"{mes}{random.choice(data)} "

                return mes

    def accurate(first: str, second: str):
        percentage = 0
        i = 0
        text1 = first.split()
        text2 = second.split()
        for t in text2:
            try:
                if t == text1[i]:
                    percentage += 10
                i += 1
            except:
                return percentage

        return percentage


class RockPaperScissors(discord.ui.View):
    def __init__(self, ctx: commands.Context):
        self.ctx = ctx
        self.get_emoji = {"rock": "🪨", "paper": "📰", "scissors": "✂️"}
        self.status = False
        super().__init__(timeout=10)

    async def disable_buttons(self):
        for item in self.children:
            item.disabled = True

        await self.message.edit(view=self)

    async def action(self, interaction: discord.Interaction, selection: str):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.client.ext.send_warning(
                interaction, "This is not your game", ephemeral=True
            )
        botselection = random.choice(["rock", "paper", "scissors"])

        def getwinner():
            if botselection == "rock" and selection == "scissors":
                return interaction.client.user.id
            elif botselection == "rock" and selection == "paper":
                return interaction.user.id
            elif botselection == "paper" and selection == "rock":
                return interaction.client.user.id
            elif botselection == "paper" and selection == "scissors":
                return interaction.user.id
            elif botselection == "scissors" and selection == "rock":
                return interaction.user.id
            elif botselection == "scissors" and selection == "paper":
                return interaction.client.user.id
            else:
                return "tie"

        if getwinner() == "tie":
            await interaction.response.edit_message(
                embed=discord.Embed(
                    color=interaction.client.color,
                    title="Tie!",
                    description=f"```You both picked {self.get_emoji.get(selection)}```",
                )
            )
        elif getwinner() == interaction.user.id:
            await interaction.response.edit_message(
                embed=discord.Embed(
                    color=interaction.client.color,
                    title="You won!",
                    description=f"```You picked {self.get_emoji.get(selection)} and the bot picked {self.get_emoji.get(botselection)}```",
                )
            )
        else:
            await interaction.response.edit_message(
                embed=discord.Embed(
                    color=interaction.client.color,
                    title="Bot won!",
                    description=f"```You picked {self.get_emoji.get(selection)} and the bot picked {self.get_emoji.get(botselection)}```",
                )
            )

        self.status = True
        await self.disable_buttons()

    @discord.ui.button(emoji="🪨")
    async def rock(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.action(interaction, "rock")

    @discord.ui.button(emoji="📰")
    async def paper(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.action(interaction, "paper")

    @discord.ui.button(emoji="✂️")
    async def scissors(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.action(interaction, "scissors")

    async def on_timeout(self) -> None:
        if self.status == False:
            await self.disable_buttons()


class Fun(commands.Cog):
    __is_hidden_event__ = True

    def __init__(self, bot: commands.AutoShardedBot):
        self.bot = bot

    @commands.command(description="play rock paper scissors", help="fun")
    async def rps(self, ctx: commands.Context):
        embed = discord.Embed(
            color=self.bot.color,
            title="Rock Paper Scissors",
            description="Click a button to play",
        )
        view = RockPaperScissors(ctx)
        view.message = await ctx.reply(embed=embed, view=view)



    @commands.command(description="flip a coin", help="fun")
    async def coinflip(self, ctx: commands.Context):
        result = random.choice(["Heads", "Tails"])
        embed = discord.Embed(
            color=self.bot.color,
            title="🪙 Coin Flip",
            description=f"The coin landed on **{result}**!",
        )
        await ctx.reply(embed=embed)

    @commands.command(description="roll a dice", help="fun", usage="<sides>")
    async def dice(self, ctx: commands.Context, sides: int = 6):
        if sides < 2:
            return await ctx.send_warning("Dice must have at least 2 sides!")
        if sides > 100:
            return await ctx.send_warning("Dice cannot have more than 100 sides!")
        
        result = random.randint(1, sides)
        embed = discord.Embed(
            color=self.bot.color,
            title="🎲 Dice Roll",
            description=f"You rolled a **{result}** on a {sides}-sided dice!",
        )
        await ctx.reply(embed=embed)

    @commands.command(description="get a random joke", help="fun")
    async def joke(self, ctx: commands.Context):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://official-joke-api.appspot.com/random_joke") as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        
                        embed = discord.Embed(
                            color=self.bot.color,
                            title="😂 Random Joke",
                        )
                        embed.add_field(name="Setup", value=data["setup"], inline=False)
                        embed.add_field(name="Punchline", value=data["punchline"], inline=False)
                        
                        await ctx.reply(embed=embed)
                    else:
                        await ctx.send_warning("Failed to fetch joke!")
        except Exception as e:
            await ctx.send_warning(f"Error fetching joke: {e}")



    @commands.command(description="make the bot say something", help="fun", usage="[message]")
    @Perms.get_perms("manage_messages")
    async def say(self, ctx: commands.Context, *, message: str):
        await ctx.message.delete()
        await ctx.send(message)


async def setup(bot):
    await bot.add_cog(Fun(bot))
