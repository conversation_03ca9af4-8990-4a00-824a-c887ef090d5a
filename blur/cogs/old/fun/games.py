import discord
from discord.ext import commands
import random
import asyncio


class TicTacToeButton(discord.ui.Button):
    def __init__(self, x: int, y: int):
        super().__init__(style=discord.ButtonStyle.secondary, label='\u200b', row=y)
        self.x = x
        self.y = y

    async def callback(self, interaction: discord.Interaction):
        assert self.view is not None
        view: TicTacToe = self.view
        state = view.board[self.y][self.x]
        
        if state in (view.X, view.O):
            return
        
        if view.current_player == view.X:
            if interaction.user != view.player1:
                return await interaction.response.send_message("It's not your turn!", ephemeral=True)
            self.style = discord.ButtonStyle.danger
            self.label = 'X'
            self.disabled = True
            view.board[self.y][self.x] = view.X
            view.current_player = view.O
            content = f"{view.player2.mention}'s turn (O)"
        else:
            if interaction.user != view.player2:
                return await interaction.response.send_message("It's not your turn!", ephemeral=True)
            self.style = discord.ButtonStyle.success
            self.label = 'O'
            self.disabled = True
            view.board[self.y][self.x] = view.O
            view.current_player = view.X
            content = f"{view.player1.mention}'s turn (X)"

        winner = view.check_board_winner()
        if winner is not None:
            if winner == view.X:
                content = f'🎉 {view.player1.mention} won!'
            elif winner == view.O:
                content = f'🎉 {view.player2.mention} won!'
            else:
                content = "It's a tie!"

            for child in view.children:
                child.disabled = True

            view.stop()

        await interaction.response.edit_message(content=content, view=view)


class TicTacToe(discord.ui.View):
    children: list[TicTacToeButton]
    X = -1
    O = 1
    Tie = 2

    def __init__(self, player1: discord.Member, player2: discord.Member):
        super().__init__(timeout=300)
        self.player1 = player1
        self.player2 = player2
        self.current_player = self.X
        self.board = [
            [0, 0, 0],
            [0, 0, 0],
            [0, 0, 0],
        ]

        for x in range(3):
            for y in range(3):
                self.add_item(TicTacToeButton(x, y))

    def check_board_winner(self):
        for across in self.board:
            value = sum(across)
            if value == 3:
                return self.O
            elif value == -3:
                return self.X

        for line in range(3):
            value = self.board[0][line] + self.board[1][line] + self.board[2][line]
            if value == 3:
                return self.O
            elif value == -3:
                return self.X

        diag = self.board[0][2] + self.board[1][1] + self.board[2][0]
        if diag == 3:
            return self.O
        elif diag == -3:
            return self.X

        diag = self.board[0][0] + self.board[1][1] + self.board[2][2]
        if diag == 3:
            return self.O
        elif diag == -3:
            return self.X

        if all(i != 0 for row in self.board for i in row):
            return self.Tie

        return None

    async def on_timeout(self):
        for child in self.children:
            child.disabled = True
        
        try:
            await self.message.edit(content="Game timed out!", view=self)
        except:
            pass


class BlackjackView(discord.ui.View):
    def __init__(self, ctx, player_hand, dealer_hand, deck):
        super().__init__(timeout=60)
        self.ctx = ctx
        self.player_hand = player_hand
        self.dealer_hand = dealer_hand
        self.deck = deck
        self.game_over = False

    def calculate_hand_value(self, hand):
        value = 0
        aces = 0
        for card in hand:
            if card[0] in ['J', 'Q', 'K']:
                value += 10
            elif card[0] == 'A':
                aces += 1
                value += 11
            else:
                value += int(card[0])
        
        while value > 21 and aces:
            value -= 10
            aces -= 1
        
        return value

    def format_hand(self, hand, hide_first=False):
        if hide_first:
            return f"🎴 {hand[1]}"
        return " ".join(hand)

    @discord.ui.button(label="Hit", style=discord.ButtonStyle.primary, emoji="🃏")
    async def hit(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            return await interaction.response.send_message("This isn't your game!", ephemeral=True)
        
        if self.game_over:
            return
        
        # Draw card
        card = self.deck.pop()
        self.player_hand.append(card)
        
        player_value = self.calculate_hand_value(self.player_hand)
        
        if player_value > 21:
            # Player busts
            self.game_over = True
            for child in self.children:
                child.disabled = True
            
            embed = discord.Embed(
                title="🃏 Blackjack",
                color=0xff0000
            )
            embed.add_field(
                name="Your Hand",
                value=f"{self.format_hand(self.player_hand)} (Value: {player_value})",
                inline=False
            )
            embed.add_field(
                name="Result",
                value="💥 **BUST!** You went over 21!",
                inline=False
            )
            
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            embed = discord.Embed(
                title="🃏 Blackjack",
                color=self.ctx.bot.color
            )
            embed.add_field(
                name="Your Hand",
                value=f"{self.format_hand(self.player_hand)} (Value: {player_value})",
                inline=False
            )
            embed.add_field(
                name="Dealer's Hand",
                value=self.format_hand(self.dealer_hand, hide_first=True),
                inline=False
            )
            
            await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="Stand", style=discord.ButtonStyle.secondary, emoji="✋")
    async def stand(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            return await interaction.response.send_message("This isn't your game!", ephemeral=True)
        
        if self.game_over:
            return
        
        self.game_over = True
        for child in self.children:
            child.disabled = True
        
        # Dealer plays
        dealer_value = self.calculate_hand_value(self.dealer_hand)
        while dealer_value < 17:
            card = self.deck.pop()
            self.dealer_hand.append(card)
            dealer_value = self.calculate_hand_value(self.dealer_hand)
        
        player_value = self.calculate_hand_value(self.player_hand)
        
        # Determine winner
        if dealer_value > 21:
            result = "🎉 **YOU WIN!** Dealer busted!"
            color = 0x00ff00
        elif player_value > dealer_value:
            result = "🎉 **YOU WIN!**"
            color = 0x00ff00
        elif dealer_value > player_value:
            result = "😔 **DEALER WINS!**"
            color = 0xff0000
        else:
            result = "🤝 **TIE!**"
            color = 0xffff00
        
        embed = discord.Embed(
            title="🃏 Blackjack",
            color=color
        )
        embed.add_field(
            name="Your Hand",
            value=f"{self.format_hand(self.player_hand)} (Value: {player_value})",
            inline=False
        )
        embed.add_field(
            name="Dealer's Hand",
            value=f"{self.format_hand(self.dealer_hand)} (Value: {dealer_value})",
            inline=False
        )
        embed.add_field(
            name="Result",
            value=result,
            inline=False
        )
        
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        if not self.game_over:
            for child in self.children:
                child.disabled = True
            
            try:
                await self.message.edit(content="Game timed out!", view=self)
            except:
                pass


class Games(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(
        name="tictactoe",
        description="Play tic-tac-toe with someone",
        usage="[member]",
        aliases=["ttt"]
    )
    async def tictactoe(self, ctx, member: discord.Member):
        """Play tic-tac-toe"""
        if member == ctx.author:
            return await ctx.send_warning("You can't play against yourself!")
        
        if member.bot:
            return await ctx.send_warning("You can't play against bots!")
        
        view = TicTacToe(ctx.author, member)
        embed = discord.Embed(
            title="🎮 Tic-Tac-Toe",
            description=f"{ctx.author.mention} (X) vs {member.mention} (O)\n\n{ctx.author.mention}'s turn!",
            color=self.bot.color
        )
        
        view.message = await ctx.reply(embed=embed, view=view)

    @commands.command(
        name="blackjack",
        description="Play blackjack",
        aliases=["bj"]
    )
    async def blackjack(self, ctx):
        """Play blackjack"""
        # Create deck
        suits = ['♠️', '♥️', '♦️', '♣️']
        ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        deck = [f"{rank}{suit}" for suit in suits for rank in ranks]
        random.shuffle(deck)
        
        # Deal initial cards
        player_hand = [deck.pop(), deck.pop()]
        dealer_hand = [deck.pop(), deck.pop()]
        
        view = BlackjackView(ctx, player_hand, dealer_hand, deck)
        player_value = view.calculate_hand_value(player_hand)
        
        # Check for blackjack
        if player_value == 21:
            embed = discord.Embed(
                title="🃏 Blackjack",
                color=0x00ff00
            )
            embed.add_field(
                name="Your Hand",
                value=f"{view.format_hand(player_hand)} (Value: {player_value})",
                inline=False
            )
            embed.add_field(
                name="Result",
                value="🎉 **BLACKJACK!** You got 21!",
                inline=False
            )
            
            await ctx.reply(embed=embed)
        else:
            embed = discord.Embed(
                title="🃏 Blackjack",
                description="Hit to draw another card, Stand to keep your current hand",
                color=self.bot.color
            )
            embed.add_field(
                name="Your Hand",
                value=f"{view.format_hand(player_hand)} (Value: {player_value})",
                inline=False
            )
            embed.add_field(
                name="Dealer's Hand",
                value=view.format_hand(dealer_hand, hide_first=True),
                inline=False
            )
            
            view.message = await ctx.reply(embed=embed, view=view)

    @commands.command(
        name="slots",
        description="Play slot machine",
        aliases=["slot"]
    )
    async def slots(self, ctx):
        """Play slot machine"""
        emojis = ['🍎', '🍊', '🍋', '🍇', '🍓', '🍒', '💎', '⭐', '🔔']
        
        # Spin the slots
        result = [random.choice(emojis) for _ in range(3)]
        
        embed = discord.Embed(
            title="🎰 Slot Machine",
            color=self.bot.color
        )
        
        # Display result
        slot_display = f"[ {result[0]} | {result[1]} | {result[2]} ]"
        embed.add_field(
            name="Result",
            value=slot_display,
            inline=False
        )
        
        # Check for wins
        if result[0] == result[1] == result[2]:
            if result[0] == '💎':
                embed.add_field(name="🎉 JACKPOT!", value="Three diamonds! 💎💎💎", inline=False)
                embed.color = 0xffd700
            elif result[0] == '⭐':
                embed.add_field(name="🌟 BIG WIN!", value="Three stars! ⭐⭐⭐", inline=False)
                embed.color = 0x00ff00
            else:
                embed.add_field(name="🎊 WIN!", value="Three of a kind!", inline=False)
                embed.color = 0x00ff00
        elif result[0] == result[1] or result[1] == result[2] or result[0] == result[2]:
            embed.add_field(name="🎯 Small Win!", value="Two of a kind!", inline=False)
            embed.color = 0xffff00
        else:
            embed.add_field(name="😔 No Win", value="Better luck next time!", inline=False)
            embed.color = 0xff0000
        
        await ctx.reply(embed=embed)

    @commands.command(
        name="guess",
        description="Guess the number game",
        usage="<max_number>",
        aliases=["guessnumber"]
    )
    async def guess_number(self, ctx, max_number: int = 100):
        """Guess the number game"""
        if max_number < 10 or max_number > 1000:
            return await ctx.send_warning("Number must be between 10-1000!")
        
        number = random.randint(1, max_number)
        attempts = 0
        max_attempts = min(10, max_number // 10 + 3)
        
        embed = discord.Embed(
            title="🔢 Guess the Number",
            description=f"I'm thinking of a number between 1 and {max_number}!\nYou have {max_attempts} attempts.",
            color=self.bot.color
        )
        
        await ctx.reply(embed=embed)
        
        def check(message):
            return (message.author == ctx.author and 
                   message.channel == ctx.channel and 
                   message.content.isdigit())
        
        while attempts < max_attempts:
            try:
                message = await self.bot.wait_for('message', check=check, timeout=30.0)
                guess = int(message.content)
                attempts += 1
                
                if guess == number:
                    embed = discord.Embed(
                        title="🎉 Congratulations!",
                        description=f"You guessed it! The number was **{number}**\nIt took you {attempts} attempt{'s' if attempts != 1 else ''}!",
                        color=0x00ff00
                    )
                    await ctx.send(embed=embed)
                    return
                elif guess < number:
                    hint = "📈 Too low!"
                else:
                    hint = "📉 Too high!"
                
                remaining = max_attempts - attempts
                if remaining > 0:
                    embed = discord.Embed(
                        title="🔢 Guess the Number",
                        description=f"{hint}\n{remaining} attempt{'s' if remaining != 1 else ''} remaining.",
                        color=self.bot.color
                    )
                    await ctx.send(embed=embed)
                
            except asyncio.TimeoutError:
                embed = discord.Embed(
                    title="⏰ Time's Up!",
                    description=f"You took too long! The number was **{number}**.",
                    color=0xff0000
                )
                await ctx.send(embed=embed)
                return
        
        # Out of attempts
        embed = discord.Embed(
            title="😔 Game Over",
            description=f"You're out of attempts! The number was **{number}**.",
            color=0xff0000
        )
        await ctx.send(embed=embed)

    @commands.command(
        name="trivia",
        description="Answer trivia questions"
    )
    async def trivia(self, ctx):
        """Trivia game"""
        questions = [
            {
                "question": "What is the capital of France?",
                "options": ["London", "Berlin", "Paris", "Madrid"],
                "answer": 2
            },
            {
                "question": "Which planet is known as the Red Planet?",
                "options": ["Venus", "Mars", "Jupiter", "Saturn"],
                "answer": 1
            },
            {
                "question": "What is 2 + 2?",
                "options": ["3", "4", "5", "6"],
                "answer": 1
            },
            {
                "question": "Who painted the Mona Lisa?",
                "options": ["Van Gogh", "Picasso", "Da Vinci", "Monet"],
                "answer": 2
            },
            {
                "question": "What is the largest ocean on Earth?",
                "options": ["Atlantic", "Indian", "Arctic", "Pacific"],
                "answer": 3
            }
        ]
        
        question_data = random.choice(questions)
        
        embed = discord.Embed(
            title="🧠 Trivia Question",
            description=question_data["question"],
            color=self.bot.color
        )
        
        options_text = ""
        for i, option in enumerate(question_data["options"]):
            options_text += f"{i+1}. {option}\n"
        
        embed.add_field(name="Options", value=options_text, inline=False)
        embed.set_footer(text="Type the number of your answer (1-4)")
        
        await ctx.reply(embed=embed)
        
        def check(message):
            return (message.author == ctx.author and 
                   message.channel == ctx.channel and 
                   message.content in ['1', '2', '3', '4'])
        
        try:
            message = await self.bot.wait_for('message', check=check, timeout=15.0)
            answer = int(message.content) - 1
            
            if answer == question_data["answer"]:
                embed = discord.Embed(
                    title="🎉 Correct!",
                    description=f"The answer was **{question_data['options'][question_data['answer']]}**!",
                    color=0x00ff00
                )
            else:
                embed = discord.Embed(
                    title="❌ Wrong!",
                    description=f"The correct answer was **{question_data['options'][question_data['answer']]}**.",
                    color=0xff0000
                )
            
            await ctx.send(embed=embed)
            
        except asyncio.TimeoutError:
            embed = discord.Embed(
                title="⏰ Time's Up!",
                description=f"The correct answer was **{question_data['options'][question_data['answer']]}**.",
                color=0xff0000
            )
            await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(Games(bot))
