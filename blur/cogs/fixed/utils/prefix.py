import discord
from discord.ext import commands
from tools.checks import Perms, Donor
from tools.ext import embed
from config.constants import Colors, Emojis


class Prefix(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="prefix",
        description="Manage server prefix",
        invoke_without_command=True
    )
    async def prefix(self, ctx):
        """Show current server prefix (no permissions required)"""
        if ctx.invoked_subcommand is None:
            # Invoke help for prefix command
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='prefix')
            else:
                # Fallback if help command not found - show current prefix
                try:
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM prefixes WHERE guild_id = %s",
                                (ctx.guild.id,)
                            )
                            result = await cursor.fetchone()

                    current_prefix = result[0] if result else ","

                    # Check if user has selfprefix
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                                (ctx.author.id,)
                            )
                            selfprefix_result = await cursor.fetchone()

                    selfprefix = selfprefix_result[0] if selfprefix_result else None

                    embed_obj = discord.Embed(
                        title="🔧 Server Prefix",
                        color=Colors.default
                    )

                    embed_obj.add_field(
                        name="Current Server Prefix",
                        value=f"`{current_prefix}`",
                        inline=False
                    )

                    if selfprefix:
                        embed_obj.add_field(
                            name="Your Personal Prefix",
                            value=f"`{selfprefix}` (Donor Only)",
                            inline=False
                        )

                    await ctx.reply(embed=embed_obj)

                except Exception as e:
                    await embed.error(ctx, f"Failed to get prefix: {e}")

    @prefix.command(
        name="set",
        description="Set server prefix"
    )
    @Perms.get_perms("manage_guild")
    async def prefix_set(self, ctx, prefix: str = None):
        """Set server prefix (requires manage_guild permission)"""
        if prefix is None:
            return await embed.warn(ctx, "Please provide a prefix to set!")

        # Reset to default if "reset" or "default"
        if prefix.lower() in ["reset", "default"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )

                await embed.success(ctx, "Prefix reset to default: `,`")

            except Exception as e:
                await embed.error(ctx, f"Failed to reset prefix: {e}")
            return

        if len(prefix) > 5:
            return await embed.error(ctx, "Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO prefixes (guild_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.guild.id, prefix, prefix)
                    )

            await embed.success(ctx, f"Server prefix set to `{prefix}`")

        except Exception as e:
            await embed.error(ctx, f"Failed to set prefix: {e}")

    @commands.group(
        name="selfprefix",
        description="Manage your personal prefix (donor only)",
        aliases=["sp"],
        invoke_without_command=True
    )
    @Donor.check_donor()
    async def selfprefix(self, ctx):
        """Show your personal prefix"""
        if ctx.invoked_subcommand is None:
            # Invoke help for selfprefix command
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='selfprefix')
            else:
                # Fallback if help command not found
                try:
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                                (ctx.author.id,)
                            )
                            result = await cursor.fetchone()

                    if result:
                        await embed.success(ctx, f"Your personal prefix: `{result[0]}`")
                    else:
                        await embed.warn(ctx, "You don't have a personal prefix set!")

                except Exception as e:
                    await embed.error(ctx, f"Failed to get selfprefix: {e}")

    @selfprefix.command(
        name="set",
        description="Set your personal prefix"
    )
    @Donor.check_donor()
    async def selfprefix_set(self, ctx, prefix: str = None):
        """Set your personal prefix (donor only)"""
        if prefix is None:
            return await embed.warn(ctx, "Please provide a prefix to set!")

        if prefix.lower() in ["reset", "default", "remove"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM selfprefixes WHERE user_id = %s",
                            (ctx.author.id,)
                        )

                await embed.success(ctx, "Personal prefix removed!")

            except Exception as e:
                await embed.error(ctx, f"Failed to remove selfprefix: {e}")
            return

        if len(prefix) > 5:
            return await embed.error(ctx, "Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO selfprefixes (user_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.author.id, prefix, prefix)
                    )

            await embed.success(ctx, f"Personal prefix set to `{prefix}`")

        except Exception as e:
            await embed.error(ctx, f"Failed to set selfprefix: {e}")


async def setup(bot):
    await bot.add_cog(Prefix(bot))
