import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Colors
from utils.parse import EmbedBuilder


class GoodbyeConfig(commands.Cog):
    """Goodbye message configuration system"""

    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="goodbye",
        description="Configure goodbye messages",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def goodbye(self, ctx):
        if ctx.invoked_subcommand is None:
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='goodbye')

    @goodbye.command(
        name="add",
        description="Add goodbye message for channel",
        usage="[channel] [message]"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_add(self, ctx, channel: discord.TextChannel, *, message: str):
        """Add goodbye message for channel"""
        try:
            # Check current count
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT COUNT(*) FROM goodbye_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    count = (await cursor.fetchone())[0]

                    if count >= 3:
                        return await embed.warn(ctx, "Maximum 3 goodbye channels allowed per server!")

                    # Check if channel already exists
                    await cursor.execute(
                        "SELECT * FROM goodbye_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    existing = await cursor.fetchone()

                    if existing:
                        # Update existing message
                        await cursor.execute(
                            "UPDATE goodbye_messages SET message = %s WHERE guild_id = %s AND channel_id = %s",
                            (message, ctx.guild.id, channel.id)
                        )
                        await embed.success(ctx, f"Goodbye message updated for {channel.mention}")
                        return

                    # Add new goodbye message
                    await cursor.execute(
                        "INSERT INTO goodbye_messages (guild_id, channel_id, message) VALUES (%s, %s, %s)",
                        (ctx.guild.id, channel.id, message)
                    )

            await ctx.message.add_reaction("✅")

        except Exception as e:
            await embed.error(ctx, f"Failed to add goodbye message: {e}")

    @goodbye.command(
        name="remove",
        description="Remove goodbye message for channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_remove(self, ctx, channel: discord.TextChannel):
        """Remove goodbye message for channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM goodbye_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, f"No goodbye message found for {channel.mention}")

            await embed.success(ctx, f"Goodbye message removed for {channel.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to remove goodbye message: {e}")

    @goodbye.command(
        name="view",
        description="View goodbye messages",
        usage="<channel>"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_view(self, ctx, channel: discord.TextChannel = None):
        """View goodbye messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    if channel:
                        # Test specific channel
                        await cursor.execute(
                            "SELECT message FROM goodbye_messages WHERE guild_id = %s AND channel_id = %s",
                            (ctx.guild.id, channel.id)
                        )
                        result = await cursor.fetchone()

                        if not result:
                            return await embed.warn(ctx, f"No goodbye message found for {channel.mention}")

                        # Parse message with embed builder
                        message = result[0]

                        # Create a mock context for variable replacement
                        class MockContext:
                            def __init__(self, author, guild, channel):
                                self.author = author
                                self.guild = guild
                                self.channel = channel

                        mock_ctx = MockContext(ctx.author, ctx.guild, ctx.channel)
                        builder = EmbedBuilder(mock_ctx)

                        # Replace variables manually for goodbye system
                        message = message.replace("{user}", str(ctx.author))  # Use str() for goodbye, not mention
                        message = message.replace("{user.mention}", ctx.author.mention)
                        message = message.replace("{user.name}", ctx.author.display_name)
                        message = message.replace("{user.id}", str(ctx.author.id))
                        message = message.replace("{server}", ctx.guild.name)
                        message = message.replace("{server.id}", str(ctx.guild.id))
                        message = message.replace("{count}", str(ctx.guild.member_count))

                        # Parse the message
                        parsed = builder.parse_embed_string(message)

                        # React with success and send actual message to target channel
                        await ctx.message.add_reaction("✅")

                        # Send the actual message to target channel (like real goodbye event)
                        if parsed['embed']:
                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - send as plain text
                            await channel.send(content=parsed['content'] or message, view=parsed['view'])

                    else:
                        # Show all goodbye channels
                        await cursor.execute(
                            "SELECT channel_id FROM goodbye_messages WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        results = await cursor.fetchall()

                        if not results:
                            return await embed.warn(ctx, "No goodbye messages configured!")

                        channels = []
                        for result in results:
                            ch = ctx.guild.get_channel(result[0])
                            if ch:
                                channels.append(ch.mention)

                        if not channels:
                            return await embed.warn(ctx, "No valid goodbye channels found!")

                        embed_obj = discord.Embed(
                            title="👋 Goodbye Channels",
                            description="\n".join(f"• {ch}" for ch in channels),
                            color=Colors.default
                        )
                        await ctx.send(embed=embed_obj)

        except Exception as e:
            await embed.error(ctx, f"Failed to view goodbye messages: {e}")

    @goodbye.command(
        name="reset",
        description="Delete all goodbye messages"
    )
    @Perms.get_perms("manage_guild")
    async def goodbye_reset(self, ctx):
        """Delete all goodbye messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM goodbye_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, "No goodbye messages to delete!")

            await embed.success(ctx, f"Deleted {affected_rows} goodbye message(s)")

        except Exception as e:
            await embed.error(ctx, f"Failed to reset goodbye messages: {e}")

    @goodbye.command(
        name="variables",
        description="Show available variables"
    )
    async def goodbye_variables(self, ctx):
        """Show available variables"""
        embed_obj = discord.Embed(
            title="👋 Goodbye Variables & Embed Guide",
            description="Available variables and embed syntax for goodbye messages",
            color=Colors.default
        )

        embed_obj.add_field(
            name="User Variables",
            value="`{user}` - User's name (not mention)\n"
                  "`{user.mention}` - Mention user\n"
                  "`{user.name}` - Display name\n"
                  "`{user.id}` - User ID\n"
                  "`{user.avatar}` - Avatar URL",
            inline=True
        )

        embed_obj.add_field(
            name="Server Variables",
            value="`{server}` - Server name\n"
                  "`{guild.name}` - Same as server\n"
                  "`{count}` - Member count\n"
                  "`{server.id}` - Server ID\n"
                  "`{guild.icon}` - Server icon URL",
            inline=True
        )

        embed_obj.add_field(
            name="Embed Syntax",
            value="`{embed}` - Start custom embed\n"
                  "`$v` - Separator between parts\n"
                  "`{title: text}` - Embed title\n"
                  "`{description: text}` - Description\n"
                  "`{color: name/hex}` - Color\n"
                  "`{field: name && value}` - Field",
            inline=False
        )

        embed_obj.add_field(
            name="Color Examples",
            value="**Named colors:** `red`, `blue`, `green`, `purple`, `orange`, `pink`, `yellow`, `cyan`, `magenta`, `lime`, `navy`, `teal`, `silver`, `gold`\n"
                  "**Hex colors:** `#ff0000`, `#00ff00`, `#0000ff`\n"
                  "**Discord colors:** `blurple`, `greyple`, `dark_theme`, `light_theme`",
            inline=False
        )

        embed_obj.add_field(
            name="Examples",
            value="**Simple:** `Goodbye {user}! We'll miss you.`\n"
                  "**Custom embed:** `{embed}$v{title: Farewell}$v{description: {user} left {server}}$v{color: red}`",
            inline=False
        )

        await ctx.send(embed=embed_obj)


async def setup(bot):
    await bot.add_cog(GoodbyeConfig(bot))
