import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Colors
from utils.parse import EmbedBuilder


class Booster(commands.Cog):
    """Server booster system with perks and rewards"""
    
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Handle boost events"""
        # Check if user started boosting
        if not before.premium_since and after.premium_since:
            await self.handle_boost_start(after)
        
        # Check if user stopped boosting
        elif before.premium_since and not after.premium_since:
            await self.handle_boost_end(after)

    async def handle_boost_start(self, member):
        """Handle when someone starts boosting"""
        try:
            # Send boost messages to all configured channels
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id, message FROM boost_messages WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    boost_messages = await cursor.fetchall()

            for channel_id, message in boost_messages:
                channel = self.bot.get_channel(channel_id)
                if channel:
                    # Check if bot has permission to send messages
                    if not channel.permissions_for(member.guild.me).send_messages:
                        print(f"Missing send_messages permission in boost channel {channel.name} ({channel.id}) for guild {member.guild.name}")
                        continue

                    # Create a mock context for variable replacement
                    class MockContext:
                        def __init__(self, author, guild, channel):
                            self.author = author
                            self.guild = guild
                            self.channel = channel

                    mock_ctx = MockContext(member, member.guild, channel)
                    builder = EmbedBuilder(mock_ctx)

                    # Replace variables manually for boost system
                    processed_message = message.replace("{user}", member.mention)
                    processed_message = processed_message.replace("{user.mention}", member.mention)
                    processed_message = processed_message.replace("{user.name}", member.display_name)
                    processed_message = processed_message.replace("{user.id}", str(member.id))
                    processed_message = processed_message.replace("{server}", member.guild.name)
                    processed_message = processed_message.replace("{server.id}", str(member.guild.id))
                    processed_message = processed_message.replace("{count}", str(member.guild.member_count))

                    # Parse the message
                    parsed = builder.parse_embed_string(processed_message)

                    try:
                        if parsed['embed']:
                            # Send custom embed
                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - create basic embed
                            embed_obj = discord.Embed(
                                description=parsed['content'] or processed_message,
                                color=0xff73fa
                            )
                            embed_obj.set_thumbnail(url=member.display_avatar.url)

                            await channel.send(embed=embed_obj, view=parsed['view'])
                    except discord.Forbidden:
                        print(f"Failed to send boost message to {member.guild.name}: Missing permissions in {channel.name}")
                    except Exception as e:
                        print(f"Failed to send boost message to {member.guild.name}: {e}")

            # Give booster perks
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    perks = await cursor.fetchall()

            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = member.guild.get_role(perk[2])  # perk_value field
                    if role and role < member.guild.me.top_role:
                        try:
                            await member.add_roles(role, reason="Booster perk")
                        except discord.Forbidden:
                            print(f"Failed to give booster perk role to {member} in {member.guild.name}: Missing permissions")
                        except Exception as e:
                            print(f"Failed to give booster perk role to {member} in {member.guild.name}: {e}")

        except Exception as e:
            print(f"Error in handle_boost_start: {e}")

    async def handle_boost_end(self, member):
        """Handle when someone stops boosting"""
        try:
            # Remove booster perks
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    perks = await cursor.fetchall()

            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = member.guild.get_role(perk[2])  # perk_value field
                    if role and role in member.roles:
                        try:
                            await member.remove_roles(role, reason="No longer boosting")
                        except discord.Forbidden:
                            print(f"Failed to remove booster perk role from {member} in {member.guild.name}: Missing permissions")
                        except Exception as e:
                            print(f"Failed to remove booster perk role from {member} in {member.guild.name}: {e}")

        except Exception as e:
            print(f"Error in handle_boost_end: {e}")

    @commands.group(
        name="booster",
        description="Server booster system",
        invoke_without_command=True,
        aliases=["boost"]
    )
    @Perms.get_perms("manage_guild")
    async def booster(self, ctx):
        if ctx.invoked_subcommand is None:
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='booster')

    @booster.command(
        name="add",
        description="Add boost message for channel",
        usage="[channel] [message]"
    )
    @Perms.get_perms("manage_guild")
    async def booster_add(self, ctx, channel: discord.TextChannel, *, message: str):
        """Add boost message for channel"""
        try:
            # Check current count
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT COUNT(*) FROM boost_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    count = (await cursor.fetchone())[0]

                    if count >= 3:
                        return await embed.warn(ctx, "Maximum 3 boost channels allowed per server!")

                    # Check if channel already exists
                    await cursor.execute(
                        "SELECT * FROM boost_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    existing = await cursor.fetchone()

                    if existing:
                        # Update existing message
                        await cursor.execute(
                            "UPDATE boost_messages SET message = %s WHERE guild_id = %s AND channel_id = %s",
                            (message, ctx.guild.id, channel.id)
                        )
                        await embed.success(ctx, f"Boost message updated for {channel.mention}")
                        return

                    # Add new boost message
                    await cursor.execute(
                        "INSERT INTO boost_messages (guild_id, channel_id, message) VALUES (%s, %s, %s)",
                        (ctx.guild.id, channel.id, message)
                    )

            await embed.success(ctx, f"Boost message added for {channel.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to add boost message: {e}")

    @booster.command(
        name="remove",
        description="Remove boost message for channel",
        usage="[channel]"
    )
    @Perms.get_perms("manage_guild")
    async def booster_remove(self, ctx, channel: discord.TextChannel):
        """Remove boost message for channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM boost_messages WHERE guild_id = %s AND channel_id = %s",
                        (ctx.guild.id, channel.id)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, f"No boost message found for {channel.mention}")

            await embed.success(ctx, f"Boost message removed for {channel.mention}")

        except Exception as e:
            await embed.error(ctx, f"Failed to remove boost message: {e}")

    @booster.command(
        name="view",
        description="View boost messages",
        usage="<channel>"
    )
    @Perms.get_perms("manage_guild")
    async def booster_view(self, ctx, channel: discord.TextChannel = None):
        """View boost messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    if channel:
                        # Test specific channel
                        await cursor.execute(
                            "SELECT message FROM boost_messages WHERE guild_id = %s AND channel_id = %s",
                            (ctx.guild.id, channel.id)
                        )
                        result = await cursor.fetchone()

                        if not result:
                            return await embed.warn(ctx, f"No boost message found for {channel.mention}")

                        # Parse message with embed builder
                        message = result[0]

                        # Create a mock context for variable replacement
                        class MockContext:
                            def __init__(self, author, guild, channel):
                                self.author = author
                                self.guild = guild
                                self.channel = channel

                        mock_ctx = MockContext(ctx.author, ctx.guild, ctx.channel)
                        builder = EmbedBuilder(mock_ctx)

                        # Replace variables manually for boost system
                        message = message.replace("{user}", ctx.author.mention)
                        message = message.replace("{user.mention}", ctx.author.mention)
                        message = message.replace("{user.name}", ctx.author.display_name)
                        message = message.replace("{user.id}", str(ctx.author.id))
                        message = message.replace("{server}", ctx.guild.name)
                        message = message.replace("{server.id}", str(ctx.guild.id))
                        message = message.replace("{count}", str(ctx.guild.member_count))

                        # Parse the message
                        parsed = builder.parse_embed_string(message)

                        # Send the parsed result
                        if parsed['embed']:
                            # Add test footer to embed
                            if not parsed['embed'].footer.text:
                                parsed['embed'].set_footer(text="🧪 This is a test message")
                            else:
                                parsed['embed'].set_footer(text=f"{parsed['embed'].footer.text} | 🧪 Test")

                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - create basic embed
                            embed_obj = discord.Embed(
                                description=parsed['content'] or message,
                                color=0xff73fa
                            )
                            embed_obj.set_thumbnail(url=ctx.author.display_avatar.url)
                            embed_obj.set_footer(text="🧪 This is a test message")

                            await channel.send(embed=embed_obj, view=parsed['view'])
                        await embed.success(ctx, f"Test boost message sent to {channel.mention}")

                    else:
                        # Show all boost channels
                        await cursor.execute(
                            "SELECT channel_id FROM boost_messages WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        results = await cursor.fetchall()

                        if not results:
                            return await embed.warn(ctx, "No boost messages configured!")

                        channels = []
                        for result in results:
                            ch = ctx.guild.get_channel(result[0])
                            if ch:
                                channels.append(ch.mention)

                        if not channels:
                            return await embed.warn(ctx, "No valid boost channels found!")

                        embed_obj = discord.Embed(
                            title="🚀 Boost Channels",
                            description="\n".join(f"• {ch}" for ch in channels),
                            color=0xff73fa
                        )
                        await ctx.reply(embed=embed_obj)

        except Exception as e:
            await embed.error(ctx, f"Failed to view boost messages: {e}")

    @booster.command(
        name="reset",
        description="Delete all boost messages"
    )
    @Perms.get_perms("manage_guild")
    async def booster_reset(self, ctx):
        """Delete all boost messages"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM boost_messages WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    affected_rows = cursor.rowcount

            if affected_rows == 0:
                return await embed.warn(ctx, "No boost messages to delete!")

            await embed.success(ctx, f"Deleted {affected_rows} boost message(s)")

        except Exception as e:
            await embed.error(ctx, f"Failed to reset boost messages: {e}")

    @booster.command(
        name="variables",
        description="Show available variables"
    )
    async def booster_variables(self, ctx):
        """Show available variables"""
        embed_obj = discord.Embed(
            title="🚀 Boost Variables & Embed Guide",
            description="Available variables and embed syntax for boost messages",
            color=0xff73fa
        )

        embed_obj.add_field(
            name="User Variables",
            value="`{user}` - Mention the user\n"
                  "`{user.mention}` - Always mention\n"
                  "`{user.name}` - Display name\n"
                  "`{user.id}` - User ID\n"
                  "`{user.avatar}` - Avatar URL",
            inline=True
        )

        embed_obj.add_field(
            name="Server Variables",
            value="`{server}` - Server name\n"
                  "`{guild.name}` - Same as server\n"
                  "`{count}` - Member count\n"
                  "`{server.id}` - Server ID\n"
                  "`{guild.icon}` - Server icon URL",
            inline=True
        )

        embed_obj.add_field(
            name="Embed Syntax",
            value="`{embed}` - Start custom embed\n"
                  "`$v` - Separator between parts\n"
                  "`{title: text}` - Embed title\n"
                  "`{description: text}` - Description\n"
                  "`{color: name/hex}` - Color\n"
                  "`{field: name && value}` - Field",
            inline=False
        )

        embed_obj.add_field(
            name="Color Examples",
            value="**Named colors:** `red`, `blue`, `green`, `purple`, `orange`, `pink`, `yellow`, `cyan`, `magenta`, `lime`, `navy`, `teal`, `silver`, `gold`\n"
                  "**Hex colors:** `#ff0000`, `#00ff00`, `#0000ff`\n"
                  "**Boost pink:** `#ff73fa` (recommended for boost messages)",
            inline=False
        )

        embed_obj.add_field(
            name="Examples",
            value="**Simple:** `Thanks {user} for boosting {server}! 🚀`\n"
                  "**Custom embed:** `{embed}$v{title: 🚀 BOOSTED!}$v{description: {user} boosted the server!}$v{color: #ff73fa}`",
            inline=False
        )

        await ctx.reply(embed=embed_obj)

    @booster.group(
        name="perks",
        description="Manage booster perks",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def booster_perks(self, ctx):
        """Manage booster perks"""
        embed_obj = discord.Embed(
            title="🎁 Booster Perks",
            description="Manage perks for server boosters",
            color=0xff73fa
        )

        embed_obj.add_field(
            name="Commands",
            value="`booster perks add role [role]` - Add role perk\n"
                  "`booster perks remove role [role]` - Remove role perk\n"
                  "`booster perks list` - List all perks",
            inline=False
        )

        await ctx.reply(embed=embed_obj)

    @booster_perks.command(
        name="add",
        description="Add booster perk",
        usage="[type] [value]"
    )
    @Perms.get_perms("manage_guild")
    async def perks_add(self, ctx, perk_type: str, *, value: str):
        """Add booster perk"""
        if perk_type.lower() == "role":
            try:
                role = await commands.RoleConverter().convert(ctx, value)
            except commands.BadArgument:
                return await embed.warn(ctx, "Invalid role!")

            if role >= ctx.guild.me.top_role:
                return await embed.warn(ctx, "That role is higher than my highest role!")

            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "INSERT INTO booster_perks (guild_id, perk_type, perk_value) VALUES (%s, %s, %s)",
                            (ctx.guild.id, "role", role.id)
                        )

                await embed.success(ctx, f"Added booster perk: {role.mention}")

            except Exception as e:
                await embed.error(ctx, f"Failed to add booster perk: {e}")
        else:
            await embed.warn(ctx, "Invalid perk type! Use: `role`")

    @booster_perks.command(
        name="remove",
        description="Remove booster perk",
        usage="[type] [value]"
    )
    @Perms.get_perms("manage_guild")
    async def perks_remove(self, ctx, perk_type: str, *, value: str):
        """Remove booster perk"""
        if perk_type.lower() == "role":
            try:
                role = await commands.RoleConverter().convert(ctx, value)
            except commands.BadArgument:
                return await embed.warn(ctx, "Invalid role!")

            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM booster_perks WHERE guild_id = %s AND perk_type = %s AND perk_value = %s",
                            (ctx.guild.id, "role", role.id)
                        )
                        affected_rows = cursor.rowcount

                if affected_rows == 0:
                    return await embed.warn(ctx, "Perk not found!")

                await embed.success(ctx, f"Removed booster perk: {role.mention}")

            except Exception as e:
                await embed.error(ctx, f"Failed to remove booster perk: {e}")
        else:
            await embed.warn(ctx, "Invalid perk type! Use: `role`")

    @booster_perks.command(
        name="list",
        description="List all booster perks"
    )
    @Perms.get_perms("manage_guild")
    async def perks_list(self, ctx):
        """List all booster perks"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM booster_perks WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    perks = await cursor.fetchall()

            if not perks:
                return await embed.warn(ctx, "No booster perks configured!")

            embed_obj = discord.Embed(
                title="🎁 Booster Perks",
                color=0xff73fa
            )

            role_perks = []
            for perk in perks:
                if perk[1] == 'role':  # perk_type field
                    role = ctx.guild.get_role(perk[2])  # perk_value field
                    if role:
                        role_perks.append(role.mention)

            if role_perks:
                embed_obj.add_field(
                    name="Role Perks",
                    value="\n".join(role_perks),
                    inline=False
                )
            else:
                embed_obj.description = "No valid role perks found."

            await ctx.reply(embed=embed_obj)

        except Exception as e:
            await embed.error(ctx, f"Failed to list booster perks: {e}")

    @booster.command(
        name="list",
        description="List all server boosters"
    )
    async def booster_list(self, ctx):
        """List all server boosters"""
        boosters = [member for member in ctx.guild.members if member.premium_since]

        if not boosters:
            return await embed.warn(ctx, "No server boosters found!")

        embed_obj = discord.Embed(
            title=f"🚀 Server Boosters ({len(boosters)})",
            color=0xff73fa
        )

        description = ""
        for i, booster in enumerate(boosters[:10], 1):
            description += f"{i}. {booster.mention} - <t:{int(booster.premium_since.timestamp())}:R>\n"

        if len(boosters) > 10:
            description += f"\n... and {len(boosters) - 10} more boosters"

        embed_obj.description = description
        embed_obj.set_footer(text=f"Boost Level: {ctx.guild.premium_tier} | Total Boosts: {ctx.guild.premium_subscription_count}")

        await ctx.reply(embed=embed_obj)

    @booster.command(
        name="info",
        description="Show server boost information"
    )
    async def booster_info(self, ctx):
        """Show server boost information"""
        embed_obj = discord.Embed(
            title="🚀 Server Boost Information",
            color=0xff73fa
        )

        embed_obj.add_field(
            name="Boost Level",
            value=f"Level {ctx.guild.premium_tier}",
            inline=True
        )

        embed_obj.add_field(
            name="Total Boosts",
            value=f"{ctx.guild.premium_subscription_count}",
            inline=True
        )

        boosters = [member for member in ctx.guild.members if member.premium_since]
        embed_obj.add_field(
            name="Boosters",
            value=f"{len(boosters)} members",
            inline=True
        )

        # Show boost perks
        perks_text = ""
        if ctx.guild.premium_tier >= 1:
            perks_text += "• 50 extra emoji slots\n• 128 Kbps audio quality\n• Custom server invite background\n• Animated server icon\n"
        if ctx.guild.premium_tier >= 2:
            perks_text += "• 150 extra emoji slots\n• 256 Kbps audio quality\n• Server banner\n• 50MB upload limit\n"
        if ctx.guild.premium_tier >= 3:
            perks_text += "• 250 extra emoji slots\n• 384 Kbps audio quality\n• Vanity URL\n• 100MB upload limit\n"

        if perks_text:
            embed_obj.add_field(
                name="Current Perks",
                value=perks_text,
                inline=False
            )

        await ctx.reply(embed=embed_obj)


async def setup(bot):
    await bot.add_cog(Booster(bot))
