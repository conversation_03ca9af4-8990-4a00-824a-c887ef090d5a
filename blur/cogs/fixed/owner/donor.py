import discord
import datetime
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from cogs.old.auth import owners


def is_bot_owner():
    """Check if user is a bot owner"""
    async def predicate(ctx: commands.Context):
        if ctx.author.id not in owners:
            await embed.error(ctx, "This command can only be used by **bot owners**")
            return False
        return True
    return commands.check(predicate)


class Donor(commands.Cog):
    """Donor management commands"""

    def __init__(self, bot):
        self.bot = bot

    @commands.group(invoke_without_command=True)
    @is_bot_owner()
    async def donor(self, ctx):
        if ctx.invoked_subcommand is None:
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='donor')

    @donor.command(name="add")
    @is_bot_owner()
    async def donor_add(self, ctx: commands.Context, user: discord.User):
        """Add a user as donor"""
        try:
            # Check if already donor
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                return await embed.warn(ctx, f"**{user}** is already a donor")

            # Add as donor
            timestamp = int(datetime.datetime.now().timestamp())
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO donor (user_id, added_at) VALUES (%s, %s)",
                        (user.id, timestamp)
                    )

            await embed.success(ctx, f"**{user}** is now a donor")

        except Exception as e:
            await embed.error(ctx, f"Error adding donor: {e}")

    @donor.command(name="remove")
    @is_bot_owner()
    async def donor_remove(self, ctx: commands.Context, user: discord.User):
        """Remove donor status from a user"""
        try:
            # Check if donor
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                return await embed.warn(ctx, f"**{user}** is not a donor")

            # Remove donor status
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM donor WHERE user_id = %s", (user.id,)
                    )

            await embed.success(ctx, f"**{user}** is no longer a donor")

        except Exception as e:
            await embed.error(ctx, f"Error removing donor: {e}")

    @donor.command(name="list")
    @is_bot_owner()
    async def donor_list(self, ctx):
        """List all donors"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM donor ORDER BY added_at DESC")
                    results = await cursor.fetchall()

            if not results:
                return await embed.warn(ctx, "No donors found")

            # Create paginated embeds
            embeds = []
            donors_per_page = 10
            
            for i in range(0, len(results), donors_per_page):
                page_results = results[i:i + donors_per_page]
                
                embed_obj = discord.Embed(
                    title=f"💎 Donors ({len(results)} total)",
                    color=Colors.default
                )
                
                description = ""
                for idx, (user_id, added_at) in enumerate(page_results, start=i + 1):
                    user = self.bot.get_user(user_id)
                    user_name = user.name if user else f"Unknown User"
                    
                    # Convert timestamp to readable date
                    date_added = datetime.datetime.fromtimestamp(added_at).strftime("%Y-%m-%d")
                    
                    description += f"`{idx}.` **{user_name}** (`{user_id}`)\n"
                    description += f"     Added: {date_added}\n\n"
                
                embed_obj.description = description
                embed_obj.set_footer(text=f"Page {len(embeds) + 1} of {(len(results) - 1) // donors_per_page + 1}")
                embeds.append(embed_obj)

            if len(embeds) == 1:
                await ctx.reply(embed=embeds[0])
            else:
                await ctx.paginator(embeds)

        except Exception as e:
            await embed.error(ctx, f"Error fetching donors: {e}")

    @donor.command(name="check")
    @is_bot_owner()
    async def donor_check(self, ctx: commands.Context, user: discord.User):
        """Check if a user is a donor"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                _, added_at = check
                date_added = datetime.datetime.fromtimestamp(added_at).strftime("%Y-%m-%d %H:%M:%S")
                await embed.success(ctx, f"**{user}** (`{user.id}`) is a donor\nAdded: {date_added}")
            else:
                await embed.warn(ctx, f"**{user}** (`{user.id}`) is not a donor")

        except Exception as e:
            await embed.error(ctx, f"Error checking donor status: {e}")


async def setup(bot):
    await bot.add_cog(Donor(bot))
