import discord
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from cogs.old.auth import owners


def is_bot_owner():
    """Check if user is a bot owner"""
    async def predicate(ctx: commands.Context):
        if ctx.author.id not in owners:
            await embed.error(ctx, "This command can only be used by **bot owners**")
            return False
        return True
    return commands.check(predicate)


class Blacklist(commands.Cog):
    """User blacklist management commands"""

    def __init__(self, bot):
        self.bot = bot

    @commands.group(invoke_without_command=True)
    @is_bot_owner()
    async def blacklist(self, ctx):
        if ctx.invoked_subcommand is None:
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='blacklist')

    @blacklist.command(name="add")
    @is_bot_owner()
    async def blacklist_add(self, ctx: commands.Context, user: discord.User, *, reason: str = "No reason provided"):
        """Add a user to the blacklist"""
        try:
            # Check if already blacklisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                return await embed.warn(ctx, f"**{user}** is already blacklisted")

            # Add to blacklist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO blacklist (user_id, reason) VALUES (%s, %s)",
                        (user.id, reason)
                    )

            await embed.success(ctx, f"Added **{user}** (`{user.id}`) to the blacklist\n**Reason:** {reason}")

        except Exception as e:
            await embed.error(ctx, f"Error adding user to blacklist: {e}")

    @blacklist.command(name="remove")
    @is_bot_owner()
    async def blacklist_remove(self, ctx: commands.Context, user: discord.User):
        """Remove a user from the blacklist"""
        try:
            # Check if blacklisted
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                return await embed.warn(ctx, f"**{user}** is not blacklisted")

            # Remove from blacklist
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM blacklist WHERE user_id = %s", (user.id,)
                    )

            await embed.success(ctx, f"Removed **{user}** (`{user.id}`) from the blacklist")

        except Exception as e:
            await embed.error(ctx, f"Error removing user from blacklist: {e}")

    @blacklist.command(name="list")
    @is_bot_owner()
    async def blacklist_list(self, ctx):
        """List all blacklisted users"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM blacklist")
                    results = await cursor.fetchall()

            if not results:
                return await embed.warn(ctx, "No users are blacklisted")

            # Create paginated embeds
            embeds = []
            users_per_page = 5
            
            for i in range(0, len(results), users_per_page):
                page_results = results[i:i + users_per_page]
                
                embed_obj = discord.Embed(
                    title=f"🚫 Blacklisted Users ({len(results)} total)",
                    color=Colors.default
                )
                
                description = ""
                for idx, (user_id, reason) in enumerate(page_results, start=i + 1):
                    user = self.bot.get_user(user_id)
                    user_name = user.name if user else "Unknown User"
                    
                    description += f"`{idx}.` **{user_name}** (`{user_id}`)\n"
                    description += f"     **Reason:** {reason}\n\n"
                
                embed_obj.description = description
                embed_obj.set_footer(text=f"Page {len(embeds) + 1} of {(len(results) - 1) // users_per_page + 1}")
                embeds.append(embed_obj)

            if len(embeds) == 1:
                await ctx.reply(embed=embeds[0])
            else:
                await ctx.paginator(embeds)

        except Exception as e:
            await embed.error(ctx, f"Error fetching blacklist: {e}")

    @blacklist.command(name="check")
    @is_bot_owner()
    async def blacklist_check(self, ctx: commands.Context, user: discord.User):
        """Check if a user is blacklisted"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (user.id,)
                    )
                    check = await cursor.fetchone()

            if check:
                _, reason = check
                await embed.warn(ctx, f"**{user}** (`{user.id}`) is blacklisted\n**Reason:** {reason}")
            else:
                await embed.success(ctx, f"**{user}** (`{user.id}`) is not blacklisted")

        except Exception as e:
            await embed.error(ctx, f"Error checking blacklist: {e}")


async def setup(bot):
    await bot.add_cog(Blacklist(bot))
