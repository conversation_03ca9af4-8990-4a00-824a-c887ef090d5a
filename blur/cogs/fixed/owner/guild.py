import discord
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from cogs.old.auth import owners


class Guild(commands.Cog):
    """Guild management commands"""
    
    def __init__(self, bot):
        self.bot = bot

    def is_bot_owner():
        """Check if user is a bot owner"""
        async def predicate(ctx: commands.Context):
            if ctx.author.id not in owners:
                await embed.error(ctx, "This command can only be used by **bot owners**")
                return False
            return True
        return commands.check(predicate)

    @commands.command(aliases=["servers"])
    @is_bot_owner()
    async def guilds(self, ctx: commands.Context):
        """List all guilds the bot is in"""
        def key(s):
            return s.member_count

        guilds_list = sorted(self.bot.guilds, reverse=True, key=key)
        
        if not guilds_list:
            return await embed.warn(ctx, "Bot is not in any guilds")

        # Create paginated embeds
        embeds = []
        guilds_per_page = 10
        
        for i in range(0, len(guilds_list), guilds_per_page):
            page_guilds = guilds_list[i:i + guilds_per_page]
            
            embed_obj = discord.Embed(
                title=f"🏰 Bot Guilds ({len(guilds_list)} total)",
                color=Colors.default
            )
            
            description = ""
            for idx, guild in enumerate(page_guilds, start=i + 1):
                description += f"`{idx}.` **{guild.name}** (`{guild.id}`)\n"
                description += f"     Members: {guild.member_count}\n\n"
            
            embed_obj.description = description
            embed_obj.set_footer(text=f"Page {len(embeds) + 1} of {(len(guilds_list) - 1) // guilds_per_page + 1}")
            embeds.append(embed_obj)

        if len(embeds) == 1:
            await ctx.reply(embed=embeds[0])
        else:
            await ctx.paginator(embeds)

    @commands.command(description="leave a guild", usage="[guild_id]", hidden=True)
    @is_bot_owner()
    async def leave(self, ctx, guild_id: int = None):
        """Leave a guild by ID"""
        if guild_id is None:
            return await embed.warn(ctx, "Please provide a guild ID to leave!")

        # Validate guild ID
        if guild_id <= 0:
            return await embed.error(ctx, "Invalid guild ID!")

        guild = self.bot.get_guild(guild_id)
        if not guild:
            return await embed.error(ctx, f"Guild with ID `{guild_id}` not found! Make sure the bot is in that server.")

        # Show guild info before leaving
        embed_obj = discord.Embed(
            title="⚠️ Confirm Guild Leave",
            description=f"Are you sure you want to leave **{guild.name}**?",
            color=0xffaa00
        )
        embed_obj.add_field(name="Guild ID", value=str(guild.id), inline=True)
        embed_obj.add_field(name="Members", value=str(guild.member_count), inline=True)
        embed_obj.add_field(name="Owner", value=str(guild.owner), inline=True)
        embed_obj.set_thumbnail(url=guild.icon.url if guild.icon else None)

        # Add confirmation buttons
        view = discord.ui.View(timeout=30)

        async def confirm_callback(interaction):
            if interaction.user.id != ctx.author.id:
                return await interaction.response.send_message("Only the command author can use this button!", ephemeral=True)

            try:
                guild_name = guild.name
                await guild.leave()

                success_embed = discord.Embed(
                    title="✅ Left Guild",
                    description=f"Successfully left **{guild_name}** (`{guild.id}`)",
                    color=0x00ff00
                )
                await interaction.response.edit_message(embed=success_embed, view=None)

            except discord.HTTPException as e:
                error_embed = discord.Embed(
                    title="❌ Failed to Leave Guild",
                    description=f"Failed to leave **{guild.name}**: {str(e)}",
                    color=0xff0000
                )
                await interaction.response.edit_message(embed=error_embed, view=None)
            except Exception as e:
                error_embed = discord.Embed(
                    title="❌ Unexpected Error",
                    description=f"An unexpected error occurred: {str(e)}",
                    color=0xff0000
                )
                await interaction.response.edit_message(embed=error_embed, view=None)

        async def cancel_callback(interaction):
            if interaction.user.id != ctx.author.id:
                return await interaction.response.send_message("Only the command author can use this button!", ephemeral=True)

            cancel_embed = discord.Embed(
                title="❌ Cancelled",
                description="Guild leave cancelled.",
                color=0x808080
            )
            await interaction.response.edit_message(embed=cancel_embed, view=None)

        confirm_button = discord.ui.Button(label="Confirm Leave", style=discord.ButtonStyle.danger, emoji="✅")
        cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary, emoji="❌")

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback

        view.add_item(confirm_button)
        view.add_item(cancel_button)

        await ctx.reply(embed=embed_obj, view=view)


async def setup(bot):
    await bot.add_cog(Guild(bot))
