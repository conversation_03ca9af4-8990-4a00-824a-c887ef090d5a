import discord
import datetime
import asyncio
import traceback
import textwrap
import io
import contextlib
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from cogs.old.auth import owners


class Admin(commands.Cog):
    """Bot administration commands"""
    
    def __init__(self, bot):
        self.bot = bot
        self._last_result = None

    def cleanup_code(self, content):
        """Automatically removes code blocks from the code."""
        # remove ```py\n```
        if content.startswith('```') and content.endswith('```'):
            return '\n'.join(content.split('\n')[1:-1])

        # remove `foo`
        return content.strip('` \n')

    @commands.command(
        name="cmdlogs",
        description="View recent command usage logs",
        usage="<limit>",
        hidden=True
    )
    @commands.is_owner()
    async def command_logs(self, ctx, limit: int = 10):
        """View recent command usage logs"""
        if limit > 50:
            limit = 50

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """SELECT command_name, user_id, guild_id, used_at
                           FROM command_usage
                           ORDER BY used_at DESC
                           LIMIT %s""",
                        (limit,)
                    )
                    results = await cursor.fetchall()

            if not results:
                return await embed.warn(ctx, "No command usage logs found")

            embed_obj = discord.Embed(
                title=f"📊 Recent Command Usage (Last {len(results)})",
                color=Colors.default,
                timestamp=datetime.datetime.utcnow()
            )

            log_text = ""
            for command_name, user_id, guild_id, used_at in results:
                user = self.bot.get_user(user_id)
                guild = self.bot.get_guild(guild_id)
                user_name = user.name if user else f"Unknown ({user_id})"
                guild_name = guild.name if guild else f"Unknown ({guild_id})"

                log_text += f"`{used_at}` - **{command_name}** by {user_name} in {guild_name}\n"

            embed_obj.description = log_text[:4000]  # Discord embed limit
            await ctx.reply(embed=embed_obj)

        except Exception as e:
            await embed.error(ctx, f"Error fetching command logs: {e}")

    @commands.command(
        name="eval",
        description="Evaluate Python code",
        usage="[code]",
        hidden=True
    )
    @commands.is_owner()
    async def eval_command(self, ctx, *, body: str):
        """Evaluate Python code"""
        env = {
            'bot': self.bot,
            'ctx': ctx,
            'channel': ctx.channel,
            'author': ctx.author,
            'guild': ctx.guild,
            'message': ctx.message,
            '_': self._last_result
        }

        env.update(globals())

        body = self.cleanup_code(body)
        stdout = io.StringIO()

        to_compile = f'async def func():\n{textwrap.indent(body, "  ")}'

        try:
            exec(to_compile, env)
        except Exception as e:
            return await ctx.send(f'```py\n{e.__class__.__name__}: {e}\n```')

        func = env['func']
        try:
            with contextlib.redirect_stdout(stdout):
                ret = await func()
        except Exception as e:
            value = stdout.getvalue()
            await ctx.send(f'```py\n{value}{traceback.format_exc()}\n```')
        else:
            value = stdout.getvalue()
            try:
                await ctx.message.add_reaction('✅')
            except:
                pass

            if ret is None:
                if value:
                    await ctx.send(f'```py\n{value}\n```')
            else:
                self._last_result = ret
                await ctx.send(f'```py\n{value}{ret}\n```')

    @commands.command(description="reload a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def reload(self, ctx, *, cog: str):
        """Reload a cog"""
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.reload_extension(cog_name)
            await embed.success(ctx, f"Successfully reloaded `{cog}`")
        except Exception as e:
            await embed.error(ctx, f"Failed to reload `{cog}`: {e}")

    @commands.command(description="load a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def load(self, ctx, *, cog: str):
        """Load a cog"""
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.load_extension(cog_name)
            await embed.success(ctx, f"Successfully loaded `{cog}`")
        except Exception as e:
            await embed.error(ctx, f"Failed to load `{cog}`: {e}")

    @commands.command(description="unload a cog", usage="[cog]", hidden=True)
    @commands.is_owner()
    async def unload(self, ctx, *, cog: str):
        """Unload a cog"""
        try:
            cog_name = f"cogs.{cog.lower()}"
            await self.bot.unload_extension(cog_name)
            await embed.success(ctx, f"Successfully unloaded `{cog}`")
        except Exception as e:
            await embed.error(ctx, f"Failed to unload `{cog}`: {e}")

    @commands.command(description="shutdown the bot", hidden=True)
    @commands.is_owner()
    async def shutdown(self, ctx):
        """Shutdown the bot"""
        embed_obj = discord.Embed(
            title="🔴 Shutting Down",
            description="Bot is shutting down...",
            color=0xff0000
        )
        await ctx.reply(embed=embed_obj)
        await self.bot.close()

    @commands.command(description="sync slash commands", hidden=True)
    @commands.is_owner()
    async def sync(self, ctx):
        """Sync slash commands"""
        try:
            synced = await self.bot.tree.sync()
            await embed.success(ctx, f"Synced {len(synced)} slash commands")
        except Exception as e:
            await embed.error(ctx, f"Failed to sync commands: {e}")

    @commands.command(description="execute command as another user", usage="[user] [command]", hidden=True)
    @commands.is_owner()
    async def sudo(self, ctx, user: discord.Member, *, command: str):
        """Execute a command as another user"""
        # Create a fake context
        fake_message = ctx.message
        fake_message.author = user
        fake_message.content = ctx.prefix + command

        new_ctx = await self.bot.get_context(fake_message)

        if new_ctx.command is None:
            return await embed.error(ctx, "Command not found!")

        try:
            await new_ctx.command.invoke(new_ctx)
            await ctx.message.add_reaction('✅')
        except Exception as e:
            await embed.error(ctx, f"Error executing command: {e}")


async def setup(bot):
    await bot.add_cog(Admin(bot))
