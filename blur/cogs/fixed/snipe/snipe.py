import discord
from discord.ext import commands
import datetime
from tools.ext import embed
from config.constants import Colors


class Snipe(commands.Cog):
    """Message snipe events for tracking deleted/edited messages"""

    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_message_delete(self, message):
        """Track deleted messages for snipe command"""
        if message.author.bot:
            return

        if not message.content:
            return

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Remove oldest messages if we have 5 or more
                    await cursor.execute(
                        """DELETE FROM snipe_messages
                           WHERE channel_id = %s AND id NOT IN (
                               SELECT id FROM (
                                   SELECT id FROM snipe_messages
                                   WHERE channel_id = %s
                                   ORDER BY deleted_at DESC
                                   LIMIT 4
                               ) AS recent
                           )""",
                        (message.channel.id, message.channel.id)
                    )

                    # Store new deleted message
                    await cursor.execute(
                        """INSERT INTO snipe_messages (channel_id, author_id, content, deleted_at)
                           VALUES (%s, %s, %s, %s)""",
                        (message.channel.id, message.author.id, message.content, datetime.datetime.now(datetime.timezone.utc))
                    )
        except Exception as e:
            print(f"Snipe storage error: {e}")

    @commands.Cog.listener()
    async def on_message_edit(self, before, after):
        """Track edited messages for editsnipe command"""
        if before.author.bot:
            return

        if not before.content or not after.content:
            return

        if before.content == after.content:
            return

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Remove oldest edit messages if we have 5 or more
                    await cursor.execute(
                        """DELETE FROM editsnipe_messages
                           WHERE channel_id = %s AND id NOT IN (
                               SELECT id FROM (
                                   SELECT id FROM editsnipe_messages
                                   WHERE channel_id = %s
                                   ORDER BY edited_at DESC
                                   LIMIT 4
                               ) AS recent
                           )""",
                        (before.channel.id, before.channel.id)
                    )

                    # Store new edited message with message ID for replying
                    await cursor.execute(
                        """INSERT INTO editsnipe_messages (channel_id, message_id, author_id, before_content, after_content, edited_at)
                           VALUES (%s, %s, %s, %s, %s, %s)""",
                        (before.channel.id, after.id, before.author.id, before.content, after.content, datetime.datetime.now(datetime.timezone.utc))
                    )
        except Exception as e:
            print(f"Edit snipe storage error: {e}")

    @commands.command(name="snipe", aliases=["s"], description="Show deleted messages in this channel")
    async def snipe(self, ctx, index: int = 1):
        """Show deleted messages with index support (,s 1, ,s 2, etc.)"""
        if index < 1:
            return await ctx.send("Index must be 1 or higher!")

        if index > 5:
            return await ctx.send("Only the last 5 deleted messages are stored!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Get total count of messages for this channel
                    await cursor.execute(
                        "SELECT COUNT(*) FROM snipe_messages WHERE channel_id = %s",
                        (ctx.channel.id,)
                    )
                    total_count = (await cursor.fetchone())[0]

                    # Get the specific message
                    await cursor.execute(
                        """SELECT author_id, content, deleted_at
                           FROM snipe_messages
                           WHERE channel_id = %s
                           ORDER BY deleted_at DESC
                           LIMIT %s OFFSET %s""",
                        (ctx.channel.id, 1, index - 1)
                    )
                    result = await cursor.fetchone()

            if not result:
                if index == 1:
                    return await ctx.send("No deleted messages found in this channel!")
                else:
                    return await ctx.send(f"No deleted message found at index {index}!")

            author_id, content, deleted_at = result
            author = self.bot.get_user(author_id) or f"Unknown User ({author_id})"

            # Format content in code block
            formatted_content = f"{content}"

            # Ensure deleted_at is a datetime object with robust error handling
            if isinstance(deleted_at, str):
                try:
                    # Try different timestamp formats
                    if deleted_at.endswith('Z'):
                        deleted_at = datetime.datetime.fromisoformat(deleted_at.replace('Z', '+00:00'))
                    elif '+' in deleted_at or deleted_at.endswith('UTC'):
                        deleted_at = datetime.datetime.fromisoformat(deleted_at.replace('UTC', '').strip())
                    else:
                        # Try parsing as ISO format
                        deleted_at = datetime.datetime.fromisoformat(deleted_at)
                except (ValueError, TypeError):
                    # If all parsing fails, use current time
                    deleted_at = datetime.datetime.now(datetime.timezone.utc)
            elif not isinstance(deleted_at, datetime.datetime):
                deleted_at = datetime.datetime.now(datetime.timezone.utc)

            # Create embed with safe timestamp handling
            try:
                embed = discord.Embed(
                    description=formatted_content,
                    color=Colors.default,
                    timestamp=deleted_at
                )
            except (TypeError, ValueError):
                # If timestamp still causes issues, create embed without it
                embed = discord.Embed(
                    description=formatted_content,
                    color=Colors.default
                )
            embed.set_author(name=str(author), icon_url=author.display_avatar.url if hasattr(author, 'display_avatar') else '')
            embed.set_footer(text=f"{index}/{total_count} message")

            await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"Error retrieving snipe: {e}")

    @commands.command(name="editsnipe", aliases=["esnipe", "es"], description="Show edited messages in this channel")
    async def editsnipe(self, ctx, index: int = 1):
        
        if index < 1:
            return await ctx.send("Index must be 1 or higher!")

        if index > 5:
            return await ctx.send("Only the last 5 edited messages are stored!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Get total count of edit messages for this channel
                    await cursor.execute(
                        "SELECT COUNT(*) FROM editsnipe_messages WHERE channel_id = %s",
                        (ctx.channel.id,)
                    )
                    total_count = (await cursor.fetchone())[0]

                    # Get the specific edit message
                    await cursor.execute(
                        """SELECT message_id, author_id, before_content, after_content, edited_at
                           FROM editsnipe_messages
                           WHERE channel_id = %s
                           ORDER BY edited_at DESC
                           LIMIT %s OFFSET %s""",
                        (ctx.channel.id, 1, index - 1)
                    )
                    result = await cursor.fetchone()

            if not result:
                if index == 1:
                    return await ctx.send("No edited messages found in this channel!")
                else:
                    return await ctx.send(f"No edited message found at index {index}!")

            message_id, author_id, before_content, _, edited_at = result
            author = self.bot.get_user(author_id) or f"Unknown User ({author_id})"

            formatted_before = f"{before_content}"

            # Ensure edited_at is a datetime object with robust error handling
            if isinstance(edited_at, str):
                try:
                    # Try different timestamp formats
                    if edited_at.endswith('Z'):
                        edited_at = datetime.datetime.fromisoformat(edited_at.replace('Z', '+00:00'))
                    elif '+' in edited_at or edited_at.endswith('UTC'):
                        edited_at = datetime.datetime.fromisoformat(edited_at.replace('UTC', '').strip())
                    else:
                        # Try parsing as ISO format
                        edited_at = datetime.datetime.fromisoformat(edited_at)
                except (ValueError, TypeError):
                    # If all parsing fails, use current time
                    edited_at = datetime.datetime.now(datetime.timezone.utc)
            elif not isinstance(edited_at, datetime.datetime):
                edited_at = datetime.datetime.now(datetime.timezone.utc)

            # Create embed with safe timestamp handling
            try:
                embed = discord.Embed(
                    description=formatted_before,
                    color=Colors.warn,
                    timestamp=edited_at
                )
            except (TypeError, ValueError):
                # If timestamp still causes issues, create embed without it
                embed = discord.Embed(
                    description=formatted_before,
                    color=Colors.warn
                )
            embed.set_author(name=str(author), icon_url=author.display_avatar.url if hasattr(author, 'display_avatar') else '')
            embed.set_footer(text=f"{index}/{total_count} message")

            # Try to reply to the original message if it still exists
            try:
                original_message = await ctx.channel.fetch_message(message_id)
                await original_message.reply(embed=embed)
            except (discord.NotFound, discord.HTTPException):
                # If original message is not found, just send normally
                await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"Error retrieving edit snipe: {e}")

    @commands.command(name="clearsnipe", aliases=["cs"], description="Clear all snipe messages in this channel")
    @commands.has_permissions(manage_messages=True)
    async def clearsnipe(self, ctx):
        """Clear all snipe messages for the current channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Clear deleted messages
                    await cursor.execute(
                        "DELETE FROM snipe_messages WHERE channel_id = %s",
                        (ctx.channel.id,)
                    )
                    deleted_count = cursor.rowcount

                    # Clear edited messages
                    await cursor.execute(
                        "DELETE FROM editsnipe_messages WHERE channel_id = %s",
                        (ctx.channel.id,)
                    )
                    edited_count = cursor.rowcount

                    total_cleared = deleted_count + edited_count

                    if total_cleared == 0:
                        await ctx.send_warning("No snipe messages found to clear in this channel!")
                    else:
                        await ctx.send_success(f"{ctx.author.mention}: Cleared **all snipes** from this channel")

        except Exception as e:
            await ctx.send(f"Error clearing snipe messages: {e}")

    @commands.command(name="resetsnipe", description="Reset snipe database tables (Owner only)")
    @commands.is_owner()
    async def resetsnipe(self, ctx):
        """Reset snipe database tables to fix any schema issues"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Drop and recreate tables with correct schema
                    await cursor.execute("DROP TABLE IF EXISTS snipe_messages")
                    await cursor.execute("DROP TABLE IF EXISTS editsnipe_messages")

                    # Create new tables with proper structure
                    await cursor.execute("""CREATE TABLE snipe_messages (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        channel_id BIGINT,
                        author_id BIGINT,
                        content TEXT,
                        deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_channel_deleted (channel_id, deleted_at)
                    )""")

                    await cursor.execute("""CREATE TABLE editsnipe_messages (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        channel_id BIGINT,
                        message_id BIGINT,
                        author_id BIGINT,
                        before_content TEXT,
                        after_content TEXT,
                        edited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_channel_edited (channel_id, edited_at)
                    )""")

                    await ctx.send_success(f"{ctx.author.mention}: Reset snipe database tables successfully!")

        except Exception as e:
            await ctx.send(f"Error resetting snipe tables: {e}")


async def setup(bot):
    await bot.add_cog(Snipe(bot))
