import discord
from discord.ext import commands
from tools.checks import Perms
from tools.ext import embed
from config.constants import Emojis
import aiohttp
import re
import io


class Steal(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def extract_emojis_from_content(self, content):
        """Extract all custom emojis from text content"""
        emoji_pattern = r'<(a?):([a-zA-Z0-9_]+):(\d+)>'
        return re.findall(emoji_pattern, content)

    async def steal_single_emoji(self, ctx, emoji_name, emoji_id, animated=False):
        """Steal a single emoji"""
        # Validate name
        if not re.match(r'^[a-zA-Z0-9_]+$', emoji_name):
            emoji_name = re.sub(r'[^a-zA-Z0-9_]', '_', emoji_name)
        
        if len(emoji_name) < 2:
            emoji_name = f"emoji_{emoji_id}"
        elif len(emoji_name) > 32:
            emoji_name = emoji_name[:32]
        
        # Check emoji limits based on boost level
        if ctx.guild.premium_tier == 0:
            emoji_limit = 50  # No boost
        elif ctx.guild.premium_tier == 1:
            emoji_limit = 100  # Level 1 boost
        elif ctx.guild.premium_tier == 2:
            emoji_limit = 150  # Level 2 boost
        elif ctx.guild.premium_tier >= 3:
            emoji_limit = 250  # Level 3 boost
        
        current_emojis = len([e for e in ctx.guild.emojis if e.animated == animated])
        
        if current_emojis >= emoji_limit:
            return None, f"Server has reached the limit for {'animated' if animated else 'static'} emojis ({emoji_limit})!"
        
        # Download emoji
        emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.{'gif' if animated else 'png'}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(emoji_url) as resp:
                    if resp.status != 200:
                        return None, "Failed to download emoji!"
                    
                    emoji_data = await resp.read()
                    
                    if len(emoji_data) > 256000:  # 256KB limit
                        return None, "Emoji file is too large!"
        except Exception as e:
            return None, f"Failed to download emoji: {e}"
        
        # Create emoji
        try:
            new_emoji = await ctx.guild.create_custom_emoji(
                name=emoji_name,
                image=emoji_data,
                reason=f"Stolen by {ctx.author}"
            )
            return new_emoji, None
            
        except discord.HTTPException as e:
            if "Maximum number of emojis reached" in str(e):
                return None, "Server has reached the emoji limit!"
            elif "Invalid image" in str(e):
                return None, "Invalid emoji image!"
            else:
                return None, f"Failed to create emoji: {e}"
        except Exception as e:
            return None, f"Error creating emoji: {e}"

    async def steal_sticker(self, ctx, sticker):
        """Steal a sticker"""
        # Validate name
        final_name = sticker.name
        if len(final_name) < 2 or len(final_name) > 30:
            final_name = f"sticker_{sticker.id}"
        
        # Check sticker limits
        sticker_limit = 5
        if ctx.guild.premium_tier >= 1:
            sticker_limit = 15
        elif ctx.guild.premium_tier >= 2:
            sticker_limit = 30
        elif ctx.guild.premium_tier >= 3:
            sticker_limit = 60
        
        if len(ctx.guild.stickers) >= sticker_limit:
            return None, f"Server has reached the sticker limit ({sticker_limit})!"
        
        # Download sticker
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(sticker.url) as resp:
                    if resp.status != 200:
                        return None, "Failed to download sticker!"
                    
                    sticker_data = await resp.read()
                    
                    if len(sticker_data) > 512000:  # 512KB limit
                        return None, "Sticker file is too large!"
        except Exception as e:
            return None, f"Failed to download sticker: {e}"
        
        # Create sticker
        try:
            new_sticker = await ctx.guild.create_sticker(
                name=final_name,
                description=sticker.description or "Stolen sticker",
                emoji="😀",  # Required emoji
                file=discord.File(io.BytesIO(sticker_data), filename=f"{final_name}.png"),
                reason=f"Stolen by {ctx.author}"
            )
            return new_sticker, None
            
        except discord.HTTPException as e:
            if "Maximum number of stickers reached" in str(e):
                return None, "Server has reached the sticker limit!"
            else:
                return None, f"Failed to create sticker: {e}"
        except Exception as e:
            return None, f"Error creating sticker: {e}"

    @commands.command(
        name="steal",
        description="Steal emojis and stickers from messages",
        usage="Reply to a message with emojis/stickers"
    )
    @Perms.get_perms("manage_emojis")
    async def steal(self, ctx):
        """Steal emojis and stickers from replied message"""
        if not ctx.message.reference:
            await embed.warn(ctx, "Please reply to a message containing emojis or stickers!")
            return

        try:
            replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
        except:
            await embed.error(ctx, "Could not fetch the replied message!")
            return

        # Send initial processing message
        processing_msg = await embed.loading(ctx, "Scanning message for emojis and stickers...")
        
        # Collect all emojis and stickers
        emojis_to_steal = []
        stickers_to_steal = []
        
        # Extract emojis from message content using improved regex
        if replied_message.content:
            # Find all custom emojis (both animated and static)
            matches = await self.extract_emojis_from_content(replied_message.content)
            
            for animated_flag, emoji_name, emoji_id in matches:
                animated = bool(animated_flag)  # 'a' for animated, '' for static
                emojis_to_steal.append((emoji_name, emoji_id, animated))
        
        # Extract emojis from embeds
        for embed in replied_message.embeds:
            embed_content = ""
            
            # Combine all embed text content
            if embed.title:
                embed_content += embed.title + " "
            if embed.description:
                embed_content += embed.description + " "
            if embed.footer and embed.footer.text:
                embed_content += embed.footer.text + " "
            if embed.author and embed.author.name:
                embed_content += embed.author.name + " "
            
            # Include ALL fields (this was missing the field content!)
            for field in embed.fields:
                if field.name:
                    embed_content += field.name + " "
                if field.value:
                    embed_content += field.value + " "
            
            # Extract emojis from combined embed content
            if embed_content:
                matches = await self.extract_emojis_from_content(embed_content)
                
                for animated_flag, emoji_name, emoji_id in matches:
                    animated = bool(animated_flag)
                    emojis_to_steal.append((emoji_name, emoji_id, animated))
        
        # Extract stickers
        for sticker in replied_message.stickers:
            stickers_to_steal.append(sticker)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_emojis = []
        for emoji in emojis_to_steal:
            if emoji not in seen:
                seen.add(emoji)
                unique_emojis.append(emoji)
        emojis_to_steal = unique_emojis
        
        if not emojis_to_steal and not stickers_to_steal:
            await embed.error(ctx, "No emojis or stickers found in the replied message!")
            await processing_msg.delete()
            return

        # Update processing message
        loading_embed = discord.Embed(
            color=0x2f3136,
            description=f"{Emojis.loading} Found {len(emojis_to_steal)} emojis and {len(stickers_to_steal)} stickers. Starting to steal..."
        )
        await processing_msg.edit(embed=loading_embed)
        
        # Start stealing
        stolen_emojis = []
        stolen_stickers = []
        failed_items = []
        
        # Steal emojis
        for i, (emoji_name, emoji_id, animated) in enumerate(emojis_to_steal, 1):
            loading_embed = discord.Embed(
                color=0x2f3136,
                description=f"{Emojis.loading} Stealing emoji {i}/{len(emojis_to_steal)}: {emoji_name}..."
            )
            await processing_msg.edit(embed=loading_embed)

            emoji, error = await self.steal_single_emoji(ctx, emoji_name, emoji_id, animated)
            if emoji:
                stolen_emojis.append(emoji)
            else:
                failed_items.append(f"Emoji `{emoji_name}`: {error}")

        # Steal stickers
        for i, sticker in enumerate(stickers_to_steal, 1):
            loading_embed = discord.Embed(
                color=0x2f3136,
                description=f"{Emojis.loading} Stealing sticker {i}/{len(stickers_to_steal)}: {sticker.name}..."
            )
            await processing_msg.edit(embed=loading_embed)

            new_sticker, error = await self.steal_sticker(ctx, sticker)
            if new_sticker:
                stolen_stickers.append(new_sticker)
            else:
                failed_items.append(f"Sticker `{sticker.name}`: {error}")

        # Create response
        if not stolen_emojis and not stolen_stickers:
            error_msg = f"Failed to steal any emojis or stickers!\n\n**Errors:**\n" + "\n".join(failed_items[:5])
            await embed.error(ctx, error_msg)
            await processing_msg.delete()
            return

        # Success message
        result_text = f"Successfully stole "

        if stolen_emojis and stolen_stickers:
            result_text += f"{len(stolen_emojis)} emojis and {len(stolen_stickers)} stickers!"
        elif stolen_emojis:
            result_text += f"{len(stolen_emojis)} emojis!"
        elif stolen_stickers:
            result_text += f"{len(stolen_stickers)} stickers!"

        if failed_items:
            result_text += f" ({len(failed_items)} failed)"

        if failed_items:
            await embed.warn(ctx, result_text)
        else:
            await embed.success(ctx, result_text)

        await processing_msg.delete()

    @commands.command(
        name="emojilimits",
        description="Check current emoji usage and limits",
        aliases=["elimit"]
    )
    @Perms.get_perms("manage_emojis")
    async def emoji_limits(self, ctx):
        """Check emoji limits and current usage"""
        # Calculate limits based on boost level
        if ctx.guild.premium_tier == 0:
            emoji_limit = 50  # No boost
        elif ctx.guild.premium_tier == 1:
            emoji_limit = 100  # Level 1 boost
        elif ctx.guild.premium_tier == 2:
            emoji_limit = 150  # Level 2 boost
        elif ctx.guild.premium_tier >= 3:
            emoji_limit = 250  # Level 3 boost
        
        # Count current emojis
        static_emojis = [e for e in ctx.guild.emojis if not e.animated]
        animated_emojis = [e for e in ctx.guild.emojis if e.animated]
        
        result_text = f"{Emojis.info} {ctx.author.mention}: **Emoji Usage & Limits**\n\n"
        result_text += f"**Boost Level:** {ctx.guild.premium_tier}\n"
        result_text += f"**Emoji Limit:** {emoji_limit} each type\n\n"
        result_text += f"**Static Emojis:** {len(static_emojis)}/{emoji_limit} ({emoji_limit - len(static_emojis)} available)\n"
        result_text += f"**Animated Emojis:** {len(animated_emojis)}/{emoji_limit} ({emoji_limit - len(animated_emojis)} available)"
        
        if len(static_emojis) >= emoji_limit:
            result_text += f"\n\n{Emojis.warn} Static emoji limit reached!"
        
        if len(animated_emojis) >= emoji_limit:
            result_text += f"\n\n{Emojis.warn} Animated emoji limit reached!"
        
        await embed.default(ctx, result_text)


async def setup(bot):
    await bot.add_cog(Steal(bot))
