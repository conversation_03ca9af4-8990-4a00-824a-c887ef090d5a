import discord
from discord.ext import commands
from tools.checks import Perms, Donor
from tools.ext import embed
from config.constants import Colors, Emojis


class Prefix(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.group(
        name="prefix",
        description="Manage server prefix",
        invoke_without_command=True
    )
    async def prefix(self, ctx):
        """Show current server prefix (no permissions required)"""
        if ctx.invoked_subcommand is None:
            # Invoke help for prefix command
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='prefix')
            else:
                # Fallback if help command not found - show current prefix
                try:
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM prefixes WHERE guild_id = %s",
                                (ctx.guild.id,)
                            )
                            result = await cursor.fetchone()

                    current_prefix = result[0] if result else ","

                    # Check if user has selfprefix
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                                (ctx.author.id,)
                            )
                            selfprefix_result = await cursor.fetchone()

                    description = f"Server prefix: `{current_prefix}`"
                    if selfprefix_result:
                        description += f"\nYour personal prefix: `{selfprefix_result[0]}`"
                        description += f"\n\nYou can use both prefixes!"

                    embed_obj = discord.Embed(
                        description=f"{Emojis.success} {ctx.author.mention}: {description}",
                        color=Colors.success
                    )
                    await ctx.send(embed=embed_obj)

                except Exception as e:
                    await embed.error(ctx, f"Failed to get prefix: {e}")

    @prefix.command(
        name="set",
        description="Set server prefix",
        usage="[new_prefix]"
    )
    @Perms.get_perms("manage_guild")
    async def prefix_set(self, ctx, prefix: str = None):
        """Set server prefix (requires manage_guild permission)"""
        if prefix is None:
            return await embed.warn(ctx, "Please provide a prefix to set!")

        # Reset to default if "reset" or "default"
        if prefix.lower() in ["reset", "default"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )

                await embed.success(ctx, "Prefix reset to default: `,`")

            except Exception as e:
                await embed.error(ctx, f"Failed to reset prefix: {e}")
            return

        if len(prefix) > 5:
            return await embed.error(ctx, "Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO prefixes (guild_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.guild.id, prefix, prefix)
                    )

            await embed.success(ctx, f"Server prefix set to `{prefix}`")

        except Exception as e:
            await embed.error(ctx, f"Failed to set prefix: {e}")

    @commands.group(
        name="selfprefix",
        description="Manage your personal prefix (donor only)",
        aliases=["sp"],
        invoke_without_command=True
    )
    @Donor.check_donor()
    async def selfprefix(self, ctx):
        """Manage your personal prefix (donor only)"""
        if ctx.invoked_subcommand is None:
            # Show current selfprefix
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                            (ctx.author.id,)
                        )
                        result = await cursor.fetchone()

                if result:
                    embed = discord.Embed(
                        description=f"{Emojis.success} {ctx.author.mention}: Your personal prefix: `{result[0]}`",
                        color=Colors.success
                    )
                    await ctx.send(embed=embed)
                else:
                    # Get server prefix
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT prefix FROM prefixes WHERE guild_id = %s",
                                (ctx.guild.id,)
                            )
                            server_result = await cursor.fetchone()

                    server_prefix = server_result[0] if server_result else ","
                    embed = discord.Embed(
                        description=f"{Emojis.warn} {ctx.author.mention}: You don't have a personal prefix set. Using server prefix: `{server_prefix}`",
                        color=Colors.warn
                    )
                    await ctx.send(embed=embed)

            except Exception as e:
                await embed.error(ctx, f"Failed to get selfprefix: {e}")

    @selfprefix.command(
        name="set",
        description="Set your personal prefix (donor only)",
        usage="[new_prefix]"
    )
    @Donor.check_donor()
    async def selfprefix_set(self, ctx, prefix: str = None):
        """Set your personal prefix (donor only)"""
        if prefix is None:
            return await embed.warn(ctx, "Please provide a prefix to set!")

        # Reset to default if "reset" or "default"
        if prefix.lower() in ["reset", "default"]:
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "DELETE FROM selfprefixes WHERE user_id = %s",
                            (ctx.author.id,)
                        )

                # Get server prefix
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT prefix FROM prefixes WHERE guild_id = %s",
                            (ctx.guild.id,)
                        )
                        server_result = await cursor.fetchone()

                server_prefix = server_result[0] if server_result else ","
                await embed.success(ctx, f"Personal prefix reset. Now using server prefix: `{server_prefix}`")

            except Exception as e:
                await embed.error(ctx, f"Failed to reset selfprefix: {e}")
            return

        if len(prefix) > 5:
            return await embed.error(ctx, "Prefix cannot be longer than 5 characters!")

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO selfprefixes (user_id, prefix)
                           VALUES (%s, %s)
                           ON DUPLICATE KEY UPDATE prefix = %s""",
                        (ctx.author.id, prefix, prefix)
                    )

            await embed.success(ctx, f"Your personal prefix set to `{prefix}`")

        except Exception as e:
            await embed.error(ctx, f"Failed to set selfprefix: {e}")

    @commands.command(
        name="testembed",
        description="Test embed system with user mentions",
        hidden=True
    )
    async def test_embed(self, ctx):
        """Test different embed methods to verify user mentions work"""
        # Test embed.success
        await embed.success(ctx, "This is a success message using embed.success")

        # Test ctx.send_success
        await ctx.send_success("This is a success message using ctx.send_success")

        # Test embed.error
        await embed.error(ctx, "This is an error message using embed.error")

        # Test ctx.send_error
        await ctx.send_error("This is an error message using ctx.send_error")

        # Test embed.warn
        await embed.warn(ctx, "This is a warning message using embed.warn")

        # Test ctx.send_warning
        await ctx.send_warning("This is a warning message using ctx.send_warning")


async def setup(bot):
    await bot.add_cog(Prefix(bot))
