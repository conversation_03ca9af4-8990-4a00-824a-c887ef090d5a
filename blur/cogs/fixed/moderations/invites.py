import discord
from discord.ext import commands
from datetime import datetime
from tools.checks import Perms


class InviteTracking(commands.Cog):
    """Comprehensive invite tracking and management system"""

    def __init__(self, bot):
        self.bot = bot
        self.invite_cache = {}
    
    async def send_log(self, guild, embed):
        """Send log to invite logging channel"""
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id FROM logging_config WHERE guild_id = %s AND log_type = %s",
                        (guild.id, "invite")
                    )
                    result = await cursor.fetchone()
                    
            if result:
                channel = guild.get_channel(result[0])
                if channel:
                    await channel.send(embed=embed)
        except Exception as e:
            print(f"Invite logging error: {e}")
    
    async def update_invite_cache(self, guild):
        """Update invite cache for guild"""
        try:
            invites = await guild.invites()
            self.invite_cache[guild.id] = {invite.code: invite.uses for invite in invites}
        except:
            self.invite_cache[guild.id] = {}
    
    async def get_invite_info(self, guild):
        """Get invite that was used"""
        try:
            current_invites = await guild.invites()
            current_uses = {invite.code: invite.uses for invite in current_invites}
            
            if guild.id not in self.invite_cache:
                await self.update_invite_cache(guild)
                return None, None
            
            cached_uses = self.invite_cache[guild.id]
            
            for code, uses in current_uses.items():
                if code in cached_uses and uses > cached_uses[code]:
                    # Found the invite that was used
                    invite = discord.utils.get(current_invites, code=code)
                    self.invite_cache[guild.id] = current_uses
                    return invite, uses
            
            # Update cache
            self.invite_cache[guild.id] = current_uses
            return None, None
            
        except:
            return None, None
    
    @commands.Cog.listener()
    async def on_ready(self):
        """Cache invites on bot ready"""
        for guild in self.bot.guilds:
            await self.update_invite_cache(guild)
    
    @commands.Cog.listener()
    async def on_guild_join(self, guild):
        """Cache invites when bot joins guild"""
        await self.update_invite_cache(guild)
    
    @commands.Cog.listener()
    async def on_invite_create(self, invite):
        """Update cache when invite is created"""
        if invite.guild.id not in self.invite_cache:
            self.invite_cache[invite.guild.id] = {}
        self.invite_cache[invite.guild.id][invite.code] = invite.uses
    
    @commands.Cog.listener()
    async def on_invite_delete(self, invite):
        """Update cache when invite is deleted"""
        if invite.guild.id in self.invite_cache:
            self.invite_cache[invite.guild.id].pop(invite.code, None)
    
    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Log member joins with invite tracking"""
        if member.bot:
            return

        invite, total_uses = await self.get_invite_info(member.guild)

        embed = discord.Embed(
            title="📥 Member Joined",
            color=0x00ff00,
            timestamp=datetime.now()
        )

        embed.add_field(
            name="Member",
            value=f"{member.mention} ({member})",
            inline=True
        )

        embed.add_field(
            name="Account Created",
            value=f"<t:{int(member.created_at.timestamp())}:R>",
            inline=True
        )

        embed.add_field(
            name="Member Count",
            value=f"{member.guild.member_count}",
            inline=True
        )

        if invite and invite.inviter:
            embed.add_field(
                name="Invited By",
                value=f"{invite.inviter.mention} ({invite.inviter})",
                inline=True
            )

            embed.add_field(
                name="Invite Code",
                value=f"`{invite.code}`",
                inline=True
            )

            embed.add_field(
                name="Inviter Total",
                value=f"{total_uses}",
                inline=True
            )

            # Update invite tracking
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO invite_tracking (guild_id, invite_code, inviter_id, uses_count)
                           VALUES (%s, %s, %s, %s)
                           ON DUPLICATE KEY UPDATE uses_count = %s""",
                        (member.guild.id, invite.code, invite.inviter.id, total_uses, total_uses)
                    )

            # Update invite stats
            await self.update_invite_stats(member.guild.id, invite.inviter.id, "join", 1)

            # Store who invited this member for future reference
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO member_invites (guild_id, user_id, inviter_id, invite_code, joined_at)
                           VALUES (%s, %s, %s, %s, %s)
                           ON DUPLICATE KEY UPDATE
                           inviter_id = %s, invite_code = %s, joined_at = %s""",
                        (member.guild.id, member.id, invite.inviter.id, invite.code, datetime.now(),
                         invite.inviter.id, invite.code, datetime.now())
                    )
        else:
            embed.add_field(
                name="Invited By",
                value="Unknown (Vanity URL or Widget)",
                inline=True
            )

        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text=f"User ID: {member.id}")

        await self.send_log(member.guild, embed)
    
    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Log member leaves and update invite stats"""
        if member.bot:
            return

        embed = discord.Embed(
            title="📤 Member Left",
            color=0xff0000,
            timestamp=datetime.now()
        )

        embed.add_field(
            name="Member",
            value=f"{member} ({member.id})",
            inline=True
        )

        embed.add_field(
            name="Joined",
            value=f"<t:{int(member.joined_at.timestamp())}:R>" if member.joined_at else "Unknown",
            inline=True
        )

        embed.add_field(
            name="Member Count",
            value=f"{member.guild.member_count}",
            inline=True
        )

        # Find who invited them and update stats
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT inviter_id, invite_code FROM member_invites WHERE guild_id = %s AND user_id = %s",
                    (member.guild.id, member.id)
                )
                result = await cursor.fetchone()

        if result:
            inviter_id, invite_code = result
            inviter = member.guild.get_member(inviter_id)

            embed.add_field(
                name="Invited By",
                value=f"<@{inviter_id}>" + (f" ({inviter})" if inviter else " (Left server)"),
                inline=True
            )

            embed.add_field(
                name="Invite Code",
                value=f"`{invite_code}`",
                inline=True
            )

            # Update inviter's left invites count
            await self.update_invite_stats(member.guild.id, inviter_id, "leave", 1)

            # Remove from member_invites table
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM member_invites WHERE guild_id = %s AND user_id = %s",
                        (member.guild.id, member.id)
                    )
        else:
            embed.add_field(
                name="Invited By",
                value="Unknown",
                inline=True
            )

        embed.set_thumbnail(url=member.display_avatar.url)
        embed.set_footer(text=f"User ID: {member.id}")

        await self.send_log(member.guild, embed)

    async def update_invite_stats(self, guild_id, user_id, change_type, amount=1):
        """Update invite statistics for a user"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                # Get current stats
                await cursor.execute(
                    "SELECT total_invites, fake_invites, left_invites FROM invite_stats WHERE guild_id = %s AND user_id = %s",
                    (guild_id, user_id)
                )
                result = await cursor.fetchone()

                if result:
                    total, fake, left = result
                else:
                    total, fake, left = 0, 0, 0

                # Update based on change type
                if change_type == "join":
                    total += amount
                elif change_type == "leave":
                    left += amount
                elif change_type == "fake":
                    fake += amount
                elif change_type == "add":
                    total += amount
                elif change_type == "remove":
                    total -= amount

                # Insert or update
                await cursor.execute(
                    """INSERT INTO invite_stats (guild_id, user_id, total_invites, fake_invites, left_invites)
                       VALUES (%s, %s, %s, %s, %s)
                       ON DUPLICATE KEY UPDATE
                       total_invites = %s, fake_invites = %s, left_invites = %s""",
                    (guild_id, user_id, total, fake, left, total, fake, left)
                )

    @commands.group(
        name="invites",
        description="Invite management system",
        invoke_without_command=True
    )
    async def invites(self, ctx, user: discord.Member = None):
        """Check invite count for a user"""
        if user is None:
            user = ctx.author

        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT total_invites, fake_invites, left_invites FROM invite_stats WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, user.id)
                )
                result = await cursor.fetchone()

        if result:
            total, fake, left = result
            real_invites = total - fake - left
        else:
            total, fake, left, real_invites = 0, 0, 0, 0

        embed = discord.Embed(
            title="📊 Invite Statistics",
            color=self.bot.color
        )

        embed.add_field(
            name="User",
            value=f"{user.mention}",
            inline=False
        )

        embed.add_field(
            name="📈 Total Invites",
            value=f"{total}",
            inline=True
        )

        embed.add_field(
            name="✅ Real Invites",
            value=f"{real_invites}",
            inline=True
        )

        embed.add_field(
            name="❌ Fake/Left",
            value=f"{fake + left}",
            inline=True
        )

        embed.add_field(
            name="🚫 Fake Invites",
            value=f"{fake}",
            inline=True
        )

        embed.add_field(
            name="📤 Left Invites",
            value=f"{left}",
            inline=True
        )

        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.display_avatar.url)

        await ctx.reply(embed=embed)

    @invites.command(
        name="leaderboard",
        description="Show invite leaderboard",
        aliases=["lb", "top"]
    )
    async def invite_leaderboard(self, ctx):
        """Show invite leaderboard"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """SELECT user_id, total_invites, fake_invites, left_invites
                       FROM invite_stats
                       WHERE guild_id = %s
                       ORDER BY (total_invites - fake_invites - left_invites) DESC
                       LIMIT 10""",
                    (ctx.guild.id,)
                )
                results = await cursor.fetchall()

        if not results:
            return await embed.warn(ctx, "No invite data found!")

        embed = discord.Embed(
            title="🏆 Invite Leaderboard",
            color=self.bot.color
        )

        description = ""
        for i, (user_id, total, fake, left) in enumerate(results, 1):
            user = ctx.guild.get_member(user_id)
            if user:
                real_invites = total - fake - left
                description += f"{i}. {user.mention} - **{real_invites}** invites\n"

        embed.description = description
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.display_avatar.url)
        await ctx.reply(embed=embed)

    @invites.command(
        name="add",
        description="Add invites to a user",
        usage="@user [amount]"
    )
    @Perms.get_perms("manage_guild")
    async def invite_add(self, ctx, user: discord.Member, amount: int = 1):
        """Add invites to a user"""
        if amount <= 0:
            return await embed.warn(ctx, "Amount must be positive!")

        await self.update_invite_stats(ctx.guild.id, user.id, "add", amount)

        from config.constants import Emojis, Colors
        embed = discord.Embed(
            title=f"{Emojis.success} Invites Added",
            description=f"Added **{amount}** invites to {user.mention}",
            color=Colors.success
        )

        await ctx.reply(embed=embed)

    @invites.command(
        name="remove",
        description="Remove invites from a user",
        usage="@user [amount]"
    )
    @Perms.get_perms("manage_guild")
    async def invite_remove(self, ctx, user: discord.Member, amount: int = 1):
        """Remove invites from a user"""
        if amount <= 0:
            return await embed.warn(ctx, "Amount must be positive!")

        await self.update_invite_stats(ctx.guild.id, user.id, "remove", amount)

        from config.constants import Emojis, Colors
        embed = discord.Embed(
            title=f"{Emojis.error} Invites Removed",
            description=f"Removed **{amount}** invites from {user.mention}",
            color=Colors.error
        )

        await ctx.reply(embed=embed)

    @invites.command(
        name="reset",
        description="Reset all invite data for the server"
    )
    @Perms.get_perms("administrator")
    async def invite_reset(self, ctx):
        """Reset all invite data"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM invite_stats WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                await cursor.execute(
                    "DELETE FROM invite_tracking WHERE guild_id = %s",
                    (ctx.guild.id,)
                )

        # Reset invite cache
        self.invite_cache[ctx.guild.id] = {}
        await self.update_invite_cache(ctx.guild)

        embed = discord.Embed(
            title="🔄 Invites Reset",
            description="All invite data has been reset for this server",
            color=self.bot.color
        )

        await ctx.reply(embed=embed)

    @invites.command(
        name="info",
        description="Get information about an invite code",
        usage="[invite_code]"
    )
    async def invite_info(self, ctx, invite_code: str):
        """Get information about an invite code"""
        try:
            # Try to get invite info
            invite = await self.bot.fetch_invite(invite_code)

            embed = discord.Embed(
                title="📋 Invite Information",
                color=self.bot.color
            )

            embed.add_field(
                name="Code",
                value=f"`{invite.code}`",
                inline=True
            )

            embed.add_field(
                name="Guild",
                value=f"{invite.guild.name}",
                inline=True
            )

            embed.add_field(
                name="Channel",
                value=f"#{invite.channel.name}",
                inline=True
            )

            if invite.inviter:
                embed.add_field(
                    name="Created By",
                    value=f"{invite.inviter.mention}",
                    inline=True
                )

            embed.add_field(
                name="Uses",
                value=f"{invite.uses}/{invite.max_uses if invite.max_uses else '∞'}",
                inline=True
            )

            if invite.expires_at:
                embed.add_field(
                    name="Expires",
                    value=f"<t:{int(invite.expires_at.timestamp())}:R>",
                    inline=True
                )
            else:
                embed.add_field(
                    name="Expires",
                    value="Never",
                    inline=True
                )

            embed.add_field(
                name="Created",
                value=f"<t:{int(invite.created_at.timestamp())}:R>",
                inline=True
            )

            if invite.guild.icon:
                embed.set_thumbnail(url=invite.guild.icon.url)

            await ctx.reply(embed=embed)

        except discord.NotFound:
            await embed.error(ctx, "Invalid or expired invite code!")
        except discord.HTTPException:
            await embed.error(ctx, "Failed to fetch invite information!")

    @commands.command(
        name="invitor",
        description="Check who invited a user",
        usage="@user"
    )
    async def invitor(self, ctx, user: discord.Member):
        """Check who invited a user"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT inviter_id, invite_code, joined_at FROM member_invites WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, user.id)
                )
                result = await cursor.fetchone()

        embed = discord.Embed(
            title="👤 Invitor Information",
            color=self.bot.color
        )

        embed.add_field(
            name="Member",
            value=f"{user.mention}",
            inline=False
        )

        if result:
            inviter_id, invite_code, joined_at = result
            inviter = ctx.guild.get_member(inviter_id)

            embed.add_field(
                name="Invited By",
                value=f"<@{inviter_id}>" + (f" ({inviter})" if inviter else " (Left server)"),
                inline=True
            )

            embed.add_field(
                name="Invite Code",
                value=f"`{invite_code}`",
                inline=True
            )

            embed.add_field(
                name="Joined At",
                value=f"<t:{int(joined_at.timestamp())}:F>",
                inline=True
            )

            # Get inviter's current stats
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT total_invites, fake_invites, left_invites FROM invite_stats WHERE guild_id = %s AND user_id = %s",
                        (ctx.guild.id, inviter_id)
                    )
                    stats = await cursor.fetchone()

            if stats:
                total, fake, left = stats
                real_invites = total - fake - left
                embed.add_field(
                    name="Inviter's Stats",
                    value=f"**{real_invites}** real invites\n**{total}** total invites",
                    inline=True
                )
        else:
            embed.add_field(
                name="Status",
                value="❌ No invite data found for this user.\nThis could mean:\n• They joined before invite tracking was enabled\n• They joined via vanity URL or server widget\n• The data was reset",
                inline=False
            )

        embed.set_thumbnail(url=user.display_avatar.url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.display_avatar.url)

        await ctx.reply(embed=embed)


async def setup(bot):
    await bot.add_cog(InviteTracking(bot))
