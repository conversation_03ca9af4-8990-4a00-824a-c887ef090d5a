import discord
import humanfriendly
import datetime
from discord.ext import commands
from typing import Union
from tools.checks import Perms
from tools.utils import NoStaff, Invoke
from tools.ext import embed
from config.constants import Colors, Emojis


class ModConfig:
    """Utility class for moderation actions"""
    
    @staticmethod
    async def send_dm(ctx: commands.Context, member: discord.Member, action: str, reason: str):
        """Send DM notification to moderated member"""
        view = discord.ui.View()
        view.add_item(
            discord.ui.Button(label=f"sent from {ctx.guild.name}", disabled=True)
        )
        embed = discord.Embed(
            color=ctx.bot.color,
            description=f"You have been **{action}** in {ctx.guild.name}\n{f'reason: {reason}' if reason != 'No reason provided' else ''}",
        )
        try:
            await member.send(embed=embed, view=view)
        except:
            pass

    @staticmethod
    async def send_mod_log(bot, guild, action: str, moderator: discord.Member, 
                          target: Union[discord.Member, discord.User], reason: str, 
                          duration: str = None):
        """Send moderation log using new logging system"""
        # Get the logging system cog
        logging_cog = bot.get_cog('LoggingSystem')
        if not logging_cog:
            return
        
        # Create embed for mod log
        embed = discord.Embed(
            color=Colors.default,
            timestamp=datetime.datetime.now()
        )
        embed.set_author(name="Mod Entry", icon_url=target.display_avatar.url)
        
        info_text = f"**Case:** {action.title()}\n**User:** {target.display_name} (`{target.id}`)"
        info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"
        
        if duration:
            info_text += f"\n**Duration:** {duration}"
        
        if reason and reason != "No reason provided":
            info_text += f"\n**Reason:** {reason}"
        
        embed.add_field(
            name="**Information**",
            value=info_text,
            inline=False
        )
        
        # Send to mod logs
        await logging_cog.send_log(guild, "mod", embed)


class ModerationCommands(commands.Cog):
    """Core moderation commands with new logging system integration"""
    
    def __init__(self, bot: commands.AutoShardedBot):
        self.bot = bot

    @commands.command(
        description="ban a member from your server",
        help="moderation",
        usage="[member] <reason>",
    )
    @Perms.get_perms("ban_members")
    async def ban(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Ban a member from the server"""
        await ctx.guild.ban(user=member, reason=reason + " | {}".format(ctx.author))
        await ModConfig.send_dm(ctx, member, "banned", reason)
        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "ban", ctx.author, member, reason
        )
        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"**{member}** was banned - {reason}")

    @commands.command(
        description="kick a member from your server",
        help="moderation",
        usage="[member] <reason>",
    )
    @Perms.get_perms("kick_members")
    async def kick(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Kick a member from the server"""
        await ctx.guild.kick(user=member, reason=reason + " | {}".format(ctx.author))
        await ModConfig.send_dm(ctx, member, "kicked", reason)
        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "kick", ctx.author, member, reason
        )
        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"**{member}** was kicked - {reason}")

    @commands.command(
        description="mute members in your server",
        help="moderation",
        brief="moderate members",
        usage="[member] [time] <reason>",
        aliases=["timeout"],
    )
    @Perms.get_perms("moderate_members")
    async def mute(
        self,
        ctx: commands.Context,
        member: NoStaff,
        time: str = "60s",
        *,
        reason="No reason provided",
    ):
        """Timeout a member for a specified duration"""
        tim = humanfriendly.parse_timespan(time)
        until = discord.utils.utcnow() + datetime.timedelta(seconds=tim)
        duration_text = humanfriendly.format_timespan(tim)
        
        await member.timeout(until, reason=reason + " | {}".format(ctx.author))
        
        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "mute", ctx.author, member, reason, duration_text
        )
        await ModConfig.send_dm(
            ctx, member, "muted", reason + " | " + duration_text
        )
        
        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"**{member}** has been muted for {duration_text} | {reason}")

    @commands.command(
        description="unmute a member in your server",
        help="moderation",
        brief="moderate members",
        usage="[member] <reason>",
        aliases=["untimeout"],
    )
    @Perms.get_perms("moderate_members")
    async def unmute(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Remove timeout from a member"""
        if not member.is_timed_out():
            return await embed.warn(ctx, f"**{member}** is not muted")
        
        await member.edit(
            timed_out_until=None, reason=reason + " | {}".format(ctx.author)
        )
        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "unmute", ctx.author, member, reason
        )
        
        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"unmuted **{member}**")

    @commands.command(
        description="unban a user from your server",
        help="moderation",
        usage="[member] <reason>",
    )
    @Perms.get_perms("ban_members")
    async def unban(
        self,
        ctx: commands.Context,
        member: discord.User,
        *,
        reason: str = "No reason provided",
    ):
        """Unban a user from the server"""
        try:
            await ctx.guild.unban(
                user=member, reason=reason + " | {}".format(ctx.author)
            )
            await ModConfig.send_mod_log(
                self.bot, ctx.guild, "unban", ctx.author, member, reason
            )
            if not await Invoke.invoke_send(ctx, member, reason):
                await embed.success(ctx, f"**{member}** has been unbanned")
        except discord.NotFound:
            return await embed.warn(ctx, f"**{member}** is not banned")

    @commands.command(
        aliases=["c"],
        description="bulk delete messages",
        help="moderation",
        brief="manage messages",
        usage="[messages] <member>",
    )
    @Perms.get_perms("manage_messages")
    async def purge(
        self, ctx: commands.Context, amount: int, *, member: NoStaff = None
    ):
        """Bulk delete messages from channel"""
        if amount <= 0:
            return await embed.warn(ctx, "Amount must be greater than 0")
        if amount > 100:
            return await embed.warn(ctx, "Cannot purge more than 100 messages at once")
            
        if member is None:
            await ctx.channel.purge(
                limit=amount + 1, bulk=True, reason=f"purge invoked by {ctx.author}"
            )
            # embed.success will automatically fallback to ctx.send if reply fails
            message = await embed.success(ctx, f"Purged **{amount}** messages")
            await message.delete(delay=5)
            return

        messages = []
        async for m in ctx.channel.history():
            if m.author.id == member.id:
                messages.append(m)
            if len(messages) == amount:
                break
        messages.append(ctx.message)
        await ctx.channel.delete_messages(messages)
        # embed.success will automatically fallback to ctx.send if reply fails
        message = await embed.success(ctx, f"Purged **{len(messages)-1}** messages from **{member}**")
        await message.delete(delay=5)

    @commands.command(
        description="lock a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def lock(self, ctx: commands.Context, channel: discord.TextChannel = None):
        """Lock a channel (disable send_messages for @everyone)"""
        channel = channel or ctx.channel

        # Check if channel is already locked
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        if overwrite.send_messages is False:
            return await ctx.message.add_reaction("⚠️")

        # Lock the channel
        overwrite.send_messages = False
        await channel.set_permissions(
            ctx.guild.default_role,
            overwrite=overwrite,
            reason=f"Channel locked by {ctx.author}"
        )

        # React with lock emoji
        await ctx.message.add_reaction(Emojis.lock)

    @commands.command(
        description="unlock a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def unlock(self, ctx: commands.Context, channel: discord.TextChannel = None):
        """Unlock a channel (enable send_messages for @everyone)"""
        channel = channel or ctx.channel

        # Check if channel is already unlocked
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        if overwrite.send_messages is not False:
            return await ctx.message.add_reaction("⚠️")

        # Unlock the channel
        overwrite.send_messages = True
        await channel.set_permissions(
            ctx.guild.default_role,
            overwrite=overwrite,
            reason=f"Channel unlocked by {ctx.author}"
        )

        # React with unlock emoji
        await ctx.message.add_reaction(Emojis.unlock)

    @commands.command(
        description="hide a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def hide(self, ctx: commands.Context, channel: discord.TextChannel = None):
        """Hide a channel (disable view_channel for @everyone)"""
        channel = channel or ctx.channel

        # Check if channel is already hidden
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        if overwrite.view_channel is False:
            return await ctx.message.add_reaction("⚠️")

        # Hide the channel
        overwrite.view_channel = False
        await channel.set_permissions(
            ctx.guild.default_role,
            overwrite=overwrite,
            reason=f"Channel hidden by {ctx.author}"
        )

        # React with ghost emoji (hide)
        await ctx.message.add_reaction(Emojis.ghost)

    @commands.command(
        description="unhide a channel", help="moderation", usage="<channel>"
    )
    @Perms.get_perms("manage_channels")
    async def unhide(self, ctx: commands.Context, channel: discord.TextChannel = None):
        """Unhide a channel (enable view_channel for @everyone)"""
        channel = channel or ctx.channel

        # Check if channel is already visible
        overwrite = channel.overwrites_for(ctx.guild.default_role)
        if overwrite.view_channel is not False:
            return await ctx.message.add_reaction("⚠️")

        # Unhide the channel
        overwrite.view_channel = True
        await channel.set_permissions(
            ctx.guild.default_role,
            overwrite=overwrite,
            reason=f"Channel unhidden by {ctx.author}"
        )

        # React with unghost emoji (unhide)
        await ctx.message.add_reaction(Emojis.unghost)

    async def cog_command_error(self, ctx, error):
        """Handle command errors for moderation commands"""
        if isinstance(error, commands.MissingRequiredArgument):
            # Show help for the command instead of error message
            return await ctx.send_help(ctx.command)

        # Let other errors be handled by global error handler
        raise error


async def setup(bot):
    await bot.add_cog(ModerationCommands(bot))
