import discord
from discord.ext import commands
from tools.checks import Perms
from datetime import datetime
import io
import asyncio
from config.constants import Emojis, Colors


class LoggingSystem(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    async def send_log(self, guild, log_type, embed, files=None):

        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id FROM logging_config WHERE guild_id = %s AND log_type = %s",
                        (guild.id, log_type)
                    )
                    result = await cursor.fetchone()

            if result:
                channel = guild.get_channel(result[0])
                if channel:
                    if files:
                        await channel.send(embed=embed, files=files)
                    else:
                        await channel.send(embed=embed)
        except Exception as e:
            print(f"Logging error: {e}")

    @commands.group(
        name="logs",
        description="community logging system",
        invoke_without_command=True
    )
    @Perms.get_perms("manage_guild")
    async def logs(self, ctx):
        if ctx.invoked_subcommand is None:
            help_command = self.bot.get_command('help')
            if help_command:
                await ctx.invoke(help_command, command_name='logs')
    
    @logs.command(
        name="add",
        description="Add logging channel",
        usage="#channel [type]"
    )
    @Perms.get_perms("manage_guild")
    async def logs_add(self, ctx, channel: discord.TextChannel, log_type: str):
        """Add logging channel for specific type"""
        valid_types = ["mod", "message", "media", "invite", "server", "all"]

        if log_type.lower() not in valid_types:
            # Show embed with valid log types
            embed = discord.Embed(
                title="❌ Invalid Log Type",
                description=f"**`{log_type}`** is not a valid log type.",
                color=Colors.default
            )

            embed.add_field(
                name="Valid Log Types",
                value="• `mod` - Moderation actions (bans, kicks, timeouts)\n"
                      "• `message` - Message deletions/edits\n"
                      "• `media` - Image/video deletions\n"
                      "• `invite` - Join/leave with invite tracking\n"
                      "• `server` - Server events (channels, roles, voice, emojis, etc)\n"
                      "• `all` - All log types",
                inline=False
            )

            embed.add_field(
                name="Usage",
                value=f"`logs add {channel.mention} [type]`",
                inline=False
            )

            return await ctx.reply(embed=embed)

        log_type = log_type.lower()

        if log_type == "all":
            # Set all log types to this channel
            all_types = ["mod", "message", "media", "invite", "server"]
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    for single_type in all_types:
                        await cursor.execute(
                            """INSERT INTO logging_config (guild_id, log_type, channel_id)
                               VALUES (%s, %s, %s)
                               ON DUPLICATE KEY UPDATE channel_id = %s""",
                            (ctx.guild.id, single_type, channel.id, channel.id)
                        )

            await embed.success(ctx, f"Set **all** logging types to {channel.mention}")
        else:
            # Set single log type
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        """INSERT INTO logging_config (guild_id, log_type, channel_id)
                           VALUES (%s, %s, %s)
                           ON DUPLICATE KEY UPDATE channel_id = %s""",
                        (ctx.guild.id, log_type, channel.id, channel.id)
                    )

            await embed.success(ctx, f"Set **{log_type}** logging to {channel.mention}")
    
    @logs.command(
        name="remove",
        description="Remove logging for type",
        usage="[type]"
    )
    @Perms.get_perms("manage_guild")
    async def logs_remove(self, ctx, log_type: str):
        """Remove logging for specific type"""
        valid_types = ["mod", "message", "media", "invite", "server", "all"]

        if log_type.lower() not in valid_types:
            # Show embed with valid log types
            embed = discord.Embed(
                title="❌ Invalid Log Type",
                description=f"**`{log_type}`** is not a valid log type.",
                color=Colors.default
            )

            embed.add_field(
                name="Valid Log Types",
                value="• `mod` - Moderation actions (bans, kicks, timeouts)\n"
                      "• `message` - Message deletions/edits\n"
                      "• `media` - Image/video deletions\n"
                      "• `invite` - Join/leave with invite tracking\n"
                      "• `server` - Server events (channels, roles, voice, emojis, etc)\n"
                      "• `all` - All log types",
                inline=False
            )

            embed.add_field(
                name="Usage",
                value="`logs remove [type]`",
                inline=False
            )

            return await ctx.reply(embed=embed)

        log_type = log_type.lower()

        if log_type == "all":
            # Remove all log types
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM logging_config WHERE guild_id = %s",
                        (ctx.guild.id,)
                    )
                    result = cursor.rowcount

            if result == 0:
                return await embed.warn(ctx, "No logging was configured!")

            await embed.success(ctx, f"Removed **all** logging ({result} types)")
        else:
            # Remove single log type
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "DELETE FROM logging_config WHERE guild_id = %s AND log_type = %s",
                        (ctx.guild.id, log_type)
                    )
                    result = cursor.rowcount

            if result == 0:
                return await embed.warn(ctx, f"No **{log_type}** logging was configured!")

            await embed.success(ctx, f"Removed **{log_type}** logging")
    
    @logs.command(
        name="list",
        description="View logging settings"
    )
    @Perms.get_perms("manage_guild")
    async def logs_list(self, ctx):
        """View current logging settings"""
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT log_type, channel_id FROM logging_config WHERE guild_id = %s",
                    (ctx.guild.id,)
                )
                results = await cursor.fetchall()

        # Create a dict of configured log types
        configured_logs = {log_type: channel_id for log_type, channel_id in results}

        # Define all log types with their display names
        log_types = [
            ("mod", "moderation events"),
            ("message", "message events"),
            ("media", "media events"),
            ("invite", "invite events"),
            ("server", "server events")
        ]

        # Build description with numbered list
        description_lines = []
        for i, (log_type, display_name) in enumerate(log_types, 1):
            if log_type in configured_logs:
                channel = ctx.guild.get_channel(configured_logs[log_type])
                if channel:
                    status = channel.mention
                else:
                    status = "`deleted channel`"
            else:
                status = "`none`"

            description_lines.append(f"`{i}` **{display_name}** - {status}")

        embed_obj = discord.Embed(
            title="Log Channels",
            description="\n".join(description_lines),
            color=Colors.default
        )
        embed_obj.set_author(
            name=ctx.author.display_name,
            icon_url=ctx.author.display_avatar.url
        )

        await ctx.send(embed=embed_obj)

    # Message logging events
    @commands.Cog.listener()
    async def on_message_delete(self, message):
        """Log message deletions"""
        if message.author.bot or not message.guild:
            return

        # Check for media attachments
        has_media = False
        media_files = []
        if message.attachments:
            for attachment in message.attachments:
                if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.mp4', '.mov', '.webm']):
                    has_media = True
                    media_files.append(attachment)

        if has_media:
            # Create media deletion embed
            media_embed = discord.Embed(
                description=f"Message from {message.author.mention} deleted in {message.channel.mention}",
                color=Colors.default,
                timestamp=datetime.now()
            )
            media_embed.set_author(name="Message deleted", icon_url=message.author.display_avatar.url)

            # Add message content if any
            if message.content:
                content = message.content[:1000] + "..." if len(message.content) > 1000 else message.content
                media_embed.add_field(
                    name="**Message Content**",
                    value=content,
                    inline=False
                )

            media_embed.set_footer(text=f"User ID: {message.author.id}")

            # Attach media files if possible
            files = []
            for attachment in media_files:
                try:
                    # Try to re-attach the media file
                    file_data = await attachment.read()
                    files.append(discord.File(io.BytesIO(file_data), filename=attachment.filename))
                except:
                    pass

            # Send to media logs
            if files:
                await self.send_log(message.guild, "media", media_embed, files=files)
            else:
                await self.send_log(message.guild, "media", media_embed)

            # Don't send regular message log if it's a media deletion
            return

        # Create regular message deletion embed (only if no media)
        embed = discord.Embed(
            description=f"Message from {message.author.mention} deleted in {message.channel.mention}",
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Message deleted", icon_url=message.author.display_avatar.url)

        if message.content:
            content = message.content[:1000] + "..." if len(message.content) > 1000 else message.content
            embed.add_field(
                name="**Message Content**",
                value=content,
                inline=False
            )

        embed.set_footer(text=f"User ID: {message.author.id}")

        await self.send_log(message.guild, "message", embed)

    @commands.Cog.listener()
    async def on_message_edit(self, before, after):
        """Log message edits"""
        if before.author.bot or not before.guild or before.content == after.content:
            return

        # Calculate relative time for edited message
        edited_timestamp = int(after.edited_at.timestamp()) if after.edited_at else int(datetime.now().timestamp())

        embed = discord.Embed(
            description=f"[Message]({after.jump_url}) from {before.author.mention} edited <t:{edited_timestamp}:R>",
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Message edited", icon_url=before.author.display_avatar.url)

        if before.content:
            before_content = before.content[:500] + "..." if len(before.content) > 500 else before.content
            embed.add_field(
                name="**Before**",
                value=f"```{before_content}```",
                inline=False
            )

        if after.content:
            after_content = after.content[:500] + "..." if len(after.content) > 500 else after.content
            embed.add_field(
                name="**After**",
                value=f"```{after_content}```",
                inline=False
            )

        embed.set_footer(text=f"User ID: {before.author.id}")

        await self.send_log(before.guild, "message", embed)

    # Server event logging
    @commands.Cog.listener()
    async def on_guild_channel_create(self, channel):
        """Log channel creation"""
        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in channel.guild.audit_logs(action=discord.AuditLogAction.channel_create, limit=1):
                if entry.target.id == channel.id:
                    moderator = entry.user
                    break
        except:
            pass

        channel_type = "Text channel" if channel.type == discord.ChannelType.text else "Voice channel" if channel.type == discord.ChannelType.voice else str(channel.type).title() + " channel"

        description = f"{channel_type} {channel.mention} created"
        if moderator:
            description += f" by {moderator.mention}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Channel Created", icon_url=moderator.display_avatar.url if moderator else channel.guild.icon.url if channel.guild.icon else None)

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(channel.guild, "server", embed)

    @commands.Cog.listener()
    async def on_guild_channel_delete(self, channel):
        """Log channel deletion"""
        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in channel.guild.audit_logs(action=discord.AuditLogAction.channel_delete, limit=1):
                if entry.target.id == channel.id:
                    moderator = entry.user
                    break
        except:
            pass

        channel_type = "Text channel" if channel.type == discord.ChannelType.text else "Voice channel" if channel.type == discord.ChannelType.voice else str(channel.type).title() + " channel"

        description = f"{channel_type} **#{channel.name}** deleted"
        if moderator:
            description += f" by {moderator.mention}"

        if hasattr(channel, 'created_at'):
            created_timestamp = int(channel.created_at.timestamp())
            description += f"\nChannel was created at <t:{created_timestamp}:F> (<t:{created_timestamp}:R>)"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Channel Deleted", icon_url=moderator.display_avatar.url if moderator else channel.guild.icon.url if channel.guild.icon else None)

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(channel.guild, "server", embed)

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Log member role updates and timeout changes"""
        # Handle role changes
        if before.roles != after.roles:
            added_roles = [role for role in after.roles if role not in before.roles]
            removed_roles = [role for role in before.roles if role not in after.roles]

            if added_roles or removed_roles:
                # Try to get moderator from audit logs
                moderator = None
                try:
                    async for entry in after.guild.audit_logs(action=discord.AuditLogAction.member_role_update, limit=1):
                        if entry.target.id == after.id:
                            moderator = entry.user
                            break
                except:
                    pass

                # Create description based on role changes
                if added_roles and removed_roles:
                    role_text = f"was granted {', '.join([role.mention for role in added_roles])} and removed from {', '.join([role.mention for role in removed_roles])}"
                elif added_roles:
                    role_text = f"was granted {', '.join([role.mention for role in added_roles])}"
                else:
                    role_text = f"was removed from {', '.join([role.mention for role in removed_roles])}"

                embed = discord.Embed(
                    description=f"{after.mention} {role_text}",
                    color=Colors.default,
                    timestamp=datetime.now()
                )
                embed.set_author(name="Member Roles Updated", icon_url=after.display_avatar.url)
                embed.set_footer(text=f"User ID: {after.id}")

                if moderator:
                    embed.add_field(
                        name="**Moderator**",
                        value=f"{moderator.mention} (`{moderator.id}`)",
                        inline=True
                    )

                await self.send_log(after.guild, "server", embed)

        # Handle timeout changes
        if before.timed_out_until != after.timed_out_until:
            # Member was timed out or timeout was removed
            moderator = None
            reason = None

            try:
                async for entry in after.guild.audit_logs(action=discord.AuditLogAction.member_update, limit=1):
                    if entry.target.id == after.id and (datetime.now() - entry.created_at).total_seconds() < 5:
                        moderator = entry.user
                        reason = entry.reason
                        break
            except:
                pass

            if after.timed_out_until and not before.timed_out_until:
                # Member was timed out (new timeout)
                embed = discord.Embed(
                    color=Colors.default,
                    timestamp=datetime.now()
                )
                embed.set_author(name="Mod Entry", icon_url=after.display_avatar.url)

                duration_seconds = (after.timed_out_until - datetime.now()).total_seconds()
                duration_text = self.format_duration(duration_seconds)
                until_timestamp = int(after.timed_out_until.timestamp())

                info_text = f"**Case:** Timed Out\n**User:** {after.display_name} (`{after.id}`)"

                if moderator:
                    info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

                info_text += f"\n**Until:** <t:{until_timestamp}:F>"
                info_text += f"\n**Duration:** {duration_text}"

                if reason:
                    info_text += f"\n**Reason:** {reason}"

                embed.add_field(
                    name="**Information**",
                    value=info_text,
                    inline=False
                )

                await self.send_log(after.guild, "mod", embed)

            elif before.timed_out_until and not after.timed_out_until:
                # Timeout was removed
                embed = discord.Embed(
                    color=Colors.default,
                    timestamp=datetime.now()
                )
                embed.set_author(name="Mod Entry", icon_url=after.display_avatar.url)

                info_text = f"**Case:** Timeout Removed\n**User:** {after.display_name} (`{after.id}`)"

                if moderator:
                    info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

                if reason:
                    info_text += f"\n**Reason:** {reason}"

                embed.add_field(
                    name="**Information**",
                    value=info_text,
                    inline=False
                )

                await self.send_log(after.guild, "mod", embed)

    @commands.Cog.listener()
    async def on_guild_role_create(self, role):
        """Log role creation"""
        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in role.guild.audit_logs(action=discord.AuditLogAction.role_create, limit=1):
                if entry.target.id == role.id:
                    moderator = entry.user
                    break
        except:
            pass

        description = f"Role {role.mention} created"
        if moderator:
            description += f" by {moderator.mention}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Role Created", icon_url=moderator.display_avatar.url if moderator else role.guild.icon.url if role.guild.icon else None)

        # Add role details
        embed.add_field(
            name="**Name**",
            value=role.name,
            inline=True
        )

        embed.add_field(
            name="**Color**",
            value=f"`{str(role.color)}`",
            inline=True
        )

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(role.guild, "server", embed)

    @commands.Cog.listener()
    async def on_guild_role_delete(self, role):
        """Log role deletion"""
        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in role.guild.audit_logs(action=discord.AuditLogAction.role_delete, limit=1):
                if entry.target.id == role.id:
                    moderator = entry.user
                    break
        except:
            pass

        description = f"Role **@{role.name}** deleted"
        if moderator:
            description += f" by {moderator.mention}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Role Deleted", icon_url=moderator.display_avatar.url if moderator else role.guild.icon.url if role.guild.icon else None)

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(role.guild, "server", embed)

    @commands.Cog.listener()
    async def on_guild_role_update(self, before, after):
        """Log role updates"""
        if before.name == after.name and before.color == after.color and before.icon == after.icon:
            return

        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in after.guild.audit_logs(action=discord.AuditLogAction.role_update, limit=1):
                if entry.target.id == after.id:
                    moderator = entry.user
                    break
        except:
            pass

        description = f"Role {after.mention} updated"
        if moderator:
            description += f" by {moderator.mention}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Role Updated", icon_url=moderator.display_avatar.url if moderator else after.guild.icon.url if after.guild.icon else None)

        # Add thumbnail if role has icon
        if after.icon:
            embed.set_thumbnail(url=after.icon.url)

        # Check for name changes
        if before.name != after.name:
            embed.add_field(
                name="**Name**",
                value=f"{before.name} -> {after.name}",
                inline=True
            )

        # Check for color changes
        if before.color != after.color:
            embed.add_field(
                name="**Color**",
                value=f"{before.color} -> {after.color}",
                inline=True
            )

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(after.guild, "server", embed)

    @commands.Cog.listener()
    async def on_guild_channel_update(self, before, after):
        """Log channel permission updates"""
        # Check if permissions changed
        if before.overwrites == after.overwrites:
            return

        # Try to get moderator from audit logs
        moderator = None
        try:
            async for entry in after.guild.audit_logs(action=discord.AuditLogAction.overwrite_update, limit=1):
                if entry.target.id == after.id and (datetime.now() - entry.created_at).total_seconds() < 10:
                    moderator = entry.user
                    break
        except:
            pass

        # Find permission changes and affected target
        permission_changes = []
        affected_target = None

        # Compare overwrites to find what changed
        all_targets = set(list(before.overwrites.keys()) + list(after.overwrites.keys()))

        for target in all_targets:
            old_overwrite = before.overwrites.get(target)
            new_overwrite = after.overwrites.get(target)

            if old_overwrite != new_overwrite:
                affected_target = target

                if old_overwrite and new_overwrite:
                    # Permission values changed
                    for perm, new_value in new_overwrite:
                        old_value = getattr(old_overwrite, perm, None)
                        if old_value != new_value:
                            perm_name = perm.replace('_', ' ').title()
                            old_symbol = "✅" if old_value else "❌" if old_value is False else "➖"
                            new_symbol = "✅" if new_value else "❌" if new_value is False else "➖"
                            permission_changes.append(f"**{perm_name}**: `{old_symbol}` -> `{new_symbol}`")
                elif new_overwrite and not old_overwrite:
                    # New overwrite added
                    for perm, value in new_overwrite:
                        if value is not None:
                            perm_name = perm.replace('_', ' ').title()
                            symbol = "✅" if value else "❌"
                            permission_changes.append(f"**{perm_name}**: `➖` -> `{symbol}`")
                elif old_overwrite and not new_overwrite:
                    # Overwrite removed
                    for perm, value in old_overwrite:
                        if value is not None:
                            perm_name = perm.replace('_', ' ').title()
                            symbol = "✅" if value else "❌"
                            permission_changes.append(f"**{perm_name}**: `{symbol}` -> `➖`")
                break

        if not permission_changes or not affected_target:
            return

        target_name = affected_target.mention if hasattr(affected_target, 'mention') else affected_target.name
        description = f"Channel permissions {after.mention} for {target_name} updated"
        if moderator:
            description += f" by {moderator.mention}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Channel Permissions Updated", icon_url=moderator.display_avatar.url if moderator else after.guild.icon.url if after.guild.icon else None)

        embed.add_field(
            name="**Permissions Updated**",
            value="\n".join(permission_changes[:10]),  # Limit to 10 changes
            inline=False
        )

        if moderator:
            embed.set_footer(text=f"User ID: {moderator.id}")

        await self.send_log(after.guild, "server", embed)

    # Voice logging events
    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Log voice channel joins/leaves/moves"""
        if member.bot:
            return

        # Voice channel join
        if not before.channel and after.channel:
            embed = discord.Embed(
                description=f"{member.mention} joined voice channel {after.channel.mention}",
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name=f"{member.display_name} joined voice", icon_url=member.display_avatar.url)
            embed.set_footer(text=f"User ID: {member.id}")
            await self.send_log(member.guild, "server", embed)

        # Voice channel leave
        elif before.channel and not after.channel:
            embed = discord.Embed(
                description=f"{member.mention} left voice channel **{before.channel.name}**",
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name=f"{member.display_name} left voice", icon_url=member.display_avatar.url)
            embed.set_footer(text=f"User ID: {member.id}")
            await self.send_log(member.guild, "server", embed)

        # Voice channel move
        elif before.channel and after.channel and before.channel != after.channel:
            embed = discord.Embed(
                description=f"{member.mention} moved from **{before.channel.name}** to {after.channel.mention}",
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name=f"{member.display_name} moved voice", icon_url=member.display_avatar.url)
            embed.set_footer(text=f"User ID: {member.id}")
            await self.send_log(member.guild, "server", embed)

    # Guild update events
    @commands.Cog.listener()
    async def on_guild_update(self, before, after):
        """Log guild updates (name, icon, banner changes)"""
        changes = []

        if before.name != after.name:
            changes.append(f"**Name:** {before.name} -> {after.name}")

        if before.icon != after.icon:
            changes.append("**Icon:** Updated")

        if before.banner != after.banner:
            changes.append("**Banner:** Updated")

        if before.description != after.description:
            changes.append("**Description:** Updated")

        if changes:
            embed = discord.Embed(
                description="Server settings updated\n\n" + "\n".join(changes),
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Server Updated", icon_url=after.icon.url if after.icon else None)

            await self.send_log(after, "server", embed)

    # Moderation logging events
    @commands.Cog.listener()
    async def on_member_ban(self, guild, user):
        """Log member bans"""
        # Try to get ban info from audit logs
        moderator = None
        reason = None
        try:
            async for entry in guild.audit_logs(action=discord.AuditLogAction.ban, limit=1):
                if entry.target.id == user.id:
                    moderator = entry.user
                    reason = entry.reason
                    break
        except:
            pass

        embed = discord.Embed(
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Mod Entry", icon_url=user.display_avatar.url)

        info_text = f"**Case:** Banned\n**User:** {user.display_name} (`{user.id}`)"

        if moderator:
            info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

        if reason:
            info_text += f"\n**Reason:** {reason}"

        embed.add_field(
            name="**Information**",
            value=info_text,
            inline=False
        )

        await self.send_log(guild, "mod", embed)

    @commands.Cog.listener()
    async def on_member_unban(self, guild, user):
        """Log member unbans"""
        # Try to get unban info from audit logs
        moderator = None
        reason = None
        try:
            async for entry in guild.audit_logs(action=discord.AuditLogAction.unban, limit=1):
                if entry.target.id == user.id:
                    moderator = entry.user
                    reason = entry.reason
                    break
        except:
            pass

        embed = discord.Embed(
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Mod Entry", icon_url=user.display_avatar.url)

        info_text = f"**Case:** Unbanned\n**User:** {user.display_name} (`{user.id}`)"

        if moderator:
            info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

        if reason:
            info_text += f"\n**Reason:** {reason}"

        embed.add_field(
            name="**Information**",
            value=info_text,
            inline=False
        )

        await self.send_log(guild, "mod", embed)

    # Member join/leave logging with invite tracking
    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Log member joins with invite tracking"""
        if member.bot:
            return

        # Get invite info using similar logic to invites.py
        invite_info = await self.get_invite_info(member.guild)
        inviter = None
        invite_count = 0
        is_vanity = False

        try:
            if invite_info:
                invite, _ = invite_info
                if invite and invite.inviter:
                    inviter = invite.inviter
                    # Get inviter's total invite count (active invites)
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                "SELECT total_invites, fake_invites, left_invites FROM invite_stats WHERE guild_id = %s AND user_id = %s",
                                (member.guild.id, invite.inviter.id)
                            )
                            result = await cursor.fetchone()
                            if result:
                                total, fake, left = result
                                invite_count = total - fake - left  # Active invites (still in server)
                            else:
                                invite_count = 1

                    # Store who invited this member for future reference
                    async with self.bot.db.acquire() as conn:
                        async with conn.cursor() as cursor:
                            await cursor.execute(
                                """INSERT INTO member_invites (guild_id, user_id, inviter_id, invite_code, joined_at)
                                   VALUES (%s, %s, %s, %s, %s)
                                   ON DUPLICATE KEY UPDATE
                                   inviter_id = %s, invite_code = %s, joined_at = %s""",
                                (member.guild.id, member.id, invite.inviter.id, invite.code, datetime.now(),
                                 invite.inviter.id, invite.code, datetime.now())
                            )
                else:
                    # Check if it's vanity URL
                    try:
                        vanity_invite = await member.guild.vanity_invite()
                        if vanity_invite:
                            is_vanity = True
                            invite_count = vanity_invite.uses or 0
                            # Store vanity join
                            async with self.bot.db.acquire() as conn:
                                async with conn.cursor() as cursor:
                                    await cursor.execute(
                                        """INSERT INTO member_invites (guild_id, user_id, inviter_id, invite_code, joined_at)
                                           VALUES (%s, %s, %s, %s, %s)
                                           ON DUPLICATE KEY UPDATE
                                           inviter_id = %s, invite_code = %s, joined_at = %s""",
                                        (member.guild.id, member.id, None, "vanity", datetime.now(),
                                         None, "vanity", datetime.now())
                                    )
                    except:
                        pass
            else:
                # No invite info found, store as unknown
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            """INSERT INTO member_invites (guild_id, user_id, inviter_id, invite_code, joined_at)
                               VALUES (%s, %s, %s, %s, %s)
                               ON DUPLICATE KEY UPDATE
                               inviter_id = %s, invite_code = %s, joined_at = %s""",
                            (member.guild.id, member.id, None, "unknown", datetime.now(),
                             None, "unknown", datetime.now())
                        )
        except Exception as e:
            print(f"Error tracking invite for {member}: {e}")
            # Store as unknown if there's an error
            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            """INSERT INTO member_invites (guild_id, user_id, inviter_id, invite_code, joined_at)
                               VALUES (%s, %s, %s, %s, %s)
                               ON DUPLICATE KEY UPDATE
                               inviter_id = %s, invite_code = %s, joined_at = %s""",
                            (member.guild.id, member.id, None, "unknown", datetime.now(),
                             None, "unknown", datetime.now())
                        )
            except:
                pass

        # Create embed
        embed = discord.Embed(
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name=f"{member.display_name} joined", icon_url=member.display_avatar.url)

        # Description with created and joined timestamps
        created_timestamp = int(member.created_at.timestamp())
        joined_timestamp = int(datetime.now().timestamp())
        description = f"**Created**: <t:{created_timestamp}:R> **Joined**: <t:{joined_timestamp}:R>\n"

        if is_vanity:
            description += f"**Invited by:** vanity (`{invite_count} Invites`)"
        elif inviter:
            description += f"**Invited by:** {inviter.display_name} (`{invite_count} Invites`)"
        else:
            description += "**Invited by:** Unknown"

        embed.description = description
        embed.set_footer(text=f"User ID: {member.id}")

        await self.send_log(member.guild, "invite", embed)

    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Log member leaves and kicks"""
        if member.bot:
            return

        # Wait a moment to check if it was a kick
        await asyncio.sleep(1)

        # Check if it was a kick
        was_kicked = False
        moderator = None
        reason = None

        try:
            async for entry in member.guild.audit_logs(action=discord.AuditLogAction.kick, limit=1):
                if entry.target.id == member.id and (datetime.now() - entry.created_at).total_seconds() < 5:
                    was_kicked = True
                    moderator = entry.user
                    reason = entry.reason
                    break
        except:
            pass

        if was_kicked:
            # Handle kick logging
            embed = discord.Embed(
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Mod Entry", icon_url=member.display_avatar.url)

            info_text = f"**Case:** Kicked\n**User:** {member.display_name} (`{member.id}`)"

            if moderator:
                info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

            if reason:
                info_text += f"\n**Reason:** {reason}"

            embed.add_field(
                name="**Information**",
                value=info_text,
                inline=False
            )

            await self.send_log(member.guild, "mod", embed)
        else:
            # Handle regular leave logging
            # Get who invited this member
            inviter = None
            invite_count = 0
            is_vanity = False
            joined_at = None

            try:
                async with self.bot.db.acquire() as conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute(
                            "SELECT inviter_id, invite_code, joined_at FROM member_invites WHERE guild_id = %s AND user_id = %s",
                            (member.guild.id, member.id)
                        )
                        result = await cursor.fetchone()

                        if result:
                            inviter_id, invite_code, joined_at = result
                            if inviter_id:
                                inviter = member.guild.get_member(inviter_id)
                                if inviter:
                                    # Get current invite count (active invites)
                                    await cursor.execute(
                                        "SELECT total_invites, fake_invites, left_invites FROM invite_stats WHERE guild_id = %s AND user_id = %s",
                                        (member.guild.id, inviter_id)
                                    )
                                    stats = await cursor.fetchone()
                                    if stats:
                                        total, fake, left = stats
                                        invite_count = total - fake - left  # Active invites (still in server)
                                    else:
                                        invite_count = 1  # Default if no stats found
                            elif invite_code == "vanity":
                                is_vanity = True
                                try:
                                    vanity_invite = await member.guild.vanity_invite()
                                    if vanity_invite:
                                        invite_count = vanity_invite.uses or 0
                                except:
                                    pass

                        # Remove from member_invites table
                        await cursor.execute(
                            "DELETE FROM member_invites WHERE guild_id = %s AND user_id = %s",
                            (member.guild.id, member.id)
                        )
            except Exception as e:
                print(f"Error getting invite info for {member}: {e}")

            # Create embed
            embed = discord.Embed(
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name=f"{member.display_name} left", icon_url=member.display_avatar.url)

            # Description with created and joined timestamps
            created_timestamp = int(member.created_at.timestamp())
            joined_timestamp = int(joined_at.timestamp()) if joined_at else int(datetime.now().timestamp())
            description = f"**Created**: <t:{created_timestamp}:R> **Joined**: <t:{joined_timestamp}:R>\n"

            if is_vanity:
                description += f"**Invited by:** vanity (`{invite_count} Invites`)"
            elif inviter:
                description += f"**Invited by:** {inviter.display_name} (`{invite_count} Invites`)"
            else:
                description += "**Invited by:** Unknown"

            embed.description = description
            embed.set_footer(text=f"User ID: {member.id}")

            await self.send_log(member.guild, "invite", embed)

    def format_duration(self, seconds):
        """Format duration in seconds to human readable format"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s" if secs > 0 else f"{minutes}m"
        elif seconds < 86400:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
        else:
            days = int(seconds // 86400)
            hours = int((seconds % 86400) // 3600)
            return f"{days}d {hours}h" if hours > 0 else f"{days}d"

    async def get_invite_info(self, guild):
        """Get invite information for tracking (similar to invites.py logic)"""
        try:
            # Get current invites
            current_invites = await guild.invites()

            # Check if we have cached invites for this guild
            if guild.id not in getattr(self, 'invite_cache', {}):
                # Initialize cache if not exists
                if not hasattr(self, 'invite_cache'):
                    self.invite_cache = {}
                self.invite_cache[guild.id] = {}
                for invite in current_invites:
                    self.invite_cache[guild.id][invite.code] = invite.uses
                return None, 0

            # Find which invite was used
            for invite in current_invites:
                cached_uses = self.invite_cache[guild.id].get(invite.code, 0)
                if invite.uses > cached_uses:
                    # This invite was used
                    self.invite_cache[guild.id][invite.code] = invite.uses
                    return invite, invite.uses

            # Update cache for all invites
            for invite in current_invites:
                self.invite_cache[guild.id][invite.code] = invite.uses

            return None, 0
        except:
            return None, 0

    # Emoji events
    @commands.Cog.listener()
    async def on_guild_emojis_update(self, guild, before, after):
        """Log emoji create/delete/update"""
        added_emojis = [emoji for emoji in after if emoji not in before]
        removed_emojis = [emoji for emoji in before if emoji not in after]

        for emoji in added_emojis:
            # Try to get moderator from audit logs
            moderator = None
            try:
                async for entry in guild.audit_logs(action=discord.AuditLogAction.emoji_create, limit=1):
                    if entry.target.id == emoji.id:
                        moderator = entry.user
                        break
            except:
                pass

            description = f"Emoji created"
            if moderator:
                description += f" by {moderator.mention}"

            embed = discord.Embed(
                description=description,
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Emoji Created", icon_url=moderator.display_avatar.url if moderator else guild.icon.url if guild.icon else None)
            embed.set_thumbnail(url=emoji.url)

            if moderator:
                embed.set_footer(text=f"User ID: {moderator.id}")

            embed.add_field(
                name="**Name**",
                value=emoji.name,
                inline=True
            )

            await self.send_log(guild, "server", embed)

        for emoji in removed_emojis:
            # Try to get moderator from audit logs
            moderator = None
            try:
                async for entry in guild.audit_logs(action=discord.AuditLogAction.emoji_delete, limit=1):
                    if entry.target.id == emoji.id:
                        moderator = entry.user
                        break
            except:
                pass

            description = f"Emoji **:{emoji.name}:** deleted"
            if moderator:
                description += f" by {moderator.mention}"

            embed = discord.Embed(
                description=description,
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Emoji Deleted", icon_url=moderator.display_avatar.url if moderator else guild.icon.url if guild.icon else None)

            if moderator:
                embed.set_footer(text=f"User ID: {moderator.id}")

            await self.send_log(guild, "server", embed)

    # Invite events
    @commands.Cog.listener()
    async def on_invite_create(self, invite):
        """Log invite creation"""
        description = f"Invite created for {invite.channel.mention}\n\n"
        description += f"**Code:** `{invite.code}`\n"
        description += f"**Max Uses:** {invite.max_uses or 'Unlimited'}\n"
        description += f"**Expires:** {'Never' if not invite.max_age else f'<t:{int((datetime.now().timestamp() + invite.max_age))}:R>'}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Invite Created", icon_url=invite.inviter.display_avatar.url if invite.inviter else invite.guild.icon.url if invite.guild.icon else None)

        if invite.inviter:
            embed.set_footer(text=f"User ID: {invite.inviter.id}")

        await self.send_log(invite.guild, "server", embed)

    @commands.Cog.listener()
    async def on_invite_delete(self, invite):
        """Log invite deletion"""
        description = f"Invite deleted for **#{invite.channel.name}**\n\n"
        description += f"**Code:** `{invite.code}`\n"
        description += f"**Uses:** {invite.uses or 0}"

        embed = discord.Embed(
            description=description,
            color=Colors.default,
            timestamp=datetime.now()
        )
        embed.set_author(name="Invite Deleted", icon_url=invite.guild.icon.url if invite.guild.icon else None)

        await self.send_log(invite.guild, "server", embed)

    # Sticker events
    @commands.Cog.listener()
    async def on_guild_stickers_update(self, guild, before, after):
        """Log sticker creation and deletion"""
        added_stickers = [sticker for sticker in after if sticker not in before]
        removed_stickers = [sticker for sticker in before if sticker not in after]

        for sticker in added_stickers:
            # Try to get moderator from audit logs
            moderator = None
            try:
                async for entry in guild.audit_logs(action=discord.AuditLogAction.sticker_create, limit=1):
                    if entry.target.id == sticker.id:
                        moderator = entry.user
                        break
            except:
                pass

            description = f"Sticker created"
            if moderator:
                description += f" by {moderator.mention}"

            embed = discord.Embed(
                description=description,
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Sticker Created", icon_url=moderator.display_avatar.url if moderator else guild.icon.url if guild.icon else None)
            embed.set_thumbnail(url=sticker.url)

            if moderator:
                embed.set_footer(text=f"User ID: {moderator.id}")

            embed.add_field(
                name="**Name**",
                value=sticker.name,
                inline=True
            )

            await self.send_log(guild, "server", embed)

        for sticker in removed_stickers:
            # Try to get moderator from audit logs
            moderator = None
            try:
                async for entry in guild.audit_logs(action=discord.AuditLogAction.sticker_delete, limit=1):
                    if entry.target.id == sticker.id:
                        moderator = entry.user
                        break
            except:
                pass

            description = f"Sticker **{sticker.name}** deleted"
            if moderator:
                description += f" by {moderator.mention}"

            embed = discord.Embed(
                description=description,
                color=Colors.default,
                timestamp=datetime.now()
            )
            embed.set_author(name="Sticker Deleted", icon_url=moderator.display_avatar.url if moderator else guild.icon.url if guild.icon else None)

            if moderator:
                embed.set_footer(text=f"User ID: {moderator.id}")

            await self.send_log(guild, "server", embed)

    def format_duration(self, seconds):
        """Format duration in a readable way"""
        if seconds < 60:
            return f"{int(seconds)} seconds"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        elif seconds < 86400:
            hours = int(seconds / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''}"
        else:
            days = int(seconds / 86400)
            return f"{days} day{'s' if days != 1 else ''}"


async def setup(bot):
    await bot.add_cog(LoggingSystem(bot))
