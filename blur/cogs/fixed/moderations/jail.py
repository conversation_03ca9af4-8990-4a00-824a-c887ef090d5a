import discord
import datetime
from discord.ext import commands
from typing import Union
from tools.checks import Perms, Mod
from tools.utils import NoStaff, Invoke
from tools.ext import embed
from config.constants import Emojis


class ClearMod(discord.ui.View):
    def __init__(self, ctx):
        super().__init__(timeout=60)
        self.ctx = ctx
        self.status = False

    @discord.ui.button(emoji=Emojis.success, style=discord.ButtonStyle.green)
    async def yes(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.client.ext.send_warning(
                interaction, "You are not the author of this embed"
            )
        async with interaction.client.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (interaction.guild.id,)
                )
                check = await cursor.fetchone()
        channelid = check[1]  # channel_id is 2nd column
        roleid = check[3]     # role_id is 4th column  
        logsid = check[2]     # jail_id is 3rd column
        channel = interaction.guild.get_channel(channelid)
        role = interaction.guild.get_role(roleid)
        logs = interaction.guild.get_channel(logsid)
        try:
            await channel.delete()
        except:
            pass
        try:
            await role.delete()
        except:
            pass
        try:
            await logs.delete()
        except:
            pass
        async with interaction.client.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM `mod` WHERE guild_id = %s", (interaction.guild.id,)
                )
        self.status = True
        return await interaction.response.edit_message(
            view=None,
            embed=discord.Embed(
                color=interaction.client.color,
                description=f"{interaction.client.yes} {interaction.user.mention}: Disabled",
            ),
        )

    @discord.ui.button(emoji=Emojis.error, style=discord.ButtonStyle.red)
    async def no(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.ctx.author.id:
            return await interaction.client.ext.send_warning(
                interaction, "You are not the author of this embed"
            )
        self.status = True
        return await interaction.response.edit_message(
            view=None,
            embed=discord.Embed(
                color=interaction.client.color,
                description=f"{interaction.client.no} {interaction.user.mention}: Cancelled",
            ),
        )

    async def on_timeout(self) -> None:
        if self.status == False:
            for item in self.children:
                item.disabled = True

            await self.message.edit(view=self)


class ModConfig:
    @staticmethod
    async def send_mod_log(bot, guild, action: str, moderator: discord.Member,
                          target: Union[discord.Member, discord.User], reason: str):
        """Send moderation log using new logging system"""
        # Get the logging system cog
        logging_cog = bot.get_cog('LoggingSystem')
        if not logging_cog:
            return

        # Create embed for mod log
        embed = discord.Embed(
            color=0x2F3136,
            timestamp=datetime.datetime.now()
        )
        embed.set_author(name="Mod Entry", icon_url=target.display_avatar.url)

        info_text = f"**Case:** {action.title()}\n**User:** {target.display_name} (`{target.id}`)"
        info_text += f"\n**Moderator:** {moderator.display_name} (`{moderator.id}`)"

        if reason and reason != "No reason provided":
            info_text += f"\n**Reason:** {reason}"

        embed.add_field(
            name="**Information**",
            value=info_text,
            inline=False
        )

        # Send to mod logs
        await logging_cog.send_log(guild, "mod", embed)

    async def send_dm(
        ctx: commands.Context,
        member: Union[discord.Member, discord.User],
        action: str,
        reason: str,
    ):
        embed = discord.Embed(
            color=ctx.bot.color,
            description=f"You have been **{action}** from **{ctx.guild.name}** for **{reason}**",
        )
        try:
            await member.send(embed=embed)
        except:
            pass


class Jail(commands.Cog):
    def __init__(self, bot: commands.AutoShardedBot):
        self.bot = bot

    @commands.command(
        description="disable the moderation features in your server", help="moderation"
    )
    @Perms.get_perms("administrator")
    async def unsetme(self, ctx: commands.Context):
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()
        if not check:
            return await embed.warn(ctx, "Moderation is **not** enabled in this server")
        view = ClearMod(ctx)
        view.message = await ctx.reply(
            view=view,
            embed=discord.Embed(
                color=self.bot.color,
                description=f"{ctx.author.mention} Are you sure you want to **disable** jail?",
            ),
        )

    @commands.command(
        description="enable the moderation features in your server", help="moderation"
    )
    @Perms.get_perms("administrator")
    async def setme(self, ctx: commands.Context):
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                    )
                    check = await cursor.fetchone()
            if check:
                return await embed.warn(ctx, "Moderation is **already** enabled in this server")

            await ctx.typing()

            # Create role and set permissions
            role = await ctx.guild.create_role(name="blur-jail")
            for channel in ctx.guild.channels:
                try:
                    await channel.set_permissions(role, view_channel=False)
                except:
                    pass  # Skip channels we can't modify

            # Create category and channels
            overwrite = {
                role: discord.PermissionOverwrite(view_channel=True),
                ctx.guild.default_role: discord.PermissionOverwrite(view_channel=False),
            }
            over = {ctx.guild.default_role: discord.PermissionOverwrite(view_channel=False)}
            category = await ctx.guild.create_category(name="blur mod", overwrites=over)
            text = await ctx.guild.create_text_channel(
                name="mod-logs", overwrites=over, category=category
            )
            jai = await ctx.guild.create_text_channel(
                name="jail", overwrites=overwrite, category=category
            )

            # Insert into database
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "INSERT INTO `mod` (guild_id, channel_id, jail_id, role_id) VALUES (%s, %s, %s, %s) ON DUPLICATE KEY UPDATE channel_id = %s, jail_id = %s, role_id = %s",
                        (ctx.guild.id, text.id, jai.id, role.id, text.id, jai.id, role.id)
                    )
                    await cursor.execute(
                        "INSERT INTO cases (guild_id, count) VALUES (%s, %s) ON DUPLICATE KEY UPDATE count = %s",
                        (ctx.guild.id, 0, 0)
                    )
                    # Automatically set up mod logging to the created text channel
                    await cursor.execute(
                        """INSERT INTO logging_config (guild_id, log_type, channel_id)
                           VALUES (%s, %s, %s)
                           ON DUPLICATE KEY UPDATE channel_id = %s""",
                        (ctx.guild.id, "mod", text.id, text.id)
                    )

            return await embed.success(ctx, "Enabled **moderation** for this server and set up **mod logging**")

        except Exception as e:
            print(f"Error in setme: {e}")
            return await embed.error(ctx, "Failed to set up moderation system. Please try again.")

    @commands.command(
        description="jail a member", help="moderation", usage="[member] <reason>"
    )
    @Perms.get_perms("moderate_members")
    @Mod.is_mod_configured()
    async def jail(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Jail a member"""
        # Get jail configuration
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()

        if not check:
            return await embed.warn(ctx, "Jail system is not configured!")

        jail_role = ctx.guild.get_role(check[3])  # role_id is 4th column
        if not jail_role:
            return await embed.warn(ctx, "Jail role not found!")

        if jail_role in member.roles:
            return await embed.warn(ctx, f"**{member}** is already jailed!")

        # Remove all roles and add jail role
        old_roles = [role for role in member.roles if role != ctx.guild.default_role]
        await member.edit(roles=[jail_role], reason=f"Jailed by {ctx.author}: {reason}")

        # Store old roles for unjail
        role_ids = [str(role.id) for role in old_roles]
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO jailed_users (guild_id, user_id, roles) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE roles = %s",
                    (ctx.guild.id, member.id, ",".join(role_ids), ",".join(role_ids))
                )

        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "jail", ctx.author, member, reason
        )
        await ModConfig.send_dm(ctx, member, "jailed", reason)

        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"**{member}** was jailed - {reason}")

    @commands.command(
        description="unjail a member", help="moderation", usage="[member] <reason>"
    )
    @Perms.get_perms("moderate_members")
    @Mod.is_mod_configured()
    async def unjail(
        self,
        ctx: commands.Context,
        member: NoStaff,
        *,
        reason: str = "No reason provided",
    ):
        """Unjail a member"""
        # Get jail configuration
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                )
                check = await cursor.fetchone()

        if not check:
            return await embed.warn(ctx, "Jail system is not configured!")

        jail_role = ctx.guild.get_role(check[3])  # role_id is 4th column
        if not jail_role:
            return await embed.warn(ctx, "Jail role not found!")

        if jail_role not in member.roles:
            return await embed.warn(ctx, f"**{member}** is not jailed!")

        # Get old roles
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT roles FROM jailed_users WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )
                jailed_data = await cursor.fetchone()

        # Restore old roles
        roles_to_add = [ctx.guild.default_role]
        if jailed_data and jailed_data[0]:
            role_ids = jailed_data[0].split(",")
            for role_id in role_ids:
                if role_id.strip():
                    role = ctx.guild.get_role(int(role_id.strip()))
                    if role:
                        roles_to_add.append(role)

        await member.edit(roles=roles_to_add, reason=f"Unjailed by {ctx.author}: {reason}")

        # Remove from jailed users
        async with self.bot.db.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "DELETE FROM jailed_users WHERE guild_id = %s AND user_id = %s",
                    (ctx.guild.id, member.id)
                )

        await ModConfig.send_mod_log(
            self.bot, ctx.guild, "unjail", ctx.author, member, reason
        )

        if not await Invoke.invoke_send(ctx, member, reason):
            await embed.success(ctx, f"**{member}** was unjailed - {reason}")


async def setup(bot):
    await bot.add_cog(Jail(bot))
