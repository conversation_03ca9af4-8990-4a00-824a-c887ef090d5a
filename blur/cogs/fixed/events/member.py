import discord
from discord.ext import commands
from tools.ext import embed
from config.constants import Colors
from utils.parse import EmbedBuilder


class Member(commands.Cog):
    """Member join/leave events for welcome/goodbye messages"""
    
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Called when a member joins a guild"""
        # Check for welcome messages
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id, message FROM welcome_messages WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    welcome_messages = await cursor.fetchall()

            for channel_id, message in welcome_messages:
                channel = self.bot.get_channel(channel_id)
                if channel:
                    # Check if bot has permission to send messages
                    if not channel.permissions_for(member.guild.me).send_messages:
                        print(f"Missing send_messages permission in welcome channel {channel.name} ({channel.id}) for guild {member.guild.name}")
                        continue

                    # Create a mock context for variable replacement
                    class MockContext:
                        def __init__(self, author, guild, channel):
                            self.author = author
                            self.guild = guild
                            self.channel = channel

                    mock_ctx = MockContext(member, member.guild, channel)
                    builder = EmbedBuilder(mock_ctx)

                    # Replace variables manually for welcome system
                    processed_message = message.replace("{user}", member.mention)
                    processed_message = processed_message.replace("{user.mention}", member.mention)
                    processed_message = processed_message.replace("{user.name}", member.display_name)
                    processed_message = processed_message.replace("{user.id}", str(member.id))
                    processed_message = processed_message.replace("{server}", member.guild.name)
                    processed_message = processed_message.replace("{server.id}", str(member.guild.id))
                    processed_message = processed_message.replace("{count}", str(member.guild.member_count))

                    # Parse the message
                    parsed = builder.parse_embed_string(processed_message)

                    try:
                        if parsed['embed']:
                            # Send custom embed
                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - create basic embed
                            embed_obj = discord.Embed(
                                description=parsed['content'] or processed_message,
                                color=Colors.success
                            )
                            embed_obj.set_thumbnail(url=member.display_avatar.url)

                            await channel.send(embed=embed_obj, view=parsed['view'])
                    except discord.Forbidden:
                        print(f"Failed to send welcome message to {member.guild.name}: Missing permissions in {channel.name}")
                    except Exception as e:
                        print(f"Failed to send welcome message to {member.guild.name}: {e}")

        except Exception as e:
            print(f"Welcome system error: {e}")

    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Called when a member leaves a guild"""
        # Check for goodbye messages
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT channel_id, message FROM goodbye_messages WHERE guild_id = %s",
                        (member.guild.id,)
                    )
                    goodbye_messages = await cursor.fetchall()

            for channel_id, message in goodbye_messages:
                channel = self.bot.get_channel(channel_id)
                if channel:
                    # Check if bot has permission to send messages
                    if not channel.permissions_for(member.guild.me).send_messages:
                        print(f"Missing send_messages permission in goodbye channel {channel.name} ({channel.id}) for guild {member.guild.name}")
                        continue

                    # Create a mock context for variable replacement
                    class MockContext:
                        def __init__(self, author, guild, channel):
                            self.author = author
                            self.guild = guild
                            self.channel = channel

                    mock_ctx = MockContext(member, member.guild, channel)
                    builder = EmbedBuilder(mock_ctx)

                    # Replace variables manually for goodbye system
                    processed_message = message.replace("{user}", str(member))  # Use str() for goodbye, not mention
                    processed_message = processed_message.replace("{user.mention}", member.mention)
                    processed_message = processed_message.replace("{user.name}", member.display_name)
                    processed_message = processed_message.replace("{user.id}", str(member.id))
                    processed_message = processed_message.replace("{server}", member.guild.name)
                    processed_message = processed_message.replace("{server.id}", str(member.guild.id))
                    processed_message = processed_message.replace("{count}", str(member.guild.member_count))

                    # Parse the message
                    parsed = builder.parse_embed_string(processed_message)

                    try:
                        if parsed['embed']:
                            # Send custom embed
                            await channel.send(
                                content=parsed['content'],
                                embed=parsed['embed'],
                                view=parsed['view']
                            )
                        else:
                            # Simple message - create basic embed
                            embed_obj = discord.Embed(
                                description=parsed['content'] or processed_message,
                                color=Colors.error
                            )
                            embed_obj.set_thumbnail(url=member.display_avatar.url)

                            await channel.send(embed=embed_obj, view=parsed['view'])
                    except discord.Forbidden:
                        print(f"Failed to send goodbye message to {member.guild.name}: Missing permissions in {channel.name}")
                    except Exception as e:
                        print(f"Failed to send goodbye message to {member.guild.name}: {e}")

        except Exception as e:
            print(f"Goodbye system error: {e}")


async def setup(bot):
    await bot.add_cog(Member(bot))
