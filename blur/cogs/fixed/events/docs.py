import discord
from discord.ext import commands
from config.constants import Colors


class DocsEvents(commands.Cog):

    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_guild_join(self, guild):
        """Send welcome message only to whitelisted servers"""

        # Check if server is whitelisted first
        try:
            async with self.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild.id,)
                    )
                    check = await cursor.fetchone()

            # Only send welcome message if server is whitelisted
            if not check:
                return  # Don't send welcome message to non-whitelisted servers

        except Exception as e:
            print(f"Error checking whitelist in docs.py: {e}")
            return

        print(f"✅ Joined whitelisted guild: {guild.name} ({guild.id}) with {guild.member_count} members")

        # Try to send a welcome message
        try:
            # Find a suitable channel to send welcome message
            channel = None

            # Try to find general channel
            for ch in guild.text_channels:
                if ch.name.lower() in ['general', 'chat', 'main']:
                    if ch.permissions_for(guild.me).send_messages:
                        channel = ch
                        break

            # If no general channel, find first channel we can send to
            if not channel:
                for ch in guild.text_channels:
                    if ch.permissions_for(guild.me).send_messages:
                        channel = ch
                        break

            if channel:
                embed = discord.Embed(
                    title="Getting started with blur",
                    description="Hey! Thanks for your interest in **blur bot**. The following will provide you with some tips on how to get started with your server!",
                    color=Colors.default
                )

                embed.set_thumbnail(url=self.bot.user.display_avatar.url)

                embed.add_field(
                    name="🤖 **Prefix**",
                    value="The most important thing is my prefix. It is set to `,` by default for this server and it is also customizable, so if you don't like this prefix, you can always change it with `prefix set` command!",
                    inline=False
                )

                embed.add_field(
                    name="🛡️ **Moderation System**",
                    value="If you would like to use moderation commands, such as `jail`, `ban`, `kick` and so much more... please run the `setme` command to quickly set up the moderation system.",
                    inline=False
                )

                embed.add_field(
                    name="📚 **Help & report**",
                    value="You can always visit our [support server](https://discord.gg/svbbAbgpPK) for extra assistance!",
                    inline=False
                )

                await channel.send(embed=embed)

        except Exception as e:
            print(f"Failed to send welcome message to {guild.name}: {e}")

    @commands.Cog.listener()
    async def on_guild_remove(self, guild):
        """Called when bot leaves a guild"""
        print(f"❌ Left guild: {guild.name} ({guild.id})")

    @commands.Cog.listener()
    async def on_ready(self):
        """Called when bot is fully ready"""
        print(f"🎯 Docs loaded for {self.bot.user}")


async def setup(bot):
    await bot.add_cog(DocsEvents(bot))
