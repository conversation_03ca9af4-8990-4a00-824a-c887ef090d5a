import discord, json
from discord.ext import commands
from cogs.old.auth import owners


class Boosts:
    def get_level(boosts: int):
        async def predicate(ctx: commands.Context):
            if ctx.guild.premium_subscription_count < boosts:
                await ctx.send_warning(
                    f"This server needs to have more than **{boosts}** boosts in order to use this command"
                )
            return ctx.guild.premium_subscription_count >= boosts

        return commands.check(predicate)


class Mod:
    def is_mod_configured():
        async def predicate(ctx: commands.Context):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM `mod` WHERE guild_id = %s", (ctx.guild.id,)
                    )
                    check = await cursor.fetchone()
            if not check:
                await ctx.send_warning(
                    f"Jail isn't **enabled** in this server. Enable it using `{ctx.clean_prefix}setme` command"
                )
                return False
            return True

        return commands.check(predicate)

    async def check_hieracy(ctx: commands.Context, member: discord.Member) -> bool:
        if member.id == ctx.bot.user.id:
            if ctx.command.name != "nickname":
                await ctx.reply("im invincible lol")
                return False
        if (
            (
                ctx.author.top_role.position <= member.top_role.position
                and ctx.guild.owner_id != ctx.author.id
            )
            or ctx.guild.me.top_role <= member.top_role
            or (member.id == ctx.guild.owner_id and ctx.author.id != member.id)
        ):
            await ctx.send_warning("I cannot do this action on **{}**".format(member))
            return False
        return True


class Joint:
    def check_joint():
        async def predicate(ctx: commands.Context):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM joint WHERE guild_id = %s", (ctx.guild.id,)
                    )
                    check = await cursor.fetchone()
            if not check:
                await ctx.bot.ext.send_error(
                    ctx,
                    f"This server **doesn't** have a **joint**. Use `{ctx.clean_prefix}joint toggle` to get one",
                )
            return check is not None

        return commands.check(predicate)

    def joint_owner():
        async def predicate(ctx: commands.Context):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM joint WHERE guild_id = %s", (ctx.guild.id,)
                    )
                    check = await cursor.fetchone()
            if check[1] != ctx.author.id:  # holder is second column
                await ctx.send_warning(
                    f"You don't have the **joint**. Steal it from <@{check[1]}>"
                )
            return check[1] == ctx.author.id

        return commands.check(predicate)


class Perms:
    @staticmethod
    def get_perms(permission: str):
        async def predicate(ctx: commands.Context):
            try:
                if ctx.guild is None:
                    return False
                if ctx.author.id == ctx.guild.owner_id:
                    return True
                if ctx.author.guild_permissions.administrator:
                    return True
                if getattr(ctx.author.guild_permissions, permission) is True:
                    return True
                await ctx.send_warning(
                    f"You are missing the **{permission.replace('_', ' ')}** permission"
                )
                return False
            except Exception as e:
                print(f"Permission check error for {permission}: {e}")
                return False

        return commands.check(predicate)

    @staticmethod
    def server_owner():
        async def predicate(ctx: commands.Context):
            if ctx.author.id != ctx.guild.owner_id:
                await ctx.send_warning("This command can only be used by the **server owner**")
                return False
            return True

        return commands.check(predicate)


class Owners:
    @staticmethod
    def check_owners():
        async def predicate(ctx: commands.Context):
            if ctx.author.id not in owners:
                await ctx.send_warning("This command can only be used by **bot owners**")
                return False
            return True

        return commands.check(predicate)


class Donor:
    @staticmethod
    def check_donor():
        async def predicate(ctx: commands.Context):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (ctx.author.id,)
                    )
                    check = await cursor.fetchone()
            if not check:
                await ctx.send_warning("This command can only be used by **donors**")
                return False
            return True

        return commands.check(predicate)


class Blacklist:
    @staticmethod
    def check_blacklist():
        async def predicate(ctx: commands.Context):
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM blacklist WHERE user_id = %s", (ctx.author.id,)
                    )
                    check = await cursor.fetchone()
            if check:
                return False
            return True

        return commands.check(predicate)
