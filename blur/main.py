import discord
from discord.ext import commands
import asyncio
import aiomysql
import time
import aiohttp
from tools.utils import StartUp
from tools.ext import Client
from config.constants import Emojis, Colors
from cogs.old.auth import BOT_TOKEN, DEFAULT_PREFIX, BOT_COLOR, DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME


class WhitelistView(discord.ui.View):
    """View with link button for whitelist message"""

    def __init__(self):
        super().__init__(timeout=None)

        # Add link button that directly opens Discord invite
        self.add_item(discord.ui.Button(
            label="blur server",
            style=discord.ButtonStyle.grey,
            url="https://discord.gg/svbbAbgpPK"
        ))


class CombinedBot(commands.AutoShardedBot):
    def __init__(self):
        intents = discord.Intents.all()
        super().__init__(
            command_prefix=self.get_prefix,
            intents=intents,
            help_command=None,
            case_insensitive=True,
            strip_after_prefix=True,
            allowed_mentions=discord.AllowedMentions(
                roles=False, 
                everyone=False, 
                users=True
            )
        )
        
        # Bot attributes
        self.color = BOT_COLOR
        self.yes = Emojis.success
        self.no = Emojis.error
        self.warn = Emojis.warn
        self.uptime = time.time()
        self.ext = Client()
        self.cogs_loaded = False
        self.session = None
        
    async def get_prefix(self, message):
        """Get bot prefix for guild - allows both server prefix and selfprefix"""
        # Always include bot mention as a prefix
        prefixes = [f"<@{self.user.id}>", f"<@!{self.user.id}>"]

        if not message.guild:
            prefixes.append(DEFAULT_PREFIX)
            return prefixes

        try:
            async with self.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    # Check for user's personal prefix
                    await cursor.execute(
                        "SELECT prefix FROM selfprefixes WHERE user_id = %s",
                        (message.author.id,)
                    )
                    selfprefix_result = await cursor.fetchone()

                    # Check guild prefix
                    await cursor.execute(
                        "SELECT prefix FROM prefixes WHERE guild_id = %s",
                        (message.guild.id,)
                    )
                    guild_result = await cursor.fetchone()

                    # Add server prefix (or default if none set)
                    server_prefix = guild_result[0] if guild_result else DEFAULT_PREFIX
                    prefixes.append(server_prefix)

                    # Add selfprefix if user has one (and it's different from server prefix)
                    if selfprefix_result and selfprefix_result[0] != server_prefix:
                        prefixes.append(selfprefix_result[0])

                    return prefixes
        except:
            prefixes.append(DEFAULT_PREFIX)
            return prefixes
    
    async def setup_hook(self):
        """Setup hook called when bot starts"""
        print("🔄 Setting up bot...")
        
        # Create aiohttp session
        self.session = aiohttp.ClientSession()
        
        # Connect to database
        try:
            self.db = await aiomysql.create_pool(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                password=DB_PASSWORD,
                db=DB_NAME,
                autocommit=True
            )
            print("✅ Connected to MySQL database")

            # Create tables if they don't exist
            await self.create_tables()

        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return
        
        # Load cogs
        await StartUp.loadcogs(self)
        
        print("✅ Bot setup complete!")
    
    async def create_tables(self):
        """Create database tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS prefixes (
                guild_id BIGINT PRIMARY KEY,
                prefix VARCHAR(10) NOT NULL DEFAULT ';'
            )""",
            """CREATE TABLE IF NOT EXISTS selfprefixes (
                user_id BIGINT PRIMARY KEY,
                prefix VARCHAR(10) NOT NULL
            )""",
            """CREATE TABLE IF NOT EXISTS `mod` (
                guild_id BIGINT PRIMARY KEY,
                channel_id BIGINT,
                jail_id BIGINT,
                role_id BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS cases (
                guild_id BIGINT PRIMARY KEY,
                count INT DEFAULT 0
            )""",
            """CREATE TABLE IF NOT EXISTS jailed_users (
                guild_id BIGINT,
                user_id BIGINT,
                roles TEXT,
                PRIMARY KEY (guild_id, user_id)
            )""",
            """CREATE TABLE IF NOT EXISTS donor (
                user_id BIGINT PRIMARY KEY,
                time INTEGER
            )""",
            """CREATE TABLE IF NOT EXISTS blacklist (
                user_id BIGINT PRIMARY KEY,
                reason TEXT
            )""",
            """CREATE TABLE IF NOT EXISTS server_whitelist (
                guild_id BIGINT PRIMARY KEY,
                added_at INTEGER
            )""",
            """CREATE TABLE IF NOT EXISTS welcome_config (
                guild_id BIGINT PRIMARY KEY,
                enabled BOOLEAN DEFAULT FALSE,
                channel_id BIGINT,
                message TEXT,
                embed BOOLEAN DEFAULT TRUE
            )""",
            """CREATE TABLE IF NOT EXISTS goodbye_config (
                guild_id BIGINT PRIMARY KEY,
                enabled BOOLEAN DEFAULT FALSE,
                channel_id BIGINT,
                message TEXT,
                embed BOOLEAN DEFAULT TRUE
            )""",
            """CREATE TABLE IF NOT EXISTS welcome_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                channel_id BIGINT,
                message TEXT,
                embed_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_guild (guild_id)
            )""",
            """CREATE TABLE IF NOT EXISTS goodbye_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                channel_id BIGINT,
                message TEXT,
                embed_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_guild (guild_id)
            )""",
            """CREATE TABLE IF NOT EXISTS boost_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                channel_id BIGINT,
                message TEXT,
                embed_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_guild (guild_id)
            )""",
            """CREATE TABLE IF NOT EXISTS guild_config (
                guild_id BIGINT PRIMARY KEY,
                log_channel BIGINT,
                mute_role BIGINT,
                autorole BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS snipe_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                channel_id BIGINT,
                author_id BIGINT,
                content TEXT,
                deleted_at TIMESTAMP,
                INDEX idx_channel_deleted (channel_id, deleted_at)
            )""",
            """CREATE TABLE IF NOT EXISTS editsnipe_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                channel_id BIGINT,
                message_id BIGINT,
                author_id BIGINT,
                before_content TEXT,
                after_content TEXT,
                edited_at TIMESTAMP,
                INDEX idx_channel_edited (channel_id, edited_at)
            )""",
            """CREATE TABLE IF NOT EXISTS command_usage (
                id INT AUTO_INCREMENT PRIMARY KEY,
                command_name VARCHAR(50),
                user_id BIGINT,
                guild_id BIGINT,
                used_at TIMESTAMP
            )""",
            """CREATE TABLE IF NOT EXISTS antinuke_toggle (
                guild_id BIGINT PRIMARY KEY
            )""",
            """CREATE TABLE IF NOT EXISTS antinuke (
                guild_id BIGINT,
                module VARCHAR(50),
                punishment VARCHAR(20),
                PRIMARY KEY (guild_id, module)
            )""",
            """CREATE TABLE IF NOT EXISTS antispam (
                guild_id BIGINT PRIMARY KEY,
                punishment VARCHAR(20) DEFAULT 'delete',
                seconds INT DEFAULT 5
            )""",
            """CREATE TABLE IF NOT EXISTS automod_config (
                guild_id BIGINT,
                module VARCHAR(50),
                enabled BOOLEAN DEFAULT FALSE,
                PRIMARY KEY (guild_id, module)
            )""",
            """CREATE TABLE IF NOT EXISTS chatfilter (
                guild_id BIGINT,
                word VARCHAR(100),
                PRIMARY KEY (guild_id, word)
            )""",
            """CREATE TABLE IF NOT EXISTS economy (
                user_id BIGINT PRIMARY KEY,
                cash DECIMAL(15,2) DEFAULT 100.00,
                bank DECIMAL(15,2) DEFAULT 0.00,
                rob INT DEFAULT 0,
                daily INT DEFAULT 0,
                weekly INT DEFAULT 0,
                last_work INT DEFAULT 0
            )""",
            """CREATE TABLE IF NOT EXISTS levels (
                guild_id BIGINT,
                user_id BIGINT,
                xp INT DEFAULT 0,
                level INT DEFAULT 0,
                PRIMARY KEY (guild_id, user_id)
            )""",
            """CREATE TABLE IF NOT EXISTS leveling_config (
                guild_id BIGINT PRIMARY KEY,
                enabled BOOLEAN DEFAULT FALSE,
                channel_id BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS level_roles (
                guild_id BIGINT,
                level INT,
                role_id BIGINT,
                PRIMARY KEY (guild_id, level)
            )""",
            # Voice Master tables
            """CREATE TABLE IF NOT EXISTS voicemaster (
                guild_id BIGINT PRIMARY KEY,
                channel_id BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS voicemaster_config (
                guild_id BIGINT PRIMARY KEY,
                vm_role_id BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS temp_voice (
                guild_id BIGINT,
                channel_id BIGINT,
                owner_id BIGINT,
                status TEXT DEFAULT NULL,
                PRIMARY KEY (guild_id, channel_id)
            )""",
            # Ticket tables
            """CREATE TABLE IF NOT EXISTS ticket_config (
                guild_id BIGINT PRIMARY KEY,
                category_id BIGINT,
                support_role_id BIGINT
            )""",
            """CREATE TABLE IF NOT EXISTS tickets (
                guild_id BIGINT,
                user_id BIGINT,
                channel_id BIGINT,
                status VARCHAR(20) DEFAULT 'open',
                PRIMARY KEY (guild_id, user_id)
            )""",
            # Reaction Role tables
            """CREATE TABLE IF NOT EXISTS reaction_roles (
                guild_id BIGINT,
                message_id BIGINT,
                emoji VARCHAR(100),
                role_id BIGINT,
                PRIMARY KEY (guild_id, message_id, emoji)
            )""",
            # Booster tables
            """CREATE TABLE IF NOT EXISTS booster_config (
                guild_id BIGINT PRIMARY KEY,
                channel_id BIGINT,
                booster_role_id BIGINT,
                boost_message TEXT
            )""",
            """CREATE TABLE IF NOT EXISTS booster_perks (
                guild_id BIGINT,
                perk_type VARCHAR(50),
                perk_value BIGINT,
                PRIMARY KEY (guild_id, perk_type, perk_value)
            )""",
            # AutoPFP tables
            """CREATE TABLE IF NOT EXISTS autopfp_config (
                guild_id BIGINT PRIMARY KEY,
                enabled BOOLEAN DEFAULT FALSE,
                interval_hours INT DEFAULT 24,
                last_change INT DEFAULT 0
            )""",
            """CREATE TABLE IF NOT EXISTS autopfp_images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                image_url TEXT
            )""",
            # AutoResponder tables
            """CREATE TABLE IF NOT EXISTS autoresponders (
                guild_id BIGINT,
                `trigger` VARCHAR(255),
                response TEXT,
                match_type VARCHAR(20) DEFAULT 'contains',
                delete_trigger BOOLEAN DEFAULT FALSE,
                PRIMARY KEY (guild_id, `trigger`)
            )""",
            """CREATE TABLE IF NOT EXISTS autoreacts (
                guild_id BIGINT,
                `trigger` VARCHAR(255),
                emoji VARCHAR(100),
                PRIMARY KEY (guild_id, `trigger`)
            )""",
            # Ping on Join table (enhanced with custom text and deletion timing)
            """CREATE TABLE IF NOT EXISTS ping_on_join_channels (
                guild_id BIGINT,
                channel_id BIGINT,
                custom_text TEXT DEFAULT NULL,
                delete_seconds INT DEFAULT 5,
                PRIMARY KEY (guild_id, channel_id)
            )""",
            # Giveaway tables
            """CREATE TABLE IF NOT EXISTS giveaway (
                guild_id BIGINT,
                channel_id BIGINT,
                message_id BIGINT,
                winners INT,
                members TEXT,
                finish_time DATETIME,
                host BIGINT,
                title TEXT,
                PRIMARY KEY (guild_id, message_id)
            )""",
            """CREATE TABLE IF NOT EXISTS gw_ended (
                channel_id BIGINT,
                message_id BIGINT,
                members TEXT,
                PRIMARY KEY (channel_id, message_id)
            )""",
            # Poll tables
            """CREATE TABLE IF NOT EXISTS polls (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                channel_id BIGINT,
                message_id BIGINT,
                creator_id BIGINT,
                question TEXT,
                options TEXT,
                created_at DATETIME,
                active BOOLEAN DEFAULT TRUE
            )""",
            """CREATE TABLE IF NOT EXISTS poll_votes (
                poll_id INT,
                user_id BIGINT,
                option_index INT,
                PRIMARY KEY (poll_id, user_id)
            )""",
            # Starboard tables
            """CREATE TABLE IF NOT EXISTS starboard_config (
                guild_id BIGINT PRIMARY KEY,
                channel_id BIGINT,
                enabled BOOLEAN DEFAULT FALSE,
                emoji VARCHAR(50) DEFAULT '⭐',
                threshold INT DEFAULT 3
            )""",
            """CREATE TABLE IF NOT EXISTS starboard_messages (
                guild_id BIGINT,
                original_message_id BIGINT,
                starboard_message_id BIGINT,
                star_count INT,
                PRIMARY KEY (guild_id, original_message_id)
            )""",
            # Reminder tables
            """CREATE TABLE IF NOT EXISTS reminders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT,
                guild_id BIGINT,
                channel_id BIGINT,
                message TEXT,
                remind_time DATETIME,
                created_at DATETIME,
                sent BOOLEAN DEFAULT FALSE
            )""",
            # Button Role tables
            """CREATE TABLE IF NOT EXISTS button_role_panels (
                guild_id BIGINT,
                message_id BIGINT,
                title VARCHAR(256),
                description TEXT,
                PRIMARY KEY (guild_id, message_id)
            )""",
            """CREATE TABLE IF NOT EXISTS button_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id BIGINT,
                message_id BIGINT,
                role_id BIGINT,
                label VARCHAR(80),
                emoji VARCHAR(100),
                style VARCHAR(20) DEFAULT 'primary'
            )""",
            # Comprehensive logging system
            """CREATE TABLE IF NOT EXISTS logging_config (
                guild_id BIGINT,
                log_type VARCHAR(20),
                channel_id BIGINT,
                PRIMARY KEY (guild_id, log_type)
            )""",
            """CREATE TABLE IF NOT EXISTS invite_tracking (
                guild_id BIGINT,
                invite_code VARCHAR(20),
                inviter_id BIGINT,
                uses_count INT DEFAULT 0,
                PRIMARY KEY (guild_id, invite_code)
            )""",
            """CREATE TABLE IF NOT EXISTS invite_stats (
                guild_id BIGINT,
                user_id BIGINT,
                total_invites INT DEFAULT 0,
                fake_invites INT DEFAULT 0,
                left_invites INT DEFAULT 0,
                PRIMARY KEY (guild_id, user_id)
            )""",
            """CREATE TABLE IF NOT EXISTS member_invites (
                guild_id BIGINT,
                user_id BIGINT,
                inviter_id BIGINT,
                invite_code VARCHAR(20),
                joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (guild_id, user_id)
            )""",
            """CREATE TABLE IF NOT EXISTS jailed_users (
                guild_id BIGINT,
                user_id BIGINT,
                roles TEXT,
                PRIMARY KEY (guild_id, user_id)
            )""",
            # Birthday table
            """CREATE TABLE IF NOT EXISTS birthday (
                user_id BIGINT PRIMARY KEY,
                bday DATE,
                state VARCHAR(10) DEFAULT 'false'
            )""",
            # Invoke table (for moderation logging)
            """CREATE TABLE IF NOT EXISTS invoke (
                guild_id BIGINT PRIMARY KEY,
                channel_id BIGINT
            )"""
        ]
        
        async with self.db.acquire() as conn:
            async with conn.cursor() as cursor:
                for table in tables:
                    try:
                        await cursor.execute(table)
                        print(f"✅ Created table")
                    except Exception as e:
                        print(f"❌ Failed to create table: {e}")

                # Migration: Add new columns to existing ping_on_join_channels table
                try:
                    await cursor.execute("ALTER TABLE ping_on_join_channels ADD COLUMN custom_text TEXT DEFAULT NULL")
                    print("✅ Added custom_text column to ping_on_join_channels")
                except Exception:
                    pass  # Column already exists

                try:
                    await cursor.execute("ALTER TABLE ping_on_join_channels ADD COLUMN delete_seconds INT DEFAULT 5")
                    print("✅ Added delete_seconds column to ping_on_join_channels")
                except Exception:
                    pass  # Column already exists

                # Migration: Update snipe tables for multiple message support
                try:
                    # Check if snipe_messages needs migration
                    await cursor.execute("SHOW COLUMNS FROM snipe_messages LIKE 'id'")
                    id_exists = await cursor.fetchone()

                    if not id_exists:
                        print("🔄 Migrating snipe_messages table...")
                        # Backup existing data
                        await cursor.execute("CREATE TABLE snipe_messages_backup AS SELECT * FROM snipe_messages")
                        # Drop old table
                        await cursor.execute("DROP TABLE snipe_messages")
                        # Create new table with proper structure
                        await cursor.execute("""CREATE TABLE snipe_messages (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            channel_id BIGINT,
                            author_id BIGINT,
                            content TEXT,
                            deleted_at TIMESTAMP,
                            INDEX idx_channel_deleted (channel_id, deleted_at)
                        )""")
                        # Restore data
                        await cursor.execute("""INSERT INTO snipe_messages (channel_id, author_id, content, deleted_at)
                                               SELECT channel_id, author_id, content, deleted_at FROM snipe_messages_backup""")
                        # Drop backup
                        await cursor.execute("DROP TABLE snipe_messages_backup")
                        print("✅ Migrated snipe_messages table")
                except Exception as e:
                    print(f"⚠️  Snipe messages migration error: {e}")

                try:
                    # Check if editsnipe_messages needs migration
                    await cursor.execute("SHOW COLUMNS FROM editsnipe_messages LIKE 'id'")
                    id_exists = await cursor.fetchone()

                    if not id_exists:
                        print("🔄 Migrating editsnipe_messages table...")
                        # Backup existing data
                        await cursor.execute("CREATE TABLE editsnipe_messages_backup AS SELECT * FROM editsnipe_messages")
                        # Drop old table
                        await cursor.execute("DROP TABLE editsnipe_messages")
                        # Create new table with proper structure
                        await cursor.execute("""CREATE TABLE editsnipe_messages (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            channel_id BIGINT,
                            message_id BIGINT DEFAULT 0,
                            author_id BIGINT,
                            before_content TEXT,
                            after_content TEXT,
                            edited_at TIMESTAMP,
                            INDEX idx_channel_edited (channel_id, edited_at)
                        )""")
                        # Restore data
                        await cursor.execute("""INSERT INTO editsnipe_messages (channel_id, author_id, before_content, after_content, edited_at)
                                               SELECT channel_id, author_id, before_content, after_content, edited_at FROM editsnipe_messages_backup""")
                        # Drop backup
                        await cursor.execute("DROP TABLE editsnipe_messages_backup")
                        print("✅ Migrated editsnipe_messages table")
                except Exception as e:
                    print(f"⚠️  Edit snipe messages migration error: {e}")
    
    @property
    def ping(self):
        return round(self.latency * 1000)
    
    async def on_ready(self):

        print(f"🤖 {self.user} is online!")
        print(f"📊 Connected to {len(self.guilds)} servers")

        # Set bot status
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.playing,
                name="blur bot | ,help"
            )
        )

        try:
            async with self.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT guild_id FROM server_whitelist")
                    whitelisted = {row[0] for row in await cursor.fetchall()}

            left_count = 0
            guilds_to_check = list(self.guilds)
            for guild in guilds_to_check:
                if guild.id not in whitelisted:
                    try:
                        print(f"❌ Leaving non-whitelisted server: {guild.name} ({guild.id})")
                        await guild.leave()
                        left_count += 1
                    except Exception as e:
                        print(f"Error leaving {guild.name}: {e}")

            if left_count > 0:
                print(f"🚫 Left {left_count} non-whitelisted servers")

            print(f"✅ Remaining in {len(self.guilds)} whitelisted servers")

        except Exception as e:
            print(f"Error checking server whitelist: {e}")
    
    async def on_guild_join(self, guild):
        """Check if server is whitelisted when bot joins"""
        try:
            async with self.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM server_whitelist WHERE guild_id = %s", (guild.id,)
                    )
                    check = await cursor.fetchone()

            if not check:
                # Send whitelist message before leaving
                await self.send_whitelist_message(guild)
                print(f"❌ Left non-whitelisted server: {guild.name} ({guild.id}) with {guild.member_count} members")
                await guild.leave()

        except Exception as e:
            print(f"Error checking whitelist for {guild.name}: {e}")

    async def send_whitelist_message(self, guild):
        """Send whitelist embed message to the server"""
        try:
            # Find a suitable channel to send the message
            channel = None

            # Try to find general channel first
            for ch in guild.text_channels:
                if ch.name.lower() in ['general', 'chat', 'main', 'welcome']:
                    if ch.permissions_for(guild.me).send_messages:
                        channel = ch
                        break

            # If no general channel, find first channel we can send to
            if not channel:
                for ch in guild.text_channels:
                    if ch.permissions_for(guild.me).send_messages:
                        channel = ch
                        break

            if channel:
                # Create whitelist embed
                embed = discord.Embed(
                    title="Your server is not whitelisted",
                    description="> Please whitelist your server from the official blur server to invite the bot, whitelisting is completely free.",
                    color=Colors.error
                )
                embed.set_thumbnail(url=self.user.display_avatar.url)

                # Create view with button
                view = WhitelistView()

                await channel.send(embed=embed, view=view)

        except Exception as e:
            print(f"Failed to send whitelist message to {guild.name}: {e}")



    async def on_message_edit(self, before, after):
        """Handle message edits and process commands if edited message becomes a command"""
        # Skip if author is bot or content didn't change
        if before.author.bot or before.content == after.content:
            return

        # Process commands on edited messages
        await self.process_commands(after)

    async def on_command_error(self, ctx, error):
        """Global error handler"""
        if isinstance(error, commands.CommandNotFound):
            return

        if isinstance(error, commands.CheckFailure):
            # Check failures are already handled by the check functions themselves
            # (they send their own warning messages), so we don't need to send another error
            return

        if isinstance(error, commands.MissingPermissions):
            return await ctx.send_error("You don't have permission to use this command!")

        if isinstance(error, commands.BotMissingPermissions):
            return await ctx.send_error("I don't have permission to do that!")

        if isinstance(error, commands.MissingRequiredArgument):
            return await ctx.send_error(f"Missing required argument: `{error.param.name}`")

        if isinstance(error, commands.BadArgument):
            return await ctx.send_error(str(error))

        if isinstance(error, commands.CommandOnCooldown):
            # Use cooldown emoji for cooldown errors
            embed = discord.Embed(
                color=Colors.cooldown,
                description=f"{Emojis.cooldown} {ctx.author.mention}: Command on cooldown! Try again in {error.retry_after:.2f}s"
            )
            return await ctx.reply(embed=embed)

        # Log unexpected errors
        print(f"Unexpected error in {ctx.command}: {error}")
        await ctx.send_error("An unexpected error occurred!")
    
    async def close(self):
        """Close bot and cleanup"""
        if self.session:
            await self.session.close()
        if hasattr(self, 'db') and self.db:
            self.db.close()
            await self.db.wait_closed()
        await super().close()


# Add context methods
async def send_success(self, message: str):
    """Send success message"""
    embed = discord.Embed(
        color=Colors.success,
        description=f"{Emojis.success} {self.author.mention}: {message}"
    )
    return await self.reply(embed=embed, mention_author=False)

async def send_warning(self, message: str):
    """Send warning message"""
    embed = discord.Embed(
        color=Colors.warn,
        description=f"{Emojis.warn} {self.author.mention}: {message}"
    )
    return await self.reply(embed=embed, mention_author=False)

async def send_error(self, message: str):
    """Send error message"""
    embed = discord.Embed(
        color=Colors.error,
        description=f"{Emojis.error} {self.author.mention}: {message}"
    )
    return await self.reply(embed=embed, mention_author=False)

async def paginator(self, embeds: list):
    """Send paginated embeds"""
    from tools.utils import Paginator
    if len(embeds) == 1:
        return await self.reply(embed=embeds[0])

    view = Paginator(self, embeds)
    message = await self.reply(embed=embeds[0], view=view)
    view.message = message
    return message

async def create_pages(self):
    """Create help pages - placeholder for compatibility"""
    embed = discord.Embed(
        title="Help",
        description="Use the help command to see available commands.",
        color=Colors.blurple
    )
    return await self.reply(embed=embed)

# Add methods to Context
commands.Context.send_success = send_success
commands.Context.send_warning = send_warning
commands.Context.send_error = send_error
commands.Context.paginator = paginator
commands.Context.create_pages = create_pages


async def main():
    """Main function to run the bot"""
    bot = CombinedBot()
    
    try:
        await bot.start(BOT_TOKEN)
    except KeyboardInterrupt:
        print("🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Bot error: {e}")
    finally:
        await bot.close()


if __name__ == "__main__":
    asyncio.run(main())
