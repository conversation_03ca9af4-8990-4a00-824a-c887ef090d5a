name: Auto Deploy to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: SSH and Deploy
        run: |
          sshpass -p "iPhone@12321" ssh -o StrictHostKeyChecking=no administrator@*********** << 'EOF'
            cd ~/blury
            git reset --hard
            git pull origin main
            pip3 install -r blur/requirements.txt --user
            pm2 restart blury || pm2 start blur/main.py --name blury --interpreter python3
            pm2 save
          EOF
